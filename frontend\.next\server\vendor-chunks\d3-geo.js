"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-geo";
exports.ids = ["vendor-chunks/d3-geo"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-geo/src/area.js":
/*!*****************************************!*\
  !*** ./node_modules/d3-geo/src/area.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   areaRingSum: () => (/* binding */ areaRingSum),\n/* harmony export */   areaStream: () => (/* binding */ areaStream),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./noop.js */ \"(ssr)/./node_modules/d3-geo/src/noop.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stream.js */ \"(ssr)/./node_modules/d3-geo/src/stream.js\");\n\n\n\n\n\nvar areaRingSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n\n// hello?\n\nvar areaSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder(),\n    lambda00,\n    phi00,\n    lambda0,\n    cosPhi0,\n    sinPhi0;\n\nvar areaStream = {\n  point: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  lineStart: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  lineEnd: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  polygonStart: function() {\n    areaRingSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n    areaStream.lineStart = areaRingStart;\n    areaStream.lineEnd = areaRingEnd;\n  },\n  polygonEnd: function() {\n    var areaRing = +areaRingSum;\n    areaSum.add(areaRing < 0 ? _math_js__WEBPACK_IMPORTED_MODULE_2__.tau + areaRing : areaRing);\n    this.lineStart = this.lineEnd = this.point = _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n  },\n  sphere: function() {\n    areaSum.add(_math_js__WEBPACK_IMPORTED_MODULE_2__.tau);\n  }\n};\n\nfunction areaRingStart() {\n  areaStream.point = areaPointFirst;\n}\n\nfunction areaRingEnd() {\n  areaPoint(lambda00, phi00);\n}\n\nfunction areaPointFirst(lambda, phi) {\n  areaStream.point = areaPoint;\n  lambda00 = lambda, phi00 = phi;\n  lambda *= _math_js__WEBPACK_IMPORTED_MODULE_2__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_2__.radians;\n  lambda0 = lambda, cosPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.cos)(phi = phi / 2 + _math_js__WEBPACK_IMPORTED_MODULE_2__.quarterPi), sinPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.sin)(phi);\n}\n\nfunction areaPoint(lambda, phi) {\n  lambda *= _math_js__WEBPACK_IMPORTED_MODULE_2__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_2__.radians;\n  phi = phi / 2 + _math_js__WEBPACK_IMPORTED_MODULE_2__.quarterPi; // half the angular distance from south pole\n\n  // Spherical excess E for a spherical triangle with vertices: south pole,\n  // previous point, current point.  Uses a formula derived from Cagnoli’s\n  // theorem.  See Todhunter, Spherical Trig. (1871), Sec. 103, Eq. (2).\n  var dLambda = lambda - lambda0,\n      sdLambda = dLambda >= 0 ? 1 : -1,\n      adLambda = sdLambda * dLambda,\n      cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.cos)(phi),\n      sinPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.sin)(phi),\n      k = sinPhi0 * sinPhi,\n      u = cosPhi0 * cosPhi + k * (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.cos)(adLambda),\n      v = k * sdLambda * (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.sin)(adLambda);\n  areaRingSum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_2__.atan2)(v, u));\n\n  // Advance the previous points.\n  lambda0 = lambda, cosPhi0 = cosPhi, sinPhi0 = sinPhi;\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(object) {\n  areaSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n  (0,_stream_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(object, areaStream);\n  return areaSum * 2;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/area.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/bounds.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-geo/src/bounds.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./area.js */ \"(ssr)/./node_modules/d3-geo/src/area.js\");\n/* harmony import */ var _cartesian_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./cartesian.js */ \"(ssr)/./node_modules/d3-geo/src/cartesian.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./stream.js */ \"(ssr)/./node_modules/d3-geo/src/stream.js\");\n\n\n\n\n\n\nvar lambda0, phi0, lambda1, phi1, // bounds\n    lambda2, // previous lambda-coordinate\n    lambda00, phi00, // first point\n    p0, // previous 3D point\n    deltaSum,\n    ranges,\n    range;\n\nvar boundsStream = {\n  point: boundsPoint,\n  lineStart: boundsLineStart,\n  lineEnd: boundsLineEnd,\n  polygonStart: function() {\n    boundsStream.point = boundsRingPoint;\n    boundsStream.lineStart = boundsRingStart;\n    boundsStream.lineEnd = boundsRingEnd;\n    deltaSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n    _area_js__WEBPACK_IMPORTED_MODULE_1__.areaStream.polygonStart();\n  },\n  polygonEnd: function() {\n    _area_js__WEBPACK_IMPORTED_MODULE_1__.areaStream.polygonEnd();\n    boundsStream.point = boundsPoint;\n    boundsStream.lineStart = boundsLineStart;\n    boundsStream.lineEnd = boundsLineEnd;\n    if (_area_js__WEBPACK_IMPORTED_MODULE_1__.areaRingSum < 0) lambda0 = -(lambda1 = 180), phi0 = -(phi1 = 90);\n    else if (deltaSum > _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon) phi1 = 90;\n    else if (deltaSum < -_math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon) phi0 = -90;\n    range[0] = lambda0, range[1] = lambda1;\n  },\n  sphere: function() {\n    lambda0 = -(lambda1 = 180), phi0 = -(phi1 = 90);\n  }\n};\n\nfunction boundsPoint(lambda, phi) {\n  ranges.push(range = [lambda0 = lambda, lambda1 = lambda]);\n  if (phi < phi0) phi0 = phi;\n  if (phi > phi1) phi1 = phi;\n}\n\nfunction linePoint(lambda, phi) {\n  var p = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesian)([lambda * _math_js__WEBPACK_IMPORTED_MODULE_2__.radians, phi * _math_js__WEBPACK_IMPORTED_MODULE_2__.radians]);\n  if (p0) {\n    var normal = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianCross)(p0, p),\n        equatorial = [normal[1], -normal[0], 0],\n        inflection = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianCross)(equatorial, normal);\n    (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianNormalizeInPlace)(inflection);\n    inflection = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.spherical)(inflection);\n    var delta = lambda - lambda2,\n        sign = delta > 0 ? 1 : -1,\n        lambdai = inflection[0] * _math_js__WEBPACK_IMPORTED_MODULE_2__.degrees * sign,\n        phii,\n        antimeridian = (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.abs)(delta) > 180;\n    if (antimeridian ^ (sign * lambda2 < lambdai && lambdai < sign * lambda)) {\n      phii = inflection[1] * _math_js__WEBPACK_IMPORTED_MODULE_2__.degrees;\n      if (phii > phi1) phi1 = phii;\n    } else if (lambdai = (lambdai + 360) % 360 - 180, antimeridian ^ (sign * lambda2 < lambdai && lambdai < sign * lambda)) {\n      phii = -inflection[1] * _math_js__WEBPACK_IMPORTED_MODULE_2__.degrees;\n      if (phii < phi0) phi0 = phii;\n    } else {\n      if (phi < phi0) phi0 = phi;\n      if (phi > phi1) phi1 = phi;\n    }\n    if (antimeridian) {\n      if (lambda < lambda2) {\n        if (angle(lambda0, lambda) > angle(lambda0, lambda1)) lambda1 = lambda;\n      } else {\n        if (angle(lambda, lambda1) > angle(lambda0, lambda1)) lambda0 = lambda;\n      }\n    } else {\n      if (lambda1 >= lambda0) {\n        if (lambda < lambda0) lambda0 = lambda;\n        if (lambda > lambda1) lambda1 = lambda;\n      } else {\n        if (lambda > lambda2) {\n          if (angle(lambda0, lambda) > angle(lambda0, lambda1)) lambda1 = lambda;\n        } else {\n          if (angle(lambda, lambda1) > angle(lambda0, lambda1)) lambda0 = lambda;\n        }\n      }\n    }\n  } else {\n    ranges.push(range = [lambda0 = lambda, lambda1 = lambda]);\n  }\n  if (phi < phi0) phi0 = phi;\n  if (phi > phi1) phi1 = phi;\n  p0 = p, lambda2 = lambda;\n}\n\nfunction boundsLineStart() {\n  boundsStream.point = linePoint;\n}\n\nfunction boundsLineEnd() {\n  range[0] = lambda0, range[1] = lambda1;\n  boundsStream.point = boundsPoint;\n  p0 = null;\n}\n\nfunction boundsRingPoint(lambda, phi) {\n  if (p0) {\n    var delta = lambda - lambda2;\n    deltaSum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_2__.abs)(delta) > 180 ? delta + (delta > 0 ? 360 : -360) : delta);\n  } else {\n    lambda00 = lambda, phi00 = phi;\n  }\n  _area_js__WEBPACK_IMPORTED_MODULE_1__.areaStream.point(lambda, phi);\n  linePoint(lambda, phi);\n}\n\nfunction boundsRingStart() {\n  _area_js__WEBPACK_IMPORTED_MODULE_1__.areaStream.lineStart();\n}\n\nfunction boundsRingEnd() {\n  boundsRingPoint(lambda00, phi00);\n  _area_js__WEBPACK_IMPORTED_MODULE_1__.areaStream.lineEnd();\n  if ((0,_math_js__WEBPACK_IMPORTED_MODULE_2__.abs)(deltaSum) > _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon) lambda0 = -(lambda1 = 180);\n  range[0] = lambda0, range[1] = lambda1;\n  p0 = null;\n}\n\n// Finds the left-right distance between two longitudes.\n// This is almost the same as (lambda1 - lambda0 + 360°) % 360°, except that we want\n// the distance between ±180° to be 360°.\nfunction angle(lambda0, lambda1) {\n  return (lambda1 -= lambda0) < 0 ? lambda1 + 360 : lambda1;\n}\n\nfunction rangeCompare(a, b) {\n  return a[0] - b[0];\n}\n\nfunction rangeContains(range, x) {\n  return range[0] <= range[1] ? range[0] <= x && x <= range[1] : x < range[0] || range[1] < x;\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(feature) {\n  var i, n, a, b, merged, deltaMax, delta;\n\n  phi1 = lambda1 = -(lambda0 = phi0 = Infinity);\n  ranges = [];\n  (0,_stream_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(feature, boundsStream);\n\n  // First, sort ranges by their minimum longitudes.\n  if (n = ranges.length) {\n    ranges.sort(rangeCompare);\n\n    // Then, merge any ranges that overlap.\n    for (i = 1, a = ranges[0], merged = [a]; i < n; ++i) {\n      b = ranges[i];\n      if (rangeContains(a, b[0]) || rangeContains(a, b[1])) {\n        if (angle(a[0], b[1]) > angle(a[0], a[1])) a[1] = b[1];\n        if (angle(b[0], a[1]) > angle(a[0], a[1])) a[0] = b[0];\n      } else {\n        merged.push(a = b);\n      }\n    }\n\n    // Finally, find the largest gap between the merged ranges.\n    // The final bounding box will be the inverse of this gap.\n    for (deltaMax = -Infinity, n = merged.length - 1, i = 0, a = merged[n]; i <= n; a = b, ++i) {\n      b = merged[i];\n      if ((delta = angle(a[1], b[0])) > deltaMax) deltaMax = delta, lambda0 = b[0], lambda1 = a[1];\n    }\n  }\n\n  ranges = range = null;\n\n  return lambda0 === Infinity || phi0 === Infinity\n      ? [[NaN, NaN], [NaN, NaN]]\n      : [[lambda0, phi0], [lambda1, phi1]];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9ib3VuZHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQStCO0FBQ21CO0FBQzZDO0FBQ3RDO0FBQ3hCOztBQUVqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDJDQUFLO0FBQ3hCLElBQUksZ0RBQVU7QUFDZCxHQUFHO0FBQ0g7QUFDQSxJQUFJLGdEQUFVO0FBQ2Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxpREFBVztBQUNuQix3QkFBd0IsNkNBQU87QUFDL0IseUJBQXlCLDZDQUFPO0FBQ2hDO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxVQUFVLHdEQUFTLFdBQVcsNkNBQU8sUUFBUSw2Q0FBTztBQUNwRDtBQUNBLGlCQUFpQiw2REFBYztBQUMvQjtBQUNBLHFCQUFxQiw2REFBYztBQUNuQyxJQUFJLHdFQUF5QjtBQUM3QixpQkFBaUIsd0RBQVM7QUFDMUI7QUFDQTtBQUNBLGtDQUFrQyw2Q0FBTztBQUN6QztBQUNBLHVCQUF1Qiw2Q0FBRztBQUMxQjtBQUNBLDZCQUE2Qiw2Q0FBTztBQUNwQztBQUNBLE1BQU07QUFDTiw4QkFBOEIsNkNBQU87QUFDckM7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQiw2Q0FBRztBQUNwQixJQUFJO0FBQ0o7QUFDQTtBQUNBLEVBQUUsZ0RBQVU7QUFDWjtBQUNBOztBQUVBO0FBQ0EsRUFBRSxnREFBVTtBQUNaOztBQUVBO0FBQ0E7QUFDQSxFQUFFLGdEQUFVO0FBQ1osTUFBTSw2Q0FBRyxhQUFhLDZDQUFPO0FBQzdCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSw2QkFBZSxvQ0FBUztBQUN4Qjs7QUFFQTtBQUNBO0FBQ0EsRUFBRSxzREFBTTs7QUFFUjtBQUNBO0FBQ0E7O0FBRUE7QUFDQSw2Q0FBNkMsT0FBTztBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLDRFQUE0RSxRQUFRO0FBQ3BGO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGQzLWdlb1xcc3JjXFxib3VuZHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtBZGRlcn0gZnJvbSBcImQzLWFycmF5XCI7XG5pbXBvcnQge2FyZWFTdHJlYW0sIGFyZWFSaW5nU3VtfSBmcm9tIFwiLi9hcmVhLmpzXCI7XG5pbXBvcnQge2NhcnRlc2lhbiwgY2FydGVzaWFuQ3Jvc3MsIGNhcnRlc2lhbk5vcm1hbGl6ZUluUGxhY2UsIHNwaGVyaWNhbH0gZnJvbSBcIi4vY2FydGVzaWFuLmpzXCI7XG5pbXBvcnQge2FicywgZGVncmVlcywgZXBzaWxvbiwgcmFkaWFuc30gZnJvbSBcIi4vbWF0aC5qc1wiO1xuaW1wb3J0IHN0cmVhbSBmcm9tIFwiLi9zdHJlYW0uanNcIjtcblxudmFyIGxhbWJkYTAsIHBoaTAsIGxhbWJkYTEsIHBoaTEsIC8vIGJvdW5kc1xuICAgIGxhbWJkYTIsIC8vIHByZXZpb3VzIGxhbWJkYS1jb29yZGluYXRlXG4gICAgbGFtYmRhMDAsIHBoaTAwLCAvLyBmaXJzdCBwb2ludFxuICAgIHAwLCAvLyBwcmV2aW91cyAzRCBwb2ludFxuICAgIGRlbHRhU3VtLFxuICAgIHJhbmdlcyxcbiAgICByYW5nZTtcblxudmFyIGJvdW5kc1N0cmVhbSA9IHtcbiAgcG9pbnQ6IGJvdW5kc1BvaW50LFxuICBsaW5lU3RhcnQ6IGJvdW5kc0xpbmVTdGFydCxcbiAgbGluZUVuZDogYm91bmRzTGluZUVuZCxcbiAgcG9seWdvblN0YXJ0OiBmdW5jdGlvbigpIHtcbiAgICBib3VuZHNTdHJlYW0ucG9pbnQgPSBib3VuZHNSaW5nUG9pbnQ7XG4gICAgYm91bmRzU3RyZWFtLmxpbmVTdGFydCA9IGJvdW5kc1JpbmdTdGFydDtcbiAgICBib3VuZHNTdHJlYW0ubGluZUVuZCA9IGJvdW5kc1JpbmdFbmQ7XG4gICAgZGVsdGFTdW0gPSBuZXcgQWRkZXIoKTtcbiAgICBhcmVhU3RyZWFtLnBvbHlnb25TdGFydCgpO1xuICB9LFxuICBwb2x5Z29uRW5kOiBmdW5jdGlvbigpIHtcbiAgICBhcmVhU3RyZWFtLnBvbHlnb25FbmQoKTtcbiAgICBib3VuZHNTdHJlYW0ucG9pbnQgPSBib3VuZHNQb2ludDtcbiAgICBib3VuZHNTdHJlYW0ubGluZVN0YXJ0ID0gYm91bmRzTGluZVN0YXJ0O1xuICAgIGJvdW5kc1N0cmVhbS5saW5lRW5kID0gYm91bmRzTGluZUVuZDtcbiAgICBpZiAoYXJlYVJpbmdTdW0gPCAwKSBsYW1iZGEwID0gLShsYW1iZGExID0gMTgwKSwgcGhpMCA9IC0ocGhpMSA9IDkwKTtcbiAgICBlbHNlIGlmIChkZWx0YVN1bSA+IGVwc2lsb24pIHBoaTEgPSA5MDtcbiAgICBlbHNlIGlmIChkZWx0YVN1bSA8IC1lcHNpbG9uKSBwaGkwID0gLTkwO1xuICAgIHJhbmdlWzBdID0gbGFtYmRhMCwgcmFuZ2VbMV0gPSBsYW1iZGExO1xuICB9LFxuICBzcGhlcmU6IGZ1bmN0aW9uKCkge1xuICAgIGxhbWJkYTAgPSAtKGxhbWJkYTEgPSAxODApLCBwaGkwID0gLShwaGkxID0gOTApO1xuICB9XG59O1xuXG5mdW5jdGlvbiBib3VuZHNQb2ludChsYW1iZGEsIHBoaSkge1xuICByYW5nZXMucHVzaChyYW5nZSA9IFtsYW1iZGEwID0gbGFtYmRhLCBsYW1iZGExID0gbGFtYmRhXSk7XG4gIGlmIChwaGkgPCBwaGkwKSBwaGkwID0gcGhpO1xuICBpZiAocGhpID4gcGhpMSkgcGhpMSA9IHBoaTtcbn1cblxuZnVuY3Rpb24gbGluZVBvaW50KGxhbWJkYSwgcGhpKSB7XG4gIHZhciBwID0gY2FydGVzaWFuKFtsYW1iZGEgKiByYWRpYW5zLCBwaGkgKiByYWRpYW5zXSk7XG4gIGlmIChwMCkge1xuICAgIHZhciBub3JtYWwgPSBjYXJ0ZXNpYW5Dcm9zcyhwMCwgcCksXG4gICAgICAgIGVxdWF0b3JpYWwgPSBbbm9ybWFsWzFdLCAtbm9ybWFsWzBdLCAwXSxcbiAgICAgICAgaW5mbGVjdGlvbiA9IGNhcnRlc2lhbkNyb3NzKGVxdWF0b3JpYWwsIG5vcm1hbCk7XG4gICAgY2FydGVzaWFuTm9ybWFsaXplSW5QbGFjZShpbmZsZWN0aW9uKTtcbiAgICBpbmZsZWN0aW9uID0gc3BoZXJpY2FsKGluZmxlY3Rpb24pO1xuICAgIHZhciBkZWx0YSA9IGxhbWJkYSAtIGxhbWJkYTIsXG4gICAgICAgIHNpZ24gPSBkZWx0YSA+IDAgPyAxIDogLTEsXG4gICAgICAgIGxhbWJkYWkgPSBpbmZsZWN0aW9uWzBdICogZGVncmVlcyAqIHNpZ24sXG4gICAgICAgIHBoaWksXG4gICAgICAgIGFudGltZXJpZGlhbiA9IGFicyhkZWx0YSkgPiAxODA7XG4gICAgaWYgKGFudGltZXJpZGlhbiBeIChzaWduICogbGFtYmRhMiA8IGxhbWJkYWkgJiYgbGFtYmRhaSA8IHNpZ24gKiBsYW1iZGEpKSB7XG4gICAgICBwaGlpID0gaW5mbGVjdGlvblsxXSAqIGRlZ3JlZXM7XG4gICAgICBpZiAocGhpaSA+IHBoaTEpIHBoaTEgPSBwaGlpO1xuICAgIH0gZWxzZSBpZiAobGFtYmRhaSA9IChsYW1iZGFpICsgMzYwKSAlIDM2MCAtIDE4MCwgYW50aW1lcmlkaWFuIF4gKHNpZ24gKiBsYW1iZGEyIDwgbGFtYmRhaSAmJiBsYW1iZGFpIDwgc2lnbiAqIGxhbWJkYSkpIHtcbiAgICAgIHBoaWkgPSAtaW5mbGVjdGlvblsxXSAqIGRlZ3JlZXM7XG4gICAgICBpZiAocGhpaSA8IHBoaTApIHBoaTAgPSBwaGlpO1xuICAgIH0gZWxzZSB7XG4gICAgICBpZiAocGhpIDwgcGhpMCkgcGhpMCA9IHBoaTtcbiAgICAgIGlmIChwaGkgPiBwaGkxKSBwaGkxID0gcGhpO1xuICAgIH1cbiAgICBpZiAoYW50aW1lcmlkaWFuKSB7XG4gICAgICBpZiAobGFtYmRhIDwgbGFtYmRhMikge1xuICAgICAgICBpZiAoYW5nbGUobGFtYmRhMCwgbGFtYmRhKSA+IGFuZ2xlKGxhbWJkYTAsIGxhbWJkYTEpKSBsYW1iZGExID0gbGFtYmRhO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgaWYgKGFuZ2xlKGxhbWJkYSwgbGFtYmRhMSkgPiBhbmdsZShsYW1iZGEwLCBsYW1iZGExKSkgbGFtYmRhMCA9IGxhbWJkYTtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgaWYgKGxhbWJkYTEgPj0gbGFtYmRhMCkge1xuICAgICAgICBpZiAobGFtYmRhIDwgbGFtYmRhMCkgbGFtYmRhMCA9IGxhbWJkYTtcbiAgICAgICAgaWYgKGxhbWJkYSA+IGxhbWJkYTEpIGxhbWJkYTEgPSBsYW1iZGE7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBpZiAobGFtYmRhID4gbGFtYmRhMikge1xuICAgICAgICAgIGlmIChhbmdsZShsYW1iZGEwLCBsYW1iZGEpID4gYW5nbGUobGFtYmRhMCwgbGFtYmRhMSkpIGxhbWJkYTEgPSBsYW1iZGE7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgaWYgKGFuZ2xlKGxhbWJkYSwgbGFtYmRhMSkgPiBhbmdsZShsYW1iZGEwLCBsYW1iZGExKSkgbGFtYmRhMCA9IGxhbWJkYTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICByYW5nZXMucHVzaChyYW5nZSA9IFtsYW1iZGEwID0gbGFtYmRhLCBsYW1iZGExID0gbGFtYmRhXSk7XG4gIH1cbiAgaWYgKHBoaSA8IHBoaTApIHBoaTAgPSBwaGk7XG4gIGlmIChwaGkgPiBwaGkxKSBwaGkxID0gcGhpO1xuICBwMCA9IHAsIGxhbWJkYTIgPSBsYW1iZGE7XG59XG5cbmZ1bmN0aW9uIGJvdW5kc0xpbmVTdGFydCgpIHtcbiAgYm91bmRzU3RyZWFtLnBvaW50ID0gbGluZVBvaW50O1xufVxuXG5mdW5jdGlvbiBib3VuZHNMaW5lRW5kKCkge1xuICByYW5nZVswXSA9IGxhbWJkYTAsIHJhbmdlWzFdID0gbGFtYmRhMTtcbiAgYm91bmRzU3RyZWFtLnBvaW50ID0gYm91bmRzUG9pbnQ7XG4gIHAwID0gbnVsbDtcbn1cblxuZnVuY3Rpb24gYm91bmRzUmluZ1BvaW50KGxhbWJkYSwgcGhpKSB7XG4gIGlmIChwMCkge1xuICAgIHZhciBkZWx0YSA9IGxhbWJkYSAtIGxhbWJkYTI7XG4gICAgZGVsdGFTdW0uYWRkKGFicyhkZWx0YSkgPiAxODAgPyBkZWx0YSArIChkZWx0YSA+IDAgPyAzNjAgOiAtMzYwKSA6IGRlbHRhKTtcbiAgfSBlbHNlIHtcbiAgICBsYW1iZGEwMCA9IGxhbWJkYSwgcGhpMDAgPSBwaGk7XG4gIH1cbiAgYXJlYVN0cmVhbS5wb2ludChsYW1iZGEsIHBoaSk7XG4gIGxpbmVQb2ludChsYW1iZGEsIHBoaSk7XG59XG5cbmZ1bmN0aW9uIGJvdW5kc1JpbmdTdGFydCgpIHtcbiAgYXJlYVN0cmVhbS5saW5lU3RhcnQoKTtcbn1cblxuZnVuY3Rpb24gYm91bmRzUmluZ0VuZCgpIHtcbiAgYm91bmRzUmluZ1BvaW50KGxhbWJkYTAwLCBwaGkwMCk7XG4gIGFyZWFTdHJlYW0ubGluZUVuZCgpO1xuICBpZiAoYWJzKGRlbHRhU3VtKSA+IGVwc2lsb24pIGxhbWJkYTAgPSAtKGxhbWJkYTEgPSAxODApO1xuICByYW5nZVswXSA9IGxhbWJkYTAsIHJhbmdlWzFdID0gbGFtYmRhMTtcbiAgcDAgPSBudWxsO1xufVxuXG4vLyBGaW5kcyB0aGUgbGVmdC1yaWdodCBkaXN0YW5jZSBiZXR3ZWVuIHR3byBsb25naXR1ZGVzLlxuLy8gVGhpcyBpcyBhbG1vc3QgdGhlIHNhbWUgYXMgKGxhbWJkYTEgLSBsYW1iZGEwICsgMzYwwrApICUgMzYwwrAsIGV4Y2VwdCB0aGF0IHdlIHdhbnRcbi8vIHRoZSBkaXN0YW5jZSBiZXR3ZWVuIMKxMTgwwrAgdG8gYmUgMzYwwrAuXG5mdW5jdGlvbiBhbmdsZShsYW1iZGEwLCBsYW1iZGExKSB7XG4gIHJldHVybiAobGFtYmRhMSAtPSBsYW1iZGEwKSA8IDAgPyBsYW1iZGExICsgMzYwIDogbGFtYmRhMTtcbn1cblxuZnVuY3Rpb24gcmFuZ2VDb21wYXJlKGEsIGIpIHtcbiAgcmV0dXJuIGFbMF0gLSBiWzBdO1xufVxuXG5mdW5jdGlvbiByYW5nZUNvbnRhaW5zKHJhbmdlLCB4KSB7XG4gIHJldHVybiByYW5nZVswXSA8PSByYW5nZVsxXSA/IHJhbmdlWzBdIDw9IHggJiYgeCA8PSByYW5nZVsxXSA6IHggPCByYW5nZVswXSB8fCByYW5nZVsxXSA8IHg7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKGZlYXR1cmUpIHtcbiAgdmFyIGksIG4sIGEsIGIsIG1lcmdlZCwgZGVsdGFNYXgsIGRlbHRhO1xuXG4gIHBoaTEgPSBsYW1iZGExID0gLShsYW1iZGEwID0gcGhpMCA9IEluZmluaXR5KTtcbiAgcmFuZ2VzID0gW107XG4gIHN0cmVhbShmZWF0dXJlLCBib3VuZHNTdHJlYW0pO1xuXG4gIC8vIEZpcnN0LCBzb3J0IHJhbmdlcyBieSB0aGVpciBtaW5pbXVtIGxvbmdpdHVkZXMuXG4gIGlmIChuID0gcmFuZ2VzLmxlbmd0aCkge1xuICAgIHJhbmdlcy5zb3J0KHJhbmdlQ29tcGFyZSk7XG5cbiAgICAvLyBUaGVuLCBtZXJnZSBhbnkgcmFuZ2VzIHRoYXQgb3ZlcmxhcC5cbiAgICBmb3IgKGkgPSAxLCBhID0gcmFuZ2VzWzBdLCBtZXJnZWQgPSBbYV07IGkgPCBuOyArK2kpIHtcbiAgICAgIGIgPSByYW5nZXNbaV07XG4gICAgICBpZiAocmFuZ2VDb250YWlucyhhLCBiWzBdKSB8fCByYW5nZUNvbnRhaW5zKGEsIGJbMV0pKSB7XG4gICAgICAgIGlmIChhbmdsZShhWzBdLCBiWzFdKSA+IGFuZ2xlKGFbMF0sIGFbMV0pKSBhWzFdID0gYlsxXTtcbiAgICAgICAgaWYgKGFuZ2xlKGJbMF0sIGFbMV0pID4gYW5nbGUoYVswXSwgYVsxXSkpIGFbMF0gPSBiWzBdO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgbWVyZ2VkLnB1c2goYSA9IGIpO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEZpbmFsbHksIGZpbmQgdGhlIGxhcmdlc3QgZ2FwIGJldHdlZW4gdGhlIG1lcmdlZCByYW5nZXMuXG4gICAgLy8gVGhlIGZpbmFsIGJvdW5kaW5nIGJveCB3aWxsIGJlIHRoZSBpbnZlcnNlIG9mIHRoaXMgZ2FwLlxuICAgIGZvciAoZGVsdGFNYXggPSAtSW5maW5pdHksIG4gPSBtZXJnZWQubGVuZ3RoIC0gMSwgaSA9IDAsIGEgPSBtZXJnZWRbbl07IGkgPD0gbjsgYSA9IGIsICsraSkge1xuICAgICAgYiA9IG1lcmdlZFtpXTtcbiAgICAgIGlmICgoZGVsdGEgPSBhbmdsZShhWzFdLCBiWzBdKSkgPiBkZWx0YU1heCkgZGVsdGFNYXggPSBkZWx0YSwgbGFtYmRhMCA9IGJbMF0sIGxhbWJkYTEgPSBhWzFdO1xuICAgIH1cbiAgfVxuXG4gIHJhbmdlcyA9IHJhbmdlID0gbnVsbDtcblxuICByZXR1cm4gbGFtYmRhMCA9PT0gSW5maW5pdHkgfHwgcGhpMCA9PT0gSW5maW5pdHlcbiAgICAgID8gW1tOYU4sIE5hTl0sIFtOYU4sIE5hTl1dXG4gICAgICA6IFtbbGFtYmRhMCwgcGhpMF0sIFtsYW1iZGExLCBwaGkxXV07XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/bounds.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/cartesian.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-geo/src/cartesian.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cartesian: () => (/* binding */ cartesian),\n/* harmony export */   cartesianAddInPlace: () => (/* binding */ cartesianAddInPlace),\n/* harmony export */   cartesianCross: () => (/* binding */ cartesianCross),\n/* harmony export */   cartesianDot: () => (/* binding */ cartesianDot),\n/* harmony export */   cartesianNormalizeInPlace: () => (/* binding */ cartesianNormalizeInPlace),\n/* harmony export */   cartesianScale: () => (/* binding */ cartesianScale),\n/* harmony export */   spherical: () => (/* binding */ spherical)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\nfunction spherical(cartesian) {\n  return [(0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(cartesian[1], cartesian[0]), (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(cartesian[2])];\n}\n\nfunction cartesian(spherical) {\n  var lambda = spherical[0], phi = spherical[1], cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi);\n  return [cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(lambda), cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(lambda), (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi)];\n}\n\nfunction cartesianDot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\n\nfunction cartesianCross(a, b) {\n  return [a[1] * b[2] - a[2] * b[1], a[2] * b[0] - a[0] * b[2], a[0] * b[1] - a[1] * b[0]];\n}\n\n// TODO return a\nfunction cartesianAddInPlace(a, b) {\n  a[0] += b[0], a[1] += b[1], a[2] += b[2];\n}\n\nfunction cartesianScale(vector, k) {\n  return [vector[0] * k, vector[1] * k, vector[2] * k];\n}\n\n// TODO return d\nfunction cartesianNormalizeInPlace(d) {\n  var l = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(d[0] * d[0] + d[1] * d[1] + d[2] * d[2]);\n  d[0] /= l, d[1] /= l, d[2] /= l;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9jYXJ0ZXNpYW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBc0Q7O0FBRS9DO0FBQ1AsVUFBVSwrQ0FBSyw4QkFBOEIsOENBQUk7QUFDakQ7O0FBRU87QUFDUCwwREFBMEQsNkNBQUc7QUFDN0QsbUJBQW1CLDZDQUFHLG1CQUFtQiw2Q0FBRyxVQUFVLDZDQUFHO0FBQ3pEOztBQUVPO0FBQ1A7QUFDQTs7QUFFTztBQUNQO0FBQ0E7O0FBRUE7QUFDTztBQUNQO0FBQ0E7O0FBRU87QUFDUDtBQUNBOztBQUVBO0FBQ087QUFDUCxVQUFVLDhDQUFJO0FBQ2Q7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1nZW9cXHNyY1xcY2FydGVzaWFuLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7YXNpbiwgYXRhbjIsIGNvcywgc2luLCBzcXJ0fSBmcm9tIFwiLi9tYXRoLmpzXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBzcGhlcmljYWwoY2FydGVzaWFuKSB7XG4gIHJldHVybiBbYXRhbjIoY2FydGVzaWFuWzFdLCBjYXJ0ZXNpYW5bMF0pLCBhc2luKGNhcnRlc2lhblsyXSldO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gY2FydGVzaWFuKHNwaGVyaWNhbCkge1xuICB2YXIgbGFtYmRhID0gc3BoZXJpY2FsWzBdLCBwaGkgPSBzcGhlcmljYWxbMV0sIGNvc1BoaSA9IGNvcyhwaGkpO1xuICByZXR1cm4gW2Nvc1BoaSAqIGNvcyhsYW1iZGEpLCBjb3NQaGkgKiBzaW4obGFtYmRhKSwgc2luKHBoaSldO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gY2FydGVzaWFuRG90KGEsIGIpIHtcbiAgcmV0dXJuIGFbMF0gKiBiWzBdICsgYVsxXSAqIGJbMV0gKyBhWzJdICogYlsyXTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGNhcnRlc2lhbkNyb3NzKGEsIGIpIHtcbiAgcmV0dXJuIFthWzFdICogYlsyXSAtIGFbMl0gKiBiWzFdLCBhWzJdICogYlswXSAtIGFbMF0gKiBiWzJdLCBhWzBdICogYlsxXSAtIGFbMV0gKiBiWzBdXTtcbn1cblxuLy8gVE9ETyByZXR1cm4gYVxuZXhwb3J0IGZ1bmN0aW9uIGNhcnRlc2lhbkFkZEluUGxhY2UoYSwgYikge1xuICBhWzBdICs9IGJbMF0sIGFbMV0gKz0gYlsxXSwgYVsyXSArPSBiWzJdO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gY2FydGVzaWFuU2NhbGUodmVjdG9yLCBrKSB7XG4gIHJldHVybiBbdmVjdG9yWzBdICogaywgdmVjdG9yWzFdICogaywgdmVjdG9yWzJdICoga107XG59XG5cbi8vIFRPRE8gcmV0dXJuIGRcbmV4cG9ydCBmdW5jdGlvbiBjYXJ0ZXNpYW5Ob3JtYWxpemVJblBsYWNlKGQpIHtcbiAgdmFyIGwgPSBzcXJ0KGRbMF0gKiBkWzBdICsgZFsxXSAqIGRbMV0gKyBkWzJdICogZFsyXSk7XG4gIGRbMF0gLz0gbCwgZFsxXSAvPSBsLCBkWzJdIC89IGw7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/cartesian.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/centroid.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-geo/src/centroid.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./noop.js */ \"(ssr)/./node_modules/d3-geo/src/noop.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stream.js */ \"(ssr)/./node_modules/d3-geo/src/stream.js\");\n\n\n\n\n\nvar W0, W1,\n    X0, Y0, Z0,\n    X1, Y1, Z1,\n    X2, Y2, Z2,\n    lambda00, phi00, // first point\n    x0, y0, z0; // previous point\n\nvar centroidStream = {\n  sphere: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n  point: centroidPoint,\n  lineStart: centroidLineStart,\n  lineEnd: centroidLineEnd,\n  polygonStart: function() {\n    centroidStream.lineStart = centroidRingStart;\n    centroidStream.lineEnd = centroidRingEnd;\n  },\n  polygonEnd: function() {\n    centroidStream.lineStart = centroidLineStart;\n    centroidStream.lineEnd = centroidLineEnd;\n  }\n};\n\n// Arithmetic mean of Cartesian vectors.\nfunction centroidPoint(lambda, phi) {\n  lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n  var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi);\n  centroidPointCartesian(cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(lambda), cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda), (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi));\n}\n\nfunction centroidPointCartesian(x, y, z) {\n  ++W0;\n  X0 += (x - X0) / W0;\n  Y0 += (y - Y0) / W0;\n  Z0 += (z - Z0) / W0;\n}\n\nfunction centroidLineStart() {\n  centroidStream.point = centroidLinePointFirst;\n}\n\nfunction centroidLinePointFirst(lambda, phi) {\n  lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n  var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi);\n  x0 = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(lambda);\n  y0 = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda);\n  z0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi);\n  centroidStream.point = centroidLinePoint;\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidLinePoint(lambda, phi) {\n  lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n  var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi),\n      x = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(lambda),\n      y = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda),\n      z = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi),\n      w = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.atan2)((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sqrt)((w = y0 * z - z0 * y) * w + (w = z0 * x - x0 * z) * w + (w = x0 * y - y0 * x) * w), x0 * x + y0 * y + z0 * z);\n  W1 += w;\n  X1 += w * (x0 + (x0 = x));\n  Y1 += w * (y0 + (y0 = y));\n  Z1 += w * (z0 + (z0 = z));\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidLineEnd() {\n  centroidStream.point = centroidPoint;\n}\n\n// See J. E. Brock, The Inertia Tensor for a Spherical Triangle,\n// J. Applied Mechanics 42, 239 (1975).\nfunction centroidRingStart() {\n  centroidStream.point = centroidRingPointFirst;\n}\n\nfunction centroidRingEnd() {\n  centroidRingPoint(lambda00, phi00);\n  centroidStream.point = centroidPoint;\n}\n\nfunction centroidRingPointFirst(lambda, phi) {\n  lambda00 = lambda, phi00 = phi;\n  lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n  centroidStream.point = centroidRingPoint;\n  var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi);\n  x0 = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(lambda);\n  y0 = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda);\n  z0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi);\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidRingPoint(lambda, phi) {\n  lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n  var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi),\n      x = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(lambda),\n      y = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda),\n      z = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi),\n      cx = y0 * z - z0 * y,\n      cy = z0 * x - x0 * z,\n      cz = x0 * y - y0 * x,\n      m = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.hypot)(cx, cy, cz),\n      w = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.asin)(m), // line weight = angle\n      v = m && -w / m; // area weight multiplier\n  X2.add(v * cx);\n  Y2.add(v * cy);\n  Z2.add(v * cz);\n  W1 += w;\n  X1 += w * (x0 + (x0 = x));\n  Y1 += w * (y0 + (y0 = y));\n  Z1 += w * (z0 + (z0 = z));\n  centroidPointCartesian(x0, y0, z0);\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(object) {\n  W0 = W1 =\n  X0 = Y0 = Z0 =\n  X1 = Y1 = Z1 = 0;\n  X2 = new d3_array__WEBPACK_IMPORTED_MODULE_2__.Adder();\n  Y2 = new d3_array__WEBPACK_IMPORTED_MODULE_2__.Adder();\n  Z2 = new d3_array__WEBPACK_IMPORTED_MODULE_2__.Adder();\n  (0,_stream_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(object, centroidStream);\n\n  var x = +X2,\n      y = +Y2,\n      z = +Z2,\n      m = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.hypot)(x, y, z);\n\n  // If the area-weighted ccentroid is undefined, fall back to length-weighted ccentroid.\n  if (m < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon2) {\n    x = X1, y = Y1, z = Z1;\n    // If the feature has zero length, fall back to arithmetic mean of point vectors.\n    if (W1 < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) x = X0, y = Y0, z = Z0;\n    m = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.hypot)(x, y, z);\n    // If the feature still has an undefined ccentroid, then return.\n    if (m < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon2) return [NaN, NaN];\n  }\n\n  return [(0,_math_js__WEBPACK_IMPORTED_MODULE_1__.atan2)(y, x) * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees, (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.asin)(z / m) * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/centroid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/circle.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-geo/src/circle.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   circleStream: () => (/* binding */ circleStream),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cartesian_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./cartesian.js */ \"(ssr)/./node_modules/d3-geo/src/cartesian.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/./node_modules/d3-geo/src/constant.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _rotation_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./rotation.js */ \"(ssr)/./node_modules/d3-geo/src/rotation.js\");\n\n\n\n\n\n// Generates a circle centered at [0°, 0°], with a given radius and precision.\nfunction circleStream(stream, radius, delta, direction, t0, t1) {\n  if (!delta) return;\n  var cosRadius = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(radius),\n      sinRadius = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(radius),\n      step = direction * delta;\n  if (t0 == null) {\n    t0 = radius + direction * _math_js__WEBPACK_IMPORTED_MODULE_0__.tau;\n    t1 = radius - step / 2;\n  } else {\n    t0 = circleRadius(cosRadius, t0);\n    t1 = circleRadius(cosRadius, t1);\n    if (direction > 0 ? t0 < t1 : t0 > t1) t0 += direction * _math_js__WEBPACK_IMPORTED_MODULE_0__.tau;\n  }\n  for (var point, t = t0; direction > 0 ? t > t1 : t < t1; t -= step) {\n    point = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_1__.spherical)([cosRadius, -sinRadius * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(t), -sinRadius * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(t)]);\n    stream.point(point[0], point[1]);\n  }\n}\n\n// Returns the signed angle of a cartesian point relative to [cosRadius, 0, 0].\nfunction circleRadius(cosRadius, point) {\n  point = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_1__.cartesian)(point), point[0] -= cosRadius;\n  (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_1__.cartesianNormalizeInPlace)(point);\n  var radius = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.acos)(-point[1]);\n  return ((-point[2] < 0 ? -radius : radius) + _math_js__WEBPACK_IMPORTED_MODULE_0__.tau - _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) % _math_js__WEBPACK_IMPORTED_MODULE_0__.tau;\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  var center = (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])([0, 0]),\n      radius = (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(90),\n      precision = (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(2),\n      ring,\n      rotate,\n      stream = {point: point};\n\n  function point(x, y) {\n    ring.push(x = rotate(x, y));\n    x[0] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, x[1] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees;\n  }\n\n  function circle() {\n    var c = center.apply(this, arguments),\n        r = radius.apply(this, arguments) * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians,\n        p = precision.apply(this, arguments) * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians;\n    ring = [];\n    rotate = (0,_rotation_js__WEBPACK_IMPORTED_MODULE_3__.rotateRadians)(-c[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, -c[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, 0).invert;\n    circleStream(stream, r, p, 1);\n    c = {type: \"Polygon\", coordinates: [ring]};\n    ring = rotate = null;\n    return c;\n  }\n\n  circle.center = function(_) {\n    return arguments.length ? (center = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])([+_[0], +_[1]]), circle) : center;\n  };\n\n  circle.radius = function(_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(+_), circle) : radius;\n  };\n\n  circle.precision = function(_) {\n    return arguments.length ? (precision = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(+_), circle) : precision;\n  };\n\n  return circle;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/circle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/clip/antimeridian.js":
/*!******************************************************!*\
  !*** ./node_modules/d3-geo/src/clip/antimeridian.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/clip/index.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\n  function() { return true; },\n  clipAntimeridianLine,\n  clipAntimeridianInterpolate,\n  [-_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, -_math_js__WEBPACK_IMPORTED_MODULE_1__.halfPi]\n));\n\n// Takes a line and cuts into visible segments. Return values: 0 - there were\n// intersections or the line was empty; 1 - no intersections; 2 - there were\n// intersections, and the first and last segments should be rejoined.\nfunction clipAntimeridianLine(stream) {\n  var lambda0 = NaN,\n      phi0 = NaN,\n      sign0 = NaN,\n      clean; // no intersections\n\n  return {\n    lineStart: function() {\n      stream.lineStart();\n      clean = 1;\n    },\n    point: function(lambda1, phi1) {\n      var sign1 = lambda1 > 0 ? _math_js__WEBPACK_IMPORTED_MODULE_1__.pi : -_math_js__WEBPACK_IMPORTED_MODULE_1__.pi,\n          delta = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(lambda1 - lambda0);\n      if ((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(delta - _math_js__WEBPACK_IMPORTED_MODULE_1__.pi) < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) { // line crosses a pole\n        stream.point(lambda0, phi0 = (phi0 + phi1) / 2 > 0 ? _math_js__WEBPACK_IMPORTED_MODULE_1__.halfPi : -_math_js__WEBPACK_IMPORTED_MODULE_1__.halfPi);\n        stream.point(sign0, phi0);\n        stream.lineEnd();\n        stream.lineStart();\n        stream.point(sign1, phi0);\n        stream.point(lambda1, phi0);\n        clean = 0;\n      } else if (sign0 !== sign1 && delta >= _math_js__WEBPACK_IMPORTED_MODULE_1__.pi) { // line crosses antimeridian\n        if ((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(lambda0 - sign0) < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) lambda0 -= sign0 * _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon; // handle degeneracies\n        if ((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(lambda1 - sign1) < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) lambda1 -= sign1 * _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon;\n        phi0 = clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1);\n        stream.point(sign0, phi0);\n        stream.lineEnd();\n        stream.lineStart();\n        stream.point(sign1, phi0);\n        clean = 0;\n      }\n      stream.point(lambda0 = lambda1, phi0 = phi1);\n      sign0 = sign1;\n    },\n    lineEnd: function() {\n      stream.lineEnd();\n      lambda0 = phi0 = NaN;\n    },\n    clean: function() {\n      return 2 - clean; // if intersections, rejoin first and last segments\n    }\n  };\n}\n\nfunction clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1) {\n  var cosPhi0,\n      cosPhi1,\n      sinLambda0Lambda1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda0 - lambda1);\n  return (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(sinLambda0Lambda1) > _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon\n      ? (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.atan)(((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi0) * (cosPhi1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi1)) * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda1)\n          - (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi1) * (cosPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi0)) * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda0))\n          / (cosPhi0 * cosPhi1 * sinLambda0Lambda1))\n      : (phi0 + phi1) / 2;\n}\n\nfunction clipAntimeridianInterpolate(from, to, direction, stream) {\n  var phi;\n  if (from == null) {\n    phi = direction * _math_js__WEBPACK_IMPORTED_MODULE_1__.halfPi;\n    stream.point(-_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, phi);\n    stream.point(0, phi);\n    stream.point(_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, phi);\n    stream.point(_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, 0);\n    stream.point(_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, -phi);\n    stream.point(0, -phi);\n    stream.point(-_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, -phi);\n    stream.point(-_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, 0);\n    stream.point(-_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, phi);\n  } else if ((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(from[0] - to[0]) > _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) {\n    var lambda = from[0] < to[0] ? _math_js__WEBPACK_IMPORTED_MODULE_1__.pi : -_math_js__WEBPACK_IMPORTED_MODULE_1__.pi;\n    phi = direction * lambda / 2;\n    stream.point(-lambda, phi);\n    stream.point(0, phi);\n    stream.point(lambda, phi);\n  } else {\n    stream.point(to[0], to[1]);\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/clip/antimeridian.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/clip/buffer.js":
/*!************************************************!*\
  !*** ./node_modules/d3-geo/src/clip/buffer.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../noop.js */ \"(ssr)/./node_modules/d3-geo/src/noop.js\");\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  var lines = [],\n      line;\n  return {\n    point: function(x, y, m) {\n      line.push([x, y, m]);\n    },\n    lineStart: function() {\n      lines.push(line = []);\n    },\n    lineEnd: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    rejoin: function() {\n      if (lines.length > 1) lines.push(lines.pop().concat(lines.shift()));\n    },\n    result: function() {\n      var result = lines;\n      lines = [];\n      line = null;\n      return result;\n    }\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9jbGlwL2J1ZmZlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4Qjs7QUFFOUIsNkJBQWUsc0NBQVc7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMLGFBQWEsZ0RBQUk7QUFDakI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGQzLWdlb1xcc3JjXFxjbGlwXFxidWZmZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG5vb3AgZnJvbSBcIi4uL25vb3AuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oKSB7XG4gIHZhciBsaW5lcyA9IFtdLFxuICAgICAgbGluZTtcbiAgcmV0dXJuIHtcbiAgICBwb2ludDogZnVuY3Rpb24oeCwgeSwgbSkge1xuICAgICAgbGluZS5wdXNoKFt4LCB5LCBtXSk7XG4gICAgfSxcbiAgICBsaW5lU3RhcnQ6IGZ1bmN0aW9uKCkge1xuICAgICAgbGluZXMucHVzaChsaW5lID0gW10pO1xuICAgIH0sXG4gICAgbGluZUVuZDogbm9vcCxcbiAgICByZWpvaW46IGZ1bmN0aW9uKCkge1xuICAgICAgaWYgKGxpbmVzLmxlbmd0aCA+IDEpIGxpbmVzLnB1c2gobGluZXMucG9wKCkuY29uY2F0KGxpbmVzLnNoaWZ0KCkpKTtcbiAgICB9LFxuICAgIHJlc3VsdDogZnVuY3Rpb24oKSB7XG4gICAgICB2YXIgcmVzdWx0ID0gbGluZXM7XG4gICAgICBsaW5lcyA9IFtdO1xuICAgICAgbGluZSA9IG51bGw7XG4gICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH1cbiAgfTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/clip/buffer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/clip/circle.js":
/*!************************************************!*\
  !*** ./node_modules/d3-geo/src/clip/circle.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cartesian_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../cartesian.js */ \"(ssr)/./node_modules/d3-geo/src/cartesian.js\");\n/* harmony import */ var _circle_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../circle.js */ \"(ssr)/./node_modules/d3-geo/src/circle.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _pointEqual_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../pointEqual.js */ \"(ssr)/./node_modules/d3-geo/src/pointEqual.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/clip/index.js\");\n\n\n\n\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(radius) {\n  var cr = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(radius),\n      delta = 2 * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians,\n      smallRadius = cr > 0,\n      notHemisphere = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(cr) > _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon; // TODO optimise for this common case\n\n  function interpolate(from, to, direction, stream) {\n    (0,_circle_js__WEBPACK_IMPORTED_MODULE_1__.circleStream)(stream, radius, delta, direction, from, to);\n  }\n\n  function visible(lambda, phi) {\n    return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(lambda) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi) > cr;\n  }\n\n  // Takes a line and cuts into visible segments. Return values used for polygon\n  // clipping: 0 - there were intersections or the line was empty; 1 - no\n  // intersections 2 - there were intersections, and the first and last segments\n  // should be rejoined.\n  function clipLine(stream) {\n    var point0, // previous point\n        c0, // code for previous point\n        v0, // visibility of previous point\n        v00, // visibility of first point\n        clean; // no intersections\n    return {\n      lineStart: function() {\n        v00 = v0 = false;\n        clean = 1;\n      },\n      point: function(lambda, phi) {\n        var point1 = [lambda, phi],\n            point2,\n            v = visible(lambda, phi),\n            c = smallRadius\n              ? v ? 0 : code(lambda, phi)\n              : v ? code(lambda + (lambda < 0 ? _math_js__WEBPACK_IMPORTED_MODULE_0__.pi : -_math_js__WEBPACK_IMPORTED_MODULE_0__.pi), phi) : 0;\n        if (!point0 && (v00 = v0 = v)) stream.lineStart();\n        if (v !== v0) {\n          point2 = intersect(point0, point1);\n          if (!point2 || (0,_pointEqual_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(point0, point2) || (0,_pointEqual_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(point1, point2))\n            point1[2] = 1;\n        }\n        if (v !== v0) {\n          clean = 0;\n          if (v) {\n            // outside going in\n            stream.lineStart();\n            point2 = intersect(point1, point0);\n            stream.point(point2[0], point2[1]);\n          } else {\n            // inside going out\n            point2 = intersect(point0, point1);\n            stream.point(point2[0], point2[1], 2);\n            stream.lineEnd();\n          }\n          point0 = point2;\n        } else if (notHemisphere && point0 && smallRadius ^ v) {\n          var t;\n          // If the codes for two points are different, or are both zero,\n          // and there this segment intersects with the small circle.\n          if (!(c & c0) && (t = intersect(point1, point0, true))) {\n            clean = 0;\n            if (smallRadius) {\n              stream.lineStart();\n              stream.point(t[0][0], t[0][1]);\n              stream.point(t[1][0], t[1][1]);\n              stream.lineEnd();\n            } else {\n              stream.point(t[1][0], t[1][1]);\n              stream.lineEnd();\n              stream.lineStart();\n              stream.point(t[0][0], t[0][1], 3);\n            }\n          }\n        }\n        if (v && (!point0 || !(0,_pointEqual_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(point0, point1))) {\n          stream.point(point1[0], point1[1]);\n        }\n        point0 = point1, v0 = v, c0 = c;\n      },\n      lineEnd: function() {\n        if (v0) stream.lineEnd();\n        point0 = null;\n      },\n      // Rejoin first and last segments if there were intersections and the first\n      // and last points were visible.\n      clean: function() {\n        return clean | ((v00 && v0) << 1);\n      }\n    };\n  }\n\n  // Intersects the great circle between a and b with the clip circle.\n  function intersect(a, b, two) {\n    var pa = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesian)(a),\n        pb = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesian)(b);\n\n    // We have two planes, n1.p = d1 and n2.p = d2.\n    // Find intersection line p(t) = c1 n1 + c2 n2 + t (n1 ⨯ n2).\n    var n1 = [1, 0, 0], // normal\n        n2 = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianCross)(pa, pb),\n        n2n2 = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianDot)(n2, n2),\n        n1n2 = n2[0], // cartesianDot(n1, n2),\n        determinant = n2n2 - n1n2 * n1n2;\n\n    // Two polar points.\n    if (!determinant) return !two && a;\n\n    var c1 =  cr * n2n2 / determinant,\n        c2 = -cr * n1n2 / determinant,\n        n1xn2 = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianCross)(n1, n2),\n        A = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianScale)(n1, c1),\n        B = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianScale)(n2, c2);\n    (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianAddInPlace)(A, B);\n\n    // Solve |p(t)|^2 = 1.\n    var u = n1xn2,\n        w = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianDot)(A, u),\n        uu = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianDot)(u, u),\n        t2 = w * w - uu * ((0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianDot)(A, A) - 1);\n\n    if (t2 < 0) return;\n\n    var t = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(t2),\n        q = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianScale)(u, (-w - t) / uu);\n    (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianAddInPlace)(q, A);\n    q = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.spherical)(q);\n\n    if (!two) return q;\n\n    // Two intersection points.\n    var lambda0 = a[0],\n        lambda1 = b[0],\n        phi0 = a[1],\n        phi1 = b[1],\n        z;\n\n    if (lambda1 < lambda0) z = lambda0, lambda0 = lambda1, lambda1 = z;\n\n    var delta = lambda1 - lambda0,\n        polar = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(delta - _math_js__WEBPACK_IMPORTED_MODULE_0__.pi) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon,\n        meridian = polar || delta < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n\n    if (!polar && phi1 < phi0) z = phi0, phi0 = phi1, phi1 = z;\n\n    // Check that the first point is between a and b.\n    if (meridian\n        ? polar\n          ? phi0 + phi1 > 0 ^ q[1] < ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(q[0] - lambda0) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon ? phi0 : phi1)\n          : phi0 <= q[1] && q[1] <= phi1\n        : delta > _math_js__WEBPACK_IMPORTED_MODULE_0__.pi ^ (lambda0 <= q[0] && q[0] <= lambda1)) {\n      var q1 = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianScale)(u, (-w + t) / uu);\n      (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianAddInPlace)(q1, A);\n      return [q, (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.spherical)(q1)];\n    }\n  }\n\n  // Generates a 4-bit vector representing the location of a point relative to\n  // the small circle's bounding box.\n  function code(lambda, phi) {\n    var r = smallRadius ? radius : _math_js__WEBPACK_IMPORTED_MODULE_0__.pi - radius,\n        code = 0;\n    if (lambda < -r) code |= 1; // left\n    else if (lambda > r) code |= 2; // right\n    if (phi < -r) code |= 4; // below\n    else if (phi > r) code |= 8; // above\n    return code;\n  }\n\n  return (0,_index_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(visible, clipLine, interpolate, smallRadius ? [0, -radius] : [-_math_js__WEBPACK_IMPORTED_MODULE_0__.pi, radius - _math_js__WEBPACK_IMPORTED_MODULE_0__.pi]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/clip/circle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/clip/extent.js":
/*!************************************************!*\
  !*** ./node_modules/d3-geo/src/clip/extent.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _rectangle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rectangle.js */ \"(ssr)/./node_modules/d3-geo/src/clip/rectangle.js\");\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  var x0 = 0,\n      y0 = 0,\n      x1 = 960,\n      y1 = 500,\n      cache,\n      cacheStream,\n      clip;\n\n  return clip = {\n    stream: function(stream) {\n      return cache && cacheStream === stream ? cache : cache = (0,_rectangle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(x0, y0, x1, y1)(cacheStream = stream);\n    },\n    extent: function(_) {\n      return arguments.length ? (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1], cache = cacheStream = null, clip) : [[x0, y0], [x1, y1]];\n    }\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9jbGlwL2V4dGVudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEyQzs7QUFFM0MsNkJBQWUsc0NBQVc7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLCtEQUErRCx5REFBYTtBQUM1RSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1nZW9cXHNyY1xcY2xpcFxcZXh0ZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjbGlwUmVjdGFuZ2xlIGZyb20gXCIuL3JlY3RhbmdsZS5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbigpIHtcbiAgdmFyIHgwID0gMCxcbiAgICAgIHkwID0gMCxcbiAgICAgIHgxID0gOTYwLFxuICAgICAgeTEgPSA1MDAsXG4gICAgICBjYWNoZSxcbiAgICAgIGNhY2hlU3RyZWFtLFxuICAgICAgY2xpcDtcblxuICByZXR1cm4gY2xpcCA9IHtcbiAgICBzdHJlYW06IGZ1bmN0aW9uKHN0cmVhbSkge1xuICAgICAgcmV0dXJuIGNhY2hlICYmIGNhY2hlU3RyZWFtID09PSBzdHJlYW0gPyBjYWNoZSA6IGNhY2hlID0gY2xpcFJlY3RhbmdsZSh4MCwgeTAsIHgxLCB5MSkoY2FjaGVTdHJlYW0gPSBzdHJlYW0pO1xuICAgIH0sXG4gICAgZXh0ZW50OiBmdW5jdGlvbihfKSB7XG4gICAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/ICh4MCA9ICtfWzBdWzBdLCB5MCA9ICtfWzBdWzFdLCB4MSA9ICtfWzFdWzBdLCB5MSA9ICtfWzFdWzFdLCBjYWNoZSA9IGNhY2hlU3RyZWFtID0gbnVsbCwgY2xpcCkgOiBbW3gwLCB5MF0sIFt4MSwgeTFdXTtcbiAgICB9XG4gIH07XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/clip/extent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/clip/index.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-geo/src/clip/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _buffer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./buffer.js */ \"(ssr)/./node_modules/d3-geo/src/clip/buffer.js\");\n/* harmony import */ var _rejoin_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./rejoin.js */ \"(ssr)/./node_modules/d3-geo/src/clip/rejoin.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _polygonContains_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../polygonContains.js */ \"(ssr)/./node_modules/d3-geo/src/polygonContains.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/merge.js\");\n\n\n\n\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(pointVisible, clipLine, interpolate, start) {\n  return function(sink) {\n    var line = clipLine(sink),\n        ringBuffer = (0,_buffer_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(),\n        ringSink = clipLine(ringBuffer),\n        polygonStarted = false,\n        polygon,\n        segments,\n        ring;\n\n    var clip = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: function() {\n        clip.point = pointRing;\n        clip.lineStart = ringStart;\n        clip.lineEnd = ringEnd;\n        segments = [];\n        polygon = [];\n      },\n      polygonEnd: function() {\n        clip.point = point;\n        clip.lineStart = lineStart;\n        clip.lineEnd = lineEnd;\n        segments = (0,d3_array__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(segments);\n        var startInside = (0,_polygonContains_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(polygon, start);\n        if (segments.length) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          (0,_rejoin_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(segments, compareIntersection, startInside, interpolate, sink);\n        } else if (startInside) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          sink.lineStart();\n          interpolate(null, null, 1, sink);\n          sink.lineEnd();\n        }\n        if (polygonStarted) sink.polygonEnd(), polygonStarted = false;\n        segments = polygon = null;\n      },\n      sphere: function() {\n        sink.polygonStart();\n        sink.lineStart();\n        interpolate(null, null, 1, sink);\n        sink.lineEnd();\n        sink.polygonEnd();\n      }\n    };\n\n    function point(lambda, phi) {\n      if (pointVisible(lambda, phi)) sink.point(lambda, phi);\n    }\n\n    function pointLine(lambda, phi) {\n      line.point(lambda, phi);\n    }\n\n    function lineStart() {\n      clip.point = pointLine;\n      line.lineStart();\n    }\n\n    function lineEnd() {\n      clip.point = point;\n      line.lineEnd();\n    }\n\n    function pointRing(lambda, phi) {\n      ring.push([lambda, phi]);\n      ringSink.point(lambda, phi);\n    }\n\n    function ringStart() {\n      ringSink.lineStart();\n      ring = [];\n    }\n\n    function ringEnd() {\n      pointRing(ring[0][0], ring[0][1]);\n      ringSink.lineEnd();\n\n      var clean = ringSink.clean(),\n          ringSegments = ringBuffer.result(),\n          i, n = ringSegments.length, m,\n          segment,\n          point;\n\n      ring.pop();\n      polygon.push(ring);\n      ring = null;\n\n      if (!n) return;\n\n      // No intersections.\n      if (clean & 1) {\n        segment = ringSegments[0];\n        if ((m = segment.length - 1) > 0) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          sink.lineStart();\n          for (i = 0; i < m; ++i) sink.point((point = segment[i])[0], point[1]);\n          sink.lineEnd();\n        }\n        return;\n      }\n\n      // Rejoin connected segments.\n      // TODO reuse ringBuffer.rejoin()?\n      if (n > 1 && clean & 2) ringSegments.push(ringSegments.pop().concat(ringSegments.shift()));\n\n      segments.push(ringSegments.filter(validSegment));\n    }\n\n    return clip;\n  };\n}\n\nfunction validSegment(segment) {\n  return segment.length > 1;\n}\n\n// Intersections are sorted along the clip edge. For both antimeridian cutting\n// and circle clipping, the same comparison is used.\nfunction compareIntersection(a, b) {\n  return ((a = a.x)[0] < 0 ? a[1] - _math_js__WEBPACK_IMPORTED_MODULE_4__.halfPi - _math_js__WEBPACK_IMPORTED_MODULE_4__.epsilon : _math_js__WEBPACK_IMPORTED_MODULE_4__.halfPi - a[1])\n       - ((b = b.x)[0] < 0 ? b[1] - _math_js__WEBPACK_IMPORTED_MODULE_4__.halfPi - _math_js__WEBPACK_IMPORTED_MODULE_4__.epsilon : _math_js__WEBPACK_IMPORTED_MODULE_4__.halfPi - b[1]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/clip/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/clip/line.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-geo/src/clip/line.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b, x0, y0, x1, y1) {\n  var ax = a[0],\n      ay = a[1],\n      bx = b[0],\n      by = b[1],\n      t0 = 0,\n      t1 = 1,\n      dx = bx - ax,\n      dy = by - ay,\n      r;\n\n  r = x0 - ax;\n  if (!dx && r > 0) return;\n  r /= dx;\n  if (dx < 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  } else if (dx > 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  }\n\n  r = x1 - ax;\n  if (!dx && r < 0) return;\n  r /= dx;\n  if (dx < 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  } else if (dx > 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  }\n\n  r = y0 - ay;\n  if (!dy && r > 0) return;\n  r /= dy;\n  if (dy < 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  } else if (dy > 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  }\n\n  r = y1 - ay;\n  if (!dy && r < 0) return;\n  r /= dy;\n  if (dy < 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  } else if (dy > 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  }\n\n  if (t0 > 0) a[0] = ax + t0 * dx, a[1] = ay + t0 * dy;\n  if (t1 < 1) b[0] = ax + t1 * dx, b[1] = ay + t1 * dy;\n  return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/clip/line.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/clip/rectangle.js":
/*!***************************************************!*\
  !*** ./node_modules/d3-geo/src/clip/rectangle.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ clipRectangle)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _buffer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./buffer.js */ \"(ssr)/./node_modules/d3-geo/src/clip/buffer.js\");\n/* harmony import */ var _line_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./line.js */ \"(ssr)/./node_modules/d3-geo/src/clip/line.js\");\n/* harmony import */ var _rejoin_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./rejoin.js */ \"(ssr)/./node_modules/d3-geo/src/clip/rejoin.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/merge.js\");\n\n\n\n\n\n\nvar clipMax = 1e9, clipMin = -clipMax;\n\n// TODO Use d3-polygon’s polygonContains here for the ring check?\n// TODO Eliminate duplicate buffering in clipBuffer and polygon.push?\n\nfunction clipRectangle(x0, y0, x1, y1) {\n\n  function visible(x, y) {\n    return x0 <= x && x <= x1 && y0 <= y && y <= y1;\n  }\n\n  function interpolate(from, to, direction, stream) {\n    var a = 0, a1 = 0;\n    if (from == null\n        || (a = corner(from, direction)) !== (a1 = corner(to, direction))\n        || comparePoint(from, to) < 0 ^ direction > 0) {\n      do stream.point(a === 0 || a === 3 ? x0 : x1, a > 1 ? y1 : y0);\n      while ((a = (a + direction + 4) % 4) !== a1);\n    } else {\n      stream.point(to[0], to[1]);\n    }\n  }\n\n  function corner(p, direction) {\n    return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(p[0] - x0) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon ? direction > 0 ? 0 : 3\n        : (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(p[0] - x1) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon ? direction > 0 ? 2 : 1\n        : (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(p[1] - y0) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon ? direction > 0 ? 1 : 0\n        : direction > 0 ? 3 : 2; // abs(p[1] - y1) < epsilon\n  }\n\n  function compareIntersection(a, b) {\n    return comparePoint(a.x, b.x);\n  }\n\n  function comparePoint(a, b) {\n    var ca = corner(a, 1),\n        cb = corner(b, 1);\n    return ca !== cb ? ca - cb\n        : ca === 0 ? b[1] - a[1]\n        : ca === 1 ? a[0] - b[0]\n        : ca === 2 ? a[1] - b[1]\n        : b[0] - a[0];\n  }\n\n  return function(stream) {\n    var activeStream = stream,\n        bufferStream = (0,_buffer_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(),\n        segments,\n        polygon,\n        ring,\n        x__, y__, v__, // first point\n        x_, y_, v_, // previous point\n        first,\n        clean;\n\n    var clipStream = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: polygonStart,\n      polygonEnd: polygonEnd\n    };\n\n    function point(x, y) {\n      if (visible(x, y)) activeStream.point(x, y);\n    }\n\n    function polygonInside() {\n      var winding = 0;\n\n      for (var i = 0, n = polygon.length; i < n; ++i) {\n        for (var ring = polygon[i], j = 1, m = ring.length, point = ring[0], a0, a1, b0 = point[0], b1 = point[1]; j < m; ++j) {\n          a0 = b0, a1 = b1, point = ring[j], b0 = point[0], b1 = point[1];\n          if (a1 <= y1) { if (b1 > y1 && (b0 - a0) * (y1 - a1) > (b1 - a1) * (x0 - a0)) ++winding; }\n          else { if (b1 <= y1 && (b0 - a0) * (y1 - a1) < (b1 - a1) * (x0 - a0)) --winding; }\n        }\n      }\n\n      return winding;\n    }\n\n    // Buffer geometry within a polygon and then clip it en masse.\n    function polygonStart() {\n      activeStream = bufferStream, segments = [], polygon = [], clean = true;\n    }\n\n    function polygonEnd() {\n      var startInside = polygonInside(),\n          cleanInside = clean && startInside,\n          visible = (segments = (0,d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(segments)).length;\n      if (cleanInside || visible) {\n        stream.polygonStart();\n        if (cleanInside) {\n          stream.lineStart();\n          interpolate(null, null, 1, stream);\n          stream.lineEnd();\n        }\n        if (visible) {\n          (0,_rejoin_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(segments, compareIntersection, startInside, interpolate, stream);\n        }\n        stream.polygonEnd();\n      }\n      activeStream = stream, segments = polygon = ring = null;\n    }\n\n    function lineStart() {\n      clipStream.point = linePoint;\n      if (polygon) polygon.push(ring = []);\n      first = true;\n      v_ = false;\n      x_ = y_ = NaN;\n    }\n\n    // TODO rather than special-case polygons, simply handle them separately.\n    // Ideally, coincident intersection points should be jittered to avoid\n    // clipping issues.\n    function lineEnd() {\n      if (segments) {\n        linePoint(x__, y__);\n        if (v__ && v_) bufferStream.rejoin();\n        segments.push(bufferStream.result());\n      }\n      clipStream.point = point;\n      if (v_) activeStream.lineEnd();\n    }\n\n    function linePoint(x, y) {\n      var v = visible(x, y);\n      if (polygon) ring.push([x, y]);\n      if (first) {\n        x__ = x, y__ = y, v__ = v;\n        first = false;\n        if (v) {\n          activeStream.lineStart();\n          activeStream.point(x, y);\n        }\n      } else {\n        if (v && v_) activeStream.point(x, y);\n        else {\n          var a = [x_ = Math.max(clipMin, Math.min(clipMax, x_)), y_ = Math.max(clipMin, Math.min(clipMax, y_))],\n              b = [x = Math.max(clipMin, Math.min(clipMax, x)), y = Math.max(clipMin, Math.min(clipMax, y))];\n          if ((0,_line_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(a, b, x0, y0, x1, y1)) {\n            if (!v_) {\n              activeStream.lineStart();\n              activeStream.point(a[0], a[1]);\n            }\n            activeStream.point(b[0], b[1]);\n            if (!v) activeStream.lineEnd();\n            clean = false;\n          } else if (v) {\n            activeStream.lineStart();\n            activeStream.point(x, y);\n            clean = false;\n          }\n        }\n      }\n      x_ = x, y_ = y, v_ = v;\n    }\n\n    return clipStream;\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/clip/rectangle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/clip/rejoin.js":
/*!************************************************!*\
  !*** ./node_modules/d3-geo/src/clip/rejoin.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _pointEqual_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../pointEqual.js */ \"(ssr)/./node_modules/d3-geo/src/pointEqual.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\n\nfunction Intersection(point, points, other, entry) {\n  this.x = point;\n  this.z = points;\n  this.o = other; // another intersection\n  this.e = entry; // is an entry?\n  this.v = false; // visited\n  this.n = this.p = null; // next & previous\n}\n\n// A generalized polygon clipping algorithm: given a polygon that has been cut\n// into its visible line segments, and rejoins the segments by interpolating\n// along the clip edge.\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(segments, compareIntersection, startInside, interpolate, stream) {\n  var subject = [],\n      clip = [],\n      i,\n      n;\n\n  segments.forEach(function(segment) {\n    if ((n = segment.length - 1) <= 0) return;\n    var n, p0 = segment[0], p1 = segment[n], x;\n\n    if ((0,_pointEqual_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(p0, p1)) {\n      if (!p0[2] && !p1[2]) {\n        stream.lineStart();\n        for (i = 0; i < n; ++i) stream.point((p0 = segment[i])[0], p0[1]);\n        stream.lineEnd();\n        return;\n      }\n      // handle degenerate cases by moving the point\n      p1[0] += 2 * _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon;\n    }\n\n    subject.push(x = new Intersection(p0, segment, null, true));\n    clip.push(x.o = new Intersection(p0, null, x, false));\n    subject.push(x = new Intersection(p1, segment, null, false));\n    clip.push(x.o = new Intersection(p1, null, x, true));\n  });\n\n  if (!subject.length) return;\n\n  clip.sort(compareIntersection);\n  link(subject);\n  link(clip);\n\n  for (i = 0, n = clip.length; i < n; ++i) {\n    clip[i].e = startInside = !startInside;\n  }\n\n  var start = subject[0],\n      points,\n      point;\n\n  while (1) {\n    // Find first unvisited intersection.\n    var current = start,\n        isSubject = true;\n    while (current.v) if ((current = current.n) === start) return;\n    points = current.z;\n    stream.lineStart();\n    do {\n      current.v = current.o.v = true;\n      if (current.e) {\n        if (isSubject) {\n          for (i = 0, n = points.length; i < n; ++i) stream.point((point = points[i])[0], point[1]);\n        } else {\n          interpolate(current.x, current.n.x, 1, stream);\n        }\n        current = current.n;\n      } else {\n        if (isSubject) {\n          points = current.p.z;\n          for (i = points.length - 1; i >= 0; --i) stream.point((point = points[i])[0], point[1]);\n        } else {\n          interpolate(current.x, current.p.x, -1, stream);\n        }\n        current = current.p;\n      }\n      current = current.o;\n      points = current.z;\n      isSubject = !isSubject;\n    } while (!current.v);\n    stream.lineEnd();\n  }\n}\n\nfunction link(array) {\n  if (!(n = array.length)) return;\n  var n,\n      i = 0,\n      a = array[0],\n      b;\n  while (++i < n) {\n    a.n = b = array[i];\n    b.p = a;\n    a = b;\n  }\n  a.n = b = array[0];\n  b.p = a;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/clip/rejoin.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/compose.js":
/*!********************************************!*\
  !*** ./node_modules/d3-geo/src/compose.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n\n  function compose(x, y) {\n    return x = a(x, y), b(x[0], x[1]);\n  }\n\n  if (a.invert && b.invert) compose.invert = function(x, y) {\n    return x = b.invert(x, y), x && a.invert(x[0], x[1]);\n  };\n\n  return compose;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9jb21wb3NlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxvQ0FBUzs7QUFFeEI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGQzLWdlb1xcc3JjXFxjb21wb3NlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKGEsIGIpIHtcblxuICBmdW5jdGlvbiBjb21wb3NlKHgsIHkpIHtcbiAgICByZXR1cm4geCA9IGEoeCwgeSksIGIoeFswXSwgeFsxXSk7XG4gIH1cblxuICBpZiAoYS5pbnZlcnQgJiYgYi5pbnZlcnQpIGNvbXBvc2UuaW52ZXJ0ID0gZnVuY3Rpb24oeCwgeSkge1xuICAgIHJldHVybiB4ID0gYi5pbnZlcnQoeCwgeSksIHggJiYgYS5pbnZlcnQoeFswXSwgeFsxXSk7XG4gIH07XG5cbiAgcmV0dXJuIGNvbXBvc2U7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/compose.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/constant.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-geo/src/constant.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x) {\n  return function() {\n    return x;\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9jb25zdGFudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVM7QUFDeEI7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtZ2VvXFxzcmNcXGNvbnN0YW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHgpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uKCkge1xuICAgIHJldHVybiB4O1xuICB9O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/constant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/contains.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-geo/src/contains.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _polygonContains_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./polygonContains.js */ \"(ssr)/./node_modules/d3-geo/src/polygonContains.js\");\n/* harmony import */ var _distance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./distance.js */ \"(ssr)/./node_modules/d3-geo/src/distance.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\n\n\nvar containsObjectType = {\n  Feature: function(object, point) {\n    return containsGeometry(object.geometry, point);\n  },\n  FeatureCollection: function(object, point) {\n    var features = object.features, i = -1, n = features.length;\n    while (++i < n) if (containsGeometry(features[i].geometry, point)) return true;\n    return false;\n  }\n};\n\nvar containsGeometryType = {\n  Sphere: function() {\n    return true;\n  },\n  Point: function(object, point) {\n    return containsPoint(object.coordinates, point);\n  },\n  MultiPoint: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsPoint(coordinates[i], point)) return true;\n    return false;\n  },\n  LineString: function(object, point) {\n    return containsLine(object.coordinates, point);\n  },\n  MultiLineString: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsLine(coordinates[i], point)) return true;\n    return false;\n  },\n  Polygon: function(object, point) {\n    return containsPolygon(object.coordinates, point);\n  },\n  MultiPolygon: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsPolygon(coordinates[i], point)) return true;\n    return false;\n  },\n  GeometryCollection: function(object, point) {\n    var geometries = object.geometries, i = -1, n = geometries.length;\n    while (++i < n) if (containsGeometry(geometries[i], point)) return true;\n    return false;\n  }\n};\n\nfunction containsGeometry(geometry, point) {\n  return geometry && containsGeometryType.hasOwnProperty(geometry.type)\n      ? containsGeometryType[geometry.type](geometry, point)\n      : false;\n}\n\nfunction containsPoint(coordinates, point) {\n  return (0,_distance_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(coordinates, point) === 0;\n}\n\nfunction containsLine(coordinates, point) {\n  var ao, bo, ab;\n  for (var i = 0, n = coordinates.length; i < n; i++) {\n    bo = (0,_distance_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(coordinates[i], point);\n    if (bo === 0) return true;\n    if (i > 0) {\n      ab = (0,_distance_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(coordinates[i], coordinates[i - 1]);\n      if (\n        ab > 0 &&\n        ao <= ab &&\n        bo <= ab &&\n        (ao + bo - ab) * (1 - Math.pow((ao - bo) / ab, 2)) < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon2 * ab\n      )\n        return true;\n    }\n    ao = bo;\n  }\n  return false;\n}\n\nfunction containsPolygon(coordinates, point) {\n  return !!(0,_polygonContains_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(coordinates.map(ringRadians), pointRadians(point));\n}\n\nfunction ringRadians(ring) {\n  return ring = ring.map(pointRadians), ring.pop(), ring;\n}\n\nfunction pointRadians(point) {\n  return [point[0] * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, point[1] * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians];\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(object, point) {\n  return (object && containsObjectType.hasOwnProperty(object.type)\n      ? containsObjectType[object.type]\n      : containsGeometry)(object, point);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/contains.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/distance.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-geo/src/distance.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _length_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./length.js */ \"(ssr)/./node_modules/d3-geo/src/length.js\");\n\n\nvar coordinates = [null, null],\n    object = {type: \"LineString\", coordinates: coordinates};\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n  coordinates[0] = a;\n  coordinates[1] = b;\n  return (0,_length_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9kaXN0YW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpQzs7QUFFakM7QUFDQSxjQUFjOztBQUVkLDZCQUFlLG9DQUFTO0FBQ3hCO0FBQ0E7QUFDQSxTQUFTLHNEQUFNO0FBQ2YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtZ2VvXFxzcmNcXGRpc3RhbmNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBsZW5ndGggZnJvbSBcIi4vbGVuZ3RoLmpzXCI7XG5cbnZhciBjb29yZGluYXRlcyA9IFtudWxsLCBudWxsXSxcbiAgICBvYmplY3QgPSB7dHlwZTogXCJMaW5lU3RyaW5nXCIsIGNvb3JkaW5hdGVzOiBjb29yZGluYXRlc307XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKGEsIGIpIHtcbiAgY29vcmRpbmF0ZXNbMF0gPSBhO1xuICBjb29yZGluYXRlc1sxXSA9IGI7XG4gIHJldHVybiBsZW5ndGgob2JqZWN0KTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/distance.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/graticule.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-geo/src/graticule.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ graticule),\n/* harmony export */   graticule10: () => (/* binding */ graticule10)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/range.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\n\nfunction graticuleX(y0, y1, dy) {\n  var y = (0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(y0, y1 - _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon, dy).concat(y1);\n  return function(x) { return y.map(function(y) { return [x, y]; }); };\n}\n\nfunction graticuleY(x0, x1, dx) {\n  var x = (0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(x0, x1 - _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon, dx).concat(x1);\n  return function(y) { return x.map(function(x) { return [x, y]; }); };\n}\n\nfunction graticule() {\n  var x1, x0, X1, X0,\n      y1, y0, Y1, Y0,\n      dx = 10, dy = dx, DX = 90, DY = 360,\n      x, y, X, Y,\n      precision = 2.5;\n\n  function graticule() {\n    return {type: \"MultiLineString\", coordinates: lines()};\n  }\n\n  function lines() {\n    return (0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.ceil)(X0 / DX) * DX, X1, DX).map(X)\n        .concat((0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.ceil)(Y0 / DY) * DY, Y1, DY).map(Y))\n        .concat((0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.ceil)(x0 / dx) * dx, x1, dx).filter(function(x) { return (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(x % DX) > _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon; }).map(x))\n        .concat((0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.ceil)(y0 / dy) * dy, y1, dy).filter(function(y) { return (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(y % DY) > _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon; }).map(y));\n  }\n\n  graticule.lines = function() {\n    return lines().map(function(coordinates) { return {type: \"LineString\", coordinates: coordinates}; });\n  };\n\n  graticule.outline = function() {\n    return {\n      type: \"Polygon\",\n      coordinates: [\n        X(X0).concat(\n        Y(Y1).slice(1),\n        X(X1).reverse().slice(1),\n        Y(Y0).reverse().slice(1))\n      ]\n    };\n  };\n\n  graticule.extent = function(_) {\n    if (!arguments.length) return graticule.extentMinor();\n    return graticule.extentMajor(_).extentMinor(_);\n  };\n\n  graticule.extentMajor = function(_) {\n    if (!arguments.length) return [[X0, Y0], [X1, Y1]];\n    X0 = +_[0][0], X1 = +_[1][0];\n    Y0 = +_[0][1], Y1 = +_[1][1];\n    if (X0 > X1) _ = X0, X0 = X1, X1 = _;\n    if (Y0 > Y1) _ = Y0, Y0 = Y1, Y1 = _;\n    return graticule.precision(precision);\n  };\n\n  graticule.extentMinor = function(_) {\n    if (!arguments.length) return [[x0, y0], [x1, y1]];\n    x0 = +_[0][0], x1 = +_[1][0];\n    y0 = +_[0][1], y1 = +_[1][1];\n    if (x0 > x1) _ = x0, x0 = x1, x1 = _;\n    if (y0 > y1) _ = y0, y0 = y1, y1 = _;\n    return graticule.precision(precision);\n  };\n\n  graticule.step = function(_) {\n    if (!arguments.length) return graticule.stepMinor();\n    return graticule.stepMajor(_).stepMinor(_);\n  };\n\n  graticule.stepMajor = function(_) {\n    if (!arguments.length) return [DX, DY];\n    DX = +_[0], DY = +_[1];\n    return graticule;\n  };\n\n  graticule.stepMinor = function(_) {\n    if (!arguments.length) return [dx, dy];\n    dx = +_[0], dy = +_[1];\n    return graticule;\n  };\n\n  graticule.precision = function(_) {\n    if (!arguments.length) return precision;\n    precision = +_;\n    x = graticuleX(y0, y1, 90);\n    y = graticuleY(x0, x1, precision);\n    X = graticuleX(Y0, Y1, 90);\n    Y = graticuleY(X0, X1, precision);\n    return graticule;\n  };\n\n  return graticule\n      .extentMajor([[-180, -90 + _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon], [180, 90 - _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon]])\n      .extentMinor([[-180, -80 - _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon], [180, 80 + _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon]]);\n}\n\nfunction graticule10() {\n  return graticule()();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9ncmF0aWN1bGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUErQjtBQUNjOztBQUU3QztBQUNBLFVBQVUsb0RBQUssVUFBVSw2Q0FBTztBQUNoQyx1QkFBdUIsMkJBQTJCLGdCQUFnQjtBQUNsRTs7QUFFQTtBQUNBLFVBQVUsb0RBQUssVUFBVSw2Q0FBTztBQUNoQyx1QkFBdUIsMkJBQTJCLGdCQUFnQjtBQUNsRTs7QUFFZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1o7O0FBRUE7QUFDQSxXQUFXLG9EQUFLLENBQUMsOENBQUk7QUFDckIsZ0JBQWdCLG9EQUFLLENBQUMsOENBQUk7QUFDMUIsZ0JBQWdCLG9EQUFLLENBQUMsOENBQUksNkNBQTZDLE9BQU8sNkNBQUcsV0FBVyw2Q0FBTyxHQUFHO0FBQ3RHLGdCQUFnQixvREFBSyxDQUFDLDhDQUFJLDZDQUE2QyxPQUFPLDZDQUFHLFdBQVcsNkNBQU8sR0FBRztBQUN0Rzs7QUFFQTtBQUNBLCtDQUErQyxRQUFRLGdEQUFnRDtBQUN2Rzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsaUNBQWlDLDZDQUFPLGNBQWMsNkNBQU87QUFDN0QsaUNBQWlDLDZDQUFPLGNBQWMsNkNBQU87QUFDN0Q7O0FBRU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGQzLWdlb1xcc3JjXFxncmF0aWN1bGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtyYW5nZX0gZnJvbSBcImQzLWFycmF5XCI7XG5pbXBvcnQge2FicywgY2VpbCwgZXBzaWxvbn0gZnJvbSBcIi4vbWF0aC5qc1wiO1xuXG5mdW5jdGlvbiBncmF0aWN1bGVYKHkwLCB5MSwgZHkpIHtcbiAgdmFyIHkgPSByYW5nZSh5MCwgeTEgLSBlcHNpbG9uLCBkeSkuY29uY2F0KHkxKTtcbiAgcmV0dXJuIGZ1bmN0aW9uKHgpIHsgcmV0dXJuIHkubWFwKGZ1bmN0aW9uKHkpIHsgcmV0dXJuIFt4LCB5XTsgfSk7IH07XG59XG5cbmZ1bmN0aW9uIGdyYXRpY3VsZVkoeDAsIHgxLCBkeCkge1xuICB2YXIgeCA9IHJhbmdlKHgwLCB4MSAtIGVwc2lsb24sIGR4KS5jb25jYXQoeDEpO1xuICByZXR1cm4gZnVuY3Rpb24oeSkgeyByZXR1cm4geC5tYXAoZnVuY3Rpb24oeCkgeyByZXR1cm4gW3gsIHldOyB9KTsgfTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZ3JhdGljdWxlKCkge1xuICB2YXIgeDEsIHgwLCBYMSwgWDAsXG4gICAgICB5MSwgeTAsIFkxLCBZMCxcbiAgICAgIGR4ID0gMTAsIGR5ID0gZHgsIERYID0gOTAsIERZID0gMzYwLFxuICAgICAgeCwgeSwgWCwgWSxcbiAgICAgIHByZWNpc2lvbiA9IDIuNTtcblxuICBmdW5jdGlvbiBncmF0aWN1bGUoKSB7XG4gICAgcmV0dXJuIHt0eXBlOiBcIk11bHRpTGluZVN0cmluZ1wiLCBjb29yZGluYXRlczogbGluZXMoKX07XG4gIH1cblxuICBmdW5jdGlvbiBsaW5lcygpIHtcbiAgICByZXR1cm4gcmFuZ2UoY2VpbChYMCAvIERYKSAqIERYLCBYMSwgRFgpLm1hcChYKVxuICAgICAgICAuY29uY2F0KHJhbmdlKGNlaWwoWTAgLyBEWSkgKiBEWSwgWTEsIERZKS5tYXAoWSkpXG4gICAgICAgIC5jb25jYXQocmFuZ2UoY2VpbCh4MCAvIGR4KSAqIGR4LCB4MSwgZHgpLmZpbHRlcihmdW5jdGlvbih4KSB7IHJldHVybiBhYnMoeCAlIERYKSA+IGVwc2lsb247IH0pLm1hcCh4KSlcbiAgICAgICAgLmNvbmNhdChyYW5nZShjZWlsKHkwIC8gZHkpICogZHksIHkxLCBkeSkuZmlsdGVyKGZ1bmN0aW9uKHkpIHsgcmV0dXJuIGFicyh5ICUgRFkpID4gZXBzaWxvbjsgfSkubWFwKHkpKTtcbiAgfVxuXG4gIGdyYXRpY3VsZS5saW5lcyA9IGZ1bmN0aW9uKCkge1xuICAgIHJldHVybiBsaW5lcygpLm1hcChmdW5jdGlvbihjb29yZGluYXRlcykgeyByZXR1cm4ge3R5cGU6IFwiTGluZVN0cmluZ1wiLCBjb29yZGluYXRlczogY29vcmRpbmF0ZXN9OyB9KTtcbiAgfTtcblxuICBncmF0aWN1bGUub3V0bGluZSA9IGZ1bmN0aW9uKCkge1xuICAgIHJldHVybiB7XG4gICAgICB0eXBlOiBcIlBvbHlnb25cIixcbiAgICAgIGNvb3JkaW5hdGVzOiBbXG4gICAgICAgIFgoWDApLmNvbmNhdChcbiAgICAgICAgWShZMSkuc2xpY2UoMSksXG4gICAgICAgIFgoWDEpLnJldmVyc2UoKS5zbGljZSgxKSxcbiAgICAgICAgWShZMCkucmV2ZXJzZSgpLnNsaWNlKDEpKVxuICAgICAgXVxuICAgIH07XG4gIH07XG5cbiAgZ3JhdGljdWxlLmV4dGVudCA9IGZ1bmN0aW9uKF8pIHtcbiAgICBpZiAoIWFyZ3VtZW50cy5sZW5ndGgpIHJldHVybiBncmF0aWN1bGUuZXh0ZW50TWlub3IoKTtcbiAgICByZXR1cm4gZ3JhdGljdWxlLmV4dGVudE1ham9yKF8pLmV4dGVudE1pbm9yKF8pO1xuICB9O1xuXG4gIGdyYXRpY3VsZS5leHRlbnRNYWpvciA9IGZ1bmN0aW9uKF8pIHtcbiAgICBpZiAoIWFyZ3VtZW50cy5sZW5ndGgpIHJldHVybiBbW1gwLCBZMF0sIFtYMSwgWTFdXTtcbiAgICBYMCA9ICtfWzBdWzBdLCBYMSA9ICtfWzFdWzBdO1xuICAgIFkwID0gK19bMF1bMV0sIFkxID0gK19bMV1bMV07XG4gICAgaWYgKFgwID4gWDEpIF8gPSBYMCwgWDAgPSBYMSwgWDEgPSBfO1xuICAgIGlmIChZMCA+IFkxKSBfID0gWTAsIFkwID0gWTEsIFkxID0gXztcbiAgICByZXR1cm4gZ3JhdGljdWxlLnByZWNpc2lvbihwcmVjaXNpb24pO1xuICB9O1xuXG4gIGdyYXRpY3VsZS5leHRlbnRNaW5vciA9IGZ1bmN0aW9uKF8pIHtcbiAgICBpZiAoIWFyZ3VtZW50cy5sZW5ndGgpIHJldHVybiBbW3gwLCB5MF0sIFt4MSwgeTFdXTtcbiAgICB4MCA9ICtfWzBdWzBdLCB4MSA9ICtfWzFdWzBdO1xuICAgIHkwID0gK19bMF1bMV0sIHkxID0gK19bMV1bMV07XG4gICAgaWYgKHgwID4geDEpIF8gPSB4MCwgeDAgPSB4MSwgeDEgPSBfO1xuICAgIGlmICh5MCA+IHkxKSBfID0geTAsIHkwID0geTEsIHkxID0gXztcbiAgICByZXR1cm4gZ3JhdGljdWxlLnByZWNpc2lvbihwcmVjaXNpb24pO1xuICB9O1xuXG4gIGdyYXRpY3VsZS5zdGVwID0gZnVuY3Rpb24oXykge1xuICAgIGlmICghYXJndW1lbnRzLmxlbmd0aCkgcmV0dXJuIGdyYXRpY3VsZS5zdGVwTWlub3IoKTtcbiAgICByZXR1cm4gZ3JhdGljdWxlLnN0ZXBNYWpvcihfKS5zdGVwTWlub3IoXyk7XG4gIH07XG5cbiAgZ3JhdGljdWxlLnN0ZXBNYWpvciA9IGZ1bmN0aW9uKF8pIHtcbiAgICBpZiAoIWFyZ3VtZW50cy5sZW5ndGgpIHJldHVybiBbRFgsIERZXTtcbiAgICBEWCA9ICtfWzBdLCBEWSA9ICtfWzFdO1xuICAgIHJldHVybiBncmF0aWN1bGU7XG4gIH07XG5cbiAgZ3JhdGljdWxlLnN0ZXBNaW5vciA9IGZ1bmN0aW9uKF8pIHtcbiAgICBpZiAoIWFyZ3VtZW50cy5sZW5ndGgpIHJldHVybiBbZHgsIGR5XTtcbiAgICBkeCA9ICtfWzBdLCBkeSA9ICtfWzFdO1xuICAgIHJldHVybiBncmF0aWN1bGU7XG4gIH07XG5cbiAgZ3JhdGljdWxlLnByZWNpc2lvbiA9IGZ1bmN0aW9uKF8pIHtcbiAgICBpZiAoIWFyZ3VtZW50cy5sZW5ndGgpIHJldHVybiBwcmVjaXNpb247XG4gICAgcHJlY2lzaW9uID0gK187XG4gICAgeCA9IGdyYXRpY3VsZVgoeTAsIHkxLCA5MCk7XG4gICAgeSA9IGdyYXRpY3VsZVkoeDAsIHgxLCBwcmVjaXNpb24pO1xuICAgIFggPSBncmF0aWN1bGVYKFkwLCBZMSwgOTApO1xuICAgIFkgPSBncmF0aWN1bGVZKFgwLCBYMSwgcHJlY2lzaW9uKTtcbiAgICByZXR1cm4gZ3JhdGljdWxlO1xuICB9O1xuXG4gIHJldHVybiBncmF0aWN1bGVcbiAgICAgIC5leHRlbnRNYWpvcihbWy0xODAsIC05MCArIGVwc2lsb25dLCBbMTgwLCA5MCAtIGVwc2lsb25dXSlcbiAgICAgIC5leHRlbnRNaW5vcihbWy0xODAsIC04MCAtIGVwc2lsb25dLCBbMTgwLCA4MCArIGVwc2lsb25dXSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBncmF0aWN1bGUxMCgpIHtcbiAgcmV0dXJuIGdyYXRpY3VsZSgpKCk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/graticule.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/identity.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-geo/src/identity.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (x => x);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9pZGVudGl0eS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsTUFBTSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGQzLWdlb1xcc3JjXFxpZGVudGl0eS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB4ID0+IHg7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/identity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/index.js":
/*!******************************************!*\
  !*** ./node_modules/d3-geo/src/index.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   geoAlbers: () => (/* reexport safe */ _projection_albers_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"]),\n/* harmony export */   geoAlbersUsa: () => (/* reexport safe */ _projection_albersUsa_js__WEBPACK_IMPORTED_MODULE_15__[\"default\"]),\n/* harmony export */   geoArea: () => (/* reexport safe */ _area_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   geoAzimuthalEqualArea: () => (/* reexport safe */ _projection_azimuthalEqualArea_js__WEBPACK_IMPORTED_MODULE_16__[\"default\"]),\n/* harmony export */   geoAzimuthalEqualAreaRaw: () => (/* reexport safe */ _projection_azimuthalEqualArea_js__WEBPACK_IMPORTED_MODULE_16__.azimuthalEqualAreaRaw),\n/* harmony export */   geoAzimuthalEquidistant: () => (/* reexport safe */ _projection_azimuthalEquidistant_js__WEBPACK_IMPORTED_MODULE_17__[\"default\"]),\n/* harmony export */   geoAzimuthalEquidistantRaw: () => (/* reexport safe */ _projection_azimuthalEquidistant_js__WEBPACK_IMPORTED_MODULE_17__.azimuthalEquidistantRaw),\n/* harmony export */   geoBounds: () => (/* reexport safe */ _bounds_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   geoCentroid: () => (/* reexport safe */ _centroid_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   geoCircle: () => (/* reexport safe */ _circle_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   geoClipAntimeridian: () => (/* reexport safe */ _clip_antimeridian_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   geoClipCircle: () => (/* reexport safe */ _clip_circle_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   geoClipExtent: () => (/* reexport safe */ _clip_extent_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   geoClipRectangle: () => (/* reexport safe */ _clip_rectangle_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   geoConicConformal: () => (/* reexport safe */ _projection_conicConformal_js__WEBPACK_IMPORTED_MODULE_18__[\"default\"]),\n/* harmony export */   geoConicConformalRaw: () => (/* reexport safe */ _projection_conicConformal_js__WEBPACK_IMPORTED_MODULE_18__.conicConformalRaw),\n/* harmony export */   geoConicEqualArea: () => (/* reexport safe */ _projection_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_19__[\"default\"]),\n/* harmony export */   geoConicEqualAreaRaw: () => (/* reexport safe */ _projection_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_19__.conicEqualAreaRaw),\n/* harmony export */   geoConicEquidistant: () => (/* reexport safe */ _projection_conicEquidistant_js__WEBPACK_IMPORTED_MODULE_20__[\"default\"]),\n/* harmony export */   geoConicEquidistantRaw: () => (/* reexport safe */ _projection_conicEquidistant_js__WEBPACK_IMPORTED_MODULE_20__.conicEquidistantRaw),\n/* harmony export */   geoContains: () => (/* reexport safe */ _contains_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   geoDistance: () => (/* reexport safe */ _distance_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   geoEqualEarth: () => (/* reexport safe */ _projection_equalEarth_js__WEBPACK_IMPORTED_MODULE_21__[\"default\"]),\n/* harmony export */   geoEqualEarthRaw: () => (/* reexport safe */ _projection_equalEarth_js__WEBPACK_IMPORTED_MODULE_21__.equalEarthRaw),\n/* harmony export */   geoEquirectangular: () => (/* reexport safe */ _projection_equirectangular_js__WEBPACK_IMPORTED_MODULE_22__[\"default\"]),\n/* harmony export */   geoEquirectangularRaw: () => (/* reexport safe */ _projection_equirectangular_js__WEBPACK_IMPORTED_MODULE_22__.equirectangularRaw),\n/* harmony export */   geoGnomonic: () => (/* reexport safe */ _projection_gnomonic_js__WEBPACK_IMPORTED_MODULE_23__[\"default\"]),\n/* harmony export */   geoGnomonicRaw: () => (/* reexport safe */ _projection_gnomonic_js__WEBPACK_IMPORTED_MODULE_23__.gnomonicRaw),\n/* harmony export */   geoGraticule: () => (/* reexport safe */ _graticule_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   geoGraticule10: () => (/* reexport safe */ _graticule_js__WEBPACK_IMPORTED_MODULE_10__.graticule10),\n/* harmony export */   geoIdentity: () => (/* reexport safe */ _projection_identity_js__WEBPACK_IMPORTED_MODULE_24__[\"default\"]),\n/* harmony export */   geoInterpolate: () => (/* reexport safe */ _interpolate_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   geoLength: () => (/* reexport safe */ _length_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   geoMercator: () => (/* reexport safe */ _projection_mercator_js__WEBPACK_IMPORTED_MODULE_26__[\"default\"]),\n/* harmony export */   geoMercatorRaw: () => (/* reexport safe */ _projection_mercator_js__WEBPACK_IMPORTED_MODULE_26__.mercatorRaw),\n/* harmony export */   geoNaturalEarth1: () => (/* reexport safe */ _projection_naturalEarth1_js__WEBPACK_IMPORTED_MODULE_27__[\"default\"]),\n/* harmony export */   geoNaturalEarth1Raw: () => (/* reexport safe */ _projection_naturalEarth1_js__WEBPACK_IMPORTED_MODULE_27__.naturalEarth1Raw),\n/* harmony export */   geoOrthographic: () => (/* reexport safe */ _projection_orthographic_js__WEBPACK_IMPORTED_MODULE_28__[\"default\"]),\n/* harmony export */   geoOrthographicRaw: () => (/* reexport safe */ _projection_orthographic_js__WEBPACK_IMPORTED_MODULE_28__.orthographicRaw),\n/* harmony export */   geoPath: () => (/* reexport safe */ _path_index_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   geoProjection: () => (/* reexport safe */ _projection_index_js__WEBPACK_IMPORTED_MODULE_25__[\"default\"]),\n/* harmony export */   geoProjectionMutator: () => (/* reexport safe */ _projection_index_js__WEBPACK_IMPORTED_MODULE_25__.projectionMutator),\n/* harmony export */   geoRotation: () => (/* reexport safe */ _rotation_js__WEBPACK_IMPORTED_MODULE_31__[\"default\"]),\n/* harmony export */   geoStereographic: () => (/* reexport safe */ _projection_stereographic_js__WEBPACK_IMPORTED_MODULE_29__[\"default\"]),\n/* harmony export */   geoStereographicRaw: () => (/* reexport safe */ _projection_stereographic_js__WEBPACK_IMPORTED_MODULE_29__.stereographicRaw),\n/* harmony export */   geoStream: () => (/* reexport safe */ _stream_js__WEBPACK_IMPORTED_MODULE_32__[\"default\"]),\n/* harmony export */   geoTransform: () => (/* reexport safe */ _transform_js__WEBPACK_IMPORTED_MODULE_33__[\"default\"]),\n/* harmony export */   geoTransverseMercator: () => (/* reexport safe */ _projection_transverseMercator_js__WEBPACK_IMPORTED_MODULE_30__[\"default\"]),\n/* harmony export */   geoTransverseMercatorRaw: () => (/* reexport safe */ _projection_transverseMercator_js__WEBPACK_IMPORTED_MODULE_30__.transverseMercatorRaw)\n/* harmony export */ });\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./area.js */ \"(ssr)/./node_modules/d3-geo/src/area.js\");\n/* harmony import */ var _bounds_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./bounds.js */ \"(ssr)/./node_modules/d3-geo/src/bounds.js\");\n/* harmony import */ var _centroid_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./centroid.js */ \"(ssr)/./node_modules/d3-geo/src/centroid.js\");\n/* harmony import */ var _circle_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./circle.js */ \"(ssr)/./node_modules/d3-geo/src/circle.js\");\n/* harmony import */ var _clip_antimeridian_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./clip/antimeridian.js */ \"(ssr)/./node_modules/d3-geo/src/clip/antimeridian.js\");\n/* harmony import */ var _clip_circle_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./clip/circle.js */ \"(ssr)/./node_modules/d3-geo/src/clip/circle.js\");\n/* harmony import */ var _clip_extent_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./clip/extent.js */ \"(ssr)/./node_modules/d3-geo/src/clip/extent.js\");\n/* harmony import */ var _clip_rectangle_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./clip/rectangle.js */ \"(ssr)/./node_modules/d3-geo/src/clip/rectangle.js\");\n/* harmony import */ var _contains_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./contains.js */ \"(ssr)/./node_modules/d3-geo/src/contains.js\");\n/* harmony import */ var _distance_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./distance.js */ \"(ssr)/./node_modules/d3-geo/src/distance.js\");\n/* harmony import */ var _graticule_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./graticule.js */ \"(ssr)/./node_modules/d3-geo/src/graticule.js\");\n/* harmony import */ var _interpolate_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./interpolate.js */ \"(ssr)/./node_modules/d3-geo/src/interpolate.js\");\n/* harmony import */ var _length_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./length.js */ \"(ssr)/./node_modules/d3-geo/src/length.js\");\n/* harmony import */ var _path_index_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./path/index.js */ \"(ssr)/./node_modules/d3-geo/src/path/index.js\");\n/* harmony import */ var _projection_albers_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./projection/albers.js */ \"(ssr)/./node_modules/d3-geo/src/projection/albers.js\");\n/* harmony import */ var _projection_albersUsa_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./projection/albersUsa.js */ \"(ssr)/./node_modules/d3-geo/src/projection/albersUsa.js\");\n/* harmony import */ var _projection_azimuthalEqualArea_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./projection/azimuthalEqualArea.js */ \"(ssr)/./node_modules/d3-geo/src/projection/azimuthalEqualArea.js\");\n/* harmony import */ var _projection_azimuthalEquidistant_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./projection/azimuthalEquidistant.js */ \"(ssr)/./node_modules/d3-geo/src/projection/azimuthalEquidistant.js\");\n/* harmony import */ var _projection_conicConformal_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./projection/conicConformal.js */ \"(ssr)/./node_modules/d3-geo/src/projection/conicConformal.js\");\n/* harmony import */ var _projection_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./projection/conicEqualArea.js */ \"(ssr)/./node_modules/d3-geo/src/projection/conicEqualArea.js\");\n/* harmony import */ var _projection_conicEquidistant_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./projection/conicEquidistant.js */ \"(ssr)/./node_modules/d3-geo/src/projection/conicEquidistant.js\");\n/* harmony import */ var _projection_equalEarth_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./projection/equalEarth.js */ \"(ssr)/./node_modules/d3-geo/src/projection/equalEarth.js\");\n/* harmony import */ var _projection_equirectangular_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./projection/equirectangular.js */ \"(ssr)/./node_modules/d3-geo/src/projection/equirectangular.js\");\n/* harmony import */ var _projection_gnomonic_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./projection/gnomonic.js */ \"(ssr)/./node_modules/d3-geo/src/projection/gnomonic.js\");\n/* harmony import */ var _projection_identity_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./projection/identity.js */ \"(ssr)/./node_modules/d3-geo/src/projection/identity.js\");\n/* harmony import */ var _projection_index_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./projection/index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n/* harmony import */ var _projection_mercator_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./projection/mercator.js */ \"(ssr)/./node_modules/d3-geo/src/projection/mercator.js\");\n/* harmony import */ var _projection_naturalEarth1_js__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./projection/naturalEarth1.js */ \"(ssr)/./node_modules/d3-geo/src/projection/naturalEarth1.js\");\n/* harmony import */ var _projection_orthographic_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./projection/orthographic.js */ \"(ssr)/./node_modules/d3-geo/src/projection/orthographic.js\");\n/* harmony import */ var _projection_stereographic_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./projection/stereographic.js */ \"(ssr)/./node_modules/d3-geo/src/projection/stereographic.js\");\n/* harmony import */ var _projection_transverseMercator_js__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./projection/transverseMercator.js */ \"(ssr)/./node_modules/d3-geo/src/projection/transverseMercator.js\");\n/* harmony import */ var _rotation_js__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./rotation.js */ \"(ssr)/./node_modules/d3-geo/src/rotation.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./stream.js */ \"(ssr)/./node_modules/d3-geo/src/stream.js\");\n/* harmony import */ var _transform_js__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./transform.js */ \"(ssr)/./node_modules/d3-geo/src/transform.js\");\n\n\n\n\n\n\n // DEPRECATED! Use d3.geoIdentity().clipExtent(…).\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/interpolate.js":
/*!************************************************!*\
  !*** ./node_modules/d3-geo/src/interpolate.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n  var x0 = a[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians,\n      y0 = a[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians,\n      x1 = b[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians,\n      y1 = b[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians,\n      cy0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y0),\n      sy0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y0),\n      cy1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y1),\n      sy1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y1),\n      kx0 = cy0 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x0),\n      ky0 = cy0 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x0),\n      kx1 = cy1 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x1),\n      ky1 = cy1 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x1),\n      d = 2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.haversin)(y1 - y0) + cy0 * cy1 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.haversin)(x1 - x0))),\n      k = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(d);\n\n  var interpolate = d ? function(t) {\n    var B = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(t *= d) / k,\n        A = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(d - t) / k,\n        x = A * kx0 + B * kx1,\n        y = A * ky0 + B * ky1,\n        z = A * sy0 + B * sy1;\n    return [\n      (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(y, x) * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees,\n      (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(z, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(x * x + y * y)) * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees\n    ];\n  } : function() {\n    return [x0 * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, y0 * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees];\n  };\n\n  interpolate.distance = d;\n\n  return interpolate;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/interpolate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/length.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-geo/src/length.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./noop.js */ \"(ssr)/./node_modules/d3-geo/src/noop.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stream.js */ \"(ssr)/./node_modules/d3-geo/src/stream.js\");\n\n\n\n\n\nvar lengthSum,\n    lambda0,\n    sinPhi0,\n    cosPhi0;\n\nvar lengthStream = {\n  sphere: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n  point: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n  lineStart: lengthLineStart,\n  lineEnd: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n  polygonStart: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n  polygonEnd: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]\n};\n\nfunction lengthLineStart() {\n  lengthStream.point = lengthPointFirst;\n  lengthStream.lineEnd = lengthLineEnd;\n}\n\nfunction lengthLineEnd() {\n  lengthStream.point = lengthStream.lineEnd = _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n}\n\nfunction lengthPointFirst(lambda, phi) {\n  lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n  lambda0 = lambda, sinPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi), cosPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi);\n  lengthStream.point = lengthPoint;\n}\n\nfunction lengthPoint(lambda, phi) {\n  lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n  var sinPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi),\n      cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi),\n      delta = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(lambda - lambda0),\n      cosDelta = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(delta),\n      sinDelta = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(delta),\n      x = cosPhi * sinDelta,\n      y = cosPhi0 * sinPhi - sinPhi0 * cosPhi * cosDelta,\n      z = sinPhi0 * sinPhi + cosPhi0 * cosPhi * cosDelta;\n  lengthSum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.atan2)((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sqrt)(x * x + y * y), z));\n  lambda0 = lambda, sinPhi0 = sinPhi, cosPhi0 = cosPhi;\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(object) {\n  lengthSum = new d3_array__WEBPACK_IMPORTED_MODULE_2__.Adder();\n  (0,_stream_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(object, lengthStream);\n  return +lengthSum;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/length.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/math.js":
/*!*****************************************!*\
  !*** ./node_modules/d3-geo/src/math.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   abs: () => (/* binding */ abs),\n/* harmony export */   acos: () => (/* binding */ acos),\n/* harmony export */   asin: () => (/* binding */ asin),\n/* harmony export */   atan: () => (/* binding */ atan),\n/* harmony export */   atan2: () => (/* binding */ atan2),\n/* harmony export */   ceil: () => (/* binding */ ceil),\n/* harmony export */   cos: () => (/* binding */ cos),\n/* harmony export */   degrees: () => (/* binding */ degrees),\n/* harmony export */   epsilon: () => (/* binding */ epsilon),\n/* harmony export */   epsilon2: () => (/* binding */ epsilon2),\n/* harmony export */   exp: () => (/* binding */ exp),\n/* harmony export */   floor: () => (/* binding */ floor),\n/* harmony export */   halfPi: () => (/* binding */ halfPi),\n/* harmony export */   haversin: () => (/* binding */ haversin),\n/* harmony export */   hypot: () => (/* binding */ hypot),\n/* harmony export */   log: () => (/* binding */ log),\n/* harmony export */   pi: () => (/* binding */ pi),\n/* harmony export */   pow: () => (/* binding */ pow),\n/* harmony export */   quarterPi: () => (/* binding */ quarterPi),\n/* harmony export */   radians: () => (/* binding */ radians),\n/* harmony export */   sign: () => (/* binding */ sign),\n/* harmony export */   sin: () => (/* binding */ sin),\n/* harmony export */   sqrt: () => (/* binding */ sqrt),\n/* harmony export */   tan: () => (/* binding */ tan),\n/* harmony export */   tau: () => (/* binding */ tau)\n/* harmony export */ });\nvar epsilon = 1e-6;\nvar epsilon2 = 1e-12;\nvar pi = Math.PI;\nvar halfPi = pi / 2;\nvar quarterPi = pi / 4;\nvar tau = pi * 2;\n\nvar degrees = 180 / pi;\nvar radians = pi / 180;\n\nvar abs = Math.abs;\nvar atan = Math.atan;\nvar atan2 = Math.atan2;\nvar cos = Math.cos;\nvar ceil = Math.ceil;\nvar exp = Math.exp;\nvar floor = Math.floor;\nvar hypot = Math.hypot;\nvar log = Math.log;\nvar pow = Math.pow;\nvar sin = Math.sin;\nvar sign = Math.sign || function(x) { return x > 0 ? 1 : x < 0 ? -1 : 0; };\nvar sqrt = Math.sqrt;\nvar tan = Math.tan;\n\nfunction acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nfunction asin(x) {\n  return x > 1 ? halfPi : x < -1 ? -halfPi : Math.asin(x);\n}\n\nfunction haversin(x) {\n  return (x = sin(x / 2)) * x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9tYXRoLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBTztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDO0FBQ3RDO0FBQ0E7O0FBRUE7QUFDUDtBQUNBOztBQUVPO0FBQ1A7QUFDQTs7QUFFTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtZ2VvXFxzcmNcXG1hdGguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBlcHNpbG9uID0gMWUtNjtcbmV4cG9ydCB2YXIgZXBzaWxvbjIgPSAxZS0xMjtcbmV4cG9ydCB2YXIgcGkgPSBNYXRoLlBJO1xuZXhwb3J0IHZhciBoYWxmUGkgPSBwaSAvIDI7XG5leHBvcnQgdmFyIHF1YXJ0ZXJQaSA9IHBpIC8gNDtcbmV4cG9ydCB2YXIgdGF1ID0gcGkgKiAyO1xuXG5leHBvcnQgdmFyIGRlZ3JlZXMgPSAxODAgLyBwaTtcbmV4cG9ydCB2YXIgcmFkaWFucyA9IHBpIC8gMTgwO1xuXG5leHBvcnQgdmFyIGFicyA9IE1hdGguYWJzO1xuZXhwb3J0IHZhciBhdGFuID0gTWF0aC5hdGFuO1xuZXhwb3J0IHZhciBhdGFuMiA9IE1hdGguYXRhbjI7XG5leHBvcnQgdmFyIGNvcyA9IE1hdGguY29zO1xuZXhwb3J0IHZhciBjZWlsID0gTWF0aC5jZWlsO1xuZXhwb3J0IHZhciBleHAgPSBNYXRoLmV4cDtcbmV4cG9ydCB2YXIgZmxvb3IgPSBNYXRoLmZsb29yO1xuZXhwb3J0IHZhciBoeXBvdCA9IE1hdGguaHlwb3Q7XG5leHBvcnQgdmFyIGxvZyA9IE1hdGgubG9nO1xuZXhwb3J0IHZhciBwb3cgPSBNYXRoLnBvdztcbmV4cG9ydCB2YXIgc2luID0gTWF0aC5zaW47XG5leHBvcnQgdmFyIHNpZ24gPSBNYXRoLnNpZ24gfHwgZnVuY3Rpb24oeCkgeyByZXR1cm4geCA+IDAgPyAxIDogeCA8IDAgPyAtMSA6IDA7IH07XG5leHBvcnQgdmFyIHNxcnQgPSBNYXRoLnNxcnQ7XG5leHBvcnQgdmFyIHRhbiA9IE1hdGgudGFuO1xuXG5leHBvcnQgZnVuY3Rpb24gYWNvcyh4KSB7XG4gIHJldHVybiB4ID4gMSA/IDAgOiB4IDwgLTEgPyBwaSA6IE1hdGguYWNvcyh4KTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGFzaW4oeCkge1xuICByZXR1cm4geCA+IDEgPyBoYWxmUGkgOiB4IDwgLTEgPyAtaGFsZlBpIDogTWF0aC5hc2luKHgpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gaGF2ZXJzaW4oeCkge1xuICByZXR1cm4gKHggPSBzaW4oeCAvIDIpKSAqIHg7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/math.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/noop.js":
/*!*****************************************!*\
  !*** ./node_modules/d3-geo/src/noop.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ noop)\n/* harmony export */ });\nfunction noop() {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9ub29wLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1nZW9cXHNyY1xcbm9vcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBub29wKCkge31cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/noop.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/path/area.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-geo/src/path/area.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../noop.js */ \"(ssr)/./node_modules/d3-geo/src/noop.js\");\n\n\n\n\nvar areaSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder(),\n    areaRingSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder(),\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar areaStream = {\n  point: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  lineStart: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  lineEnd: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  polygonStart: function() {\n    areaStream.lineStart = areaRingStart;\n    areaStream.lineEnd = areaRingEnd;\n  },\n  polygonEnd: function() {\n    areaStream.lineStart = areaStream.lineEnd = areaStream.point = _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n    areaSum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_2__.abs)(areaRingSum));\n    areaRingSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n  },\n  result: function() {\n    var area = areaSum / 2;\n    areaSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n    return area;\n  }\n};\n\nfunction areaRingStart() {\n  areaStream.point = areaPointFirst;\n}\n\nfunction areaPointFirst(x, y) {\n  areaStream.point = areaPoint;\n  x00 = x0 = x, y00 = y0 = y;\n}\n\nfunction areaPoint(x, y) {\n  areaRingSum.add(y0 * x - x0 * y);\n  x0 = x, y0 = y;\n}\n\nfunction areaRingEnd() {\n  areaPoint(x00, y00);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (areaStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/path/area.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/path/bounds.js":
/*!************************************************!*\
  !*** ./node_modules/d3-geo/src/path/bounds.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../noop.js */ \"(ssr)/./node_modules/d3-geo/src/noop.js\");\n\n\nvar x0 = Infinity,\n    y0 = x0,\n    x1 = -x0,\n    y1 = x1;\n\nvar boundsStream = {\n  point: boundsPoint,\n  lineStart: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n  lineEnd: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n  polygonStart: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n  polygonEnd: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n  result: function() {\n    var bounds = [[x0, y0], [x1, y1]];\n    x1 = y1 = -(y0 = x0 = Infinity);\n    return bounds;\n  }\n};\n\nfunction boundsPoint(x, y) {\n  if (x < x0) x0 = x;\n  if (x > x1) x1 = x;\n  if (y < y0) y0 = y;\n  if (y > y1) y1 = y;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (boundsStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wYXRoL2JvdW5kcy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4Qjs7QUFFOUI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGFBQWEsZ0RBQUk7QUFDakIsV0FBVyxnREFBSTtBQUNmLGdCQUFnQixnREFBSTtBQUNwQixjQUFjLGdEQUFJO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsaUVBQWUsWUFBWSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGQzLWdlb1xcc3JjXFxwYXRoXFxib3VuZHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG5vb3AgZnJvbSBcIi4uL25vb3AuanNcIjtcblxudmFyIHgwID0gSW5maW5pdHksXG4gICAgeTAgPSB4MCxcbiAgICB4MSA9IC14MCxcbiAgICB5MSA9IHgxO1xuXG52YXIgYm91bmRzU3RyZWFtID0ge1xuICBwb2ludDogYm91bmRzUG9pbnQsXG4gIGxpbmVTdGFydDogbm9vcCxcbiAgbGluZUVuZDogbm9vcCxcbiAgcG9seWdvblN0YXJ0OiBub29wLFxuICBwb2x5Z29uRW5kOiBub29wLFxuICByZXN1bHQ6IGZ1bmN0aW9uKCkge1xuICAgIHZhciBib3VuZHMgPSBbW3gwLCB5MF0sIFt4MSwgeTFdXTtcbiAgICB4MSA9IHkxID0gLSh5MCA9IHgwID0gSW5maW5pdHkpO1xuICAgIHJldHVybiBib3VuZHM7XG4gIH1cbn07XG5cbmZ1bmN0aW9uIGJvdW5kc1BvaW50KHgsIHkpIHtcbiAgaWYgKHggPCB4MCkgeDAgPSB4O1xuICBpZiAoeCA+IHgxKSB4MSA9IHg7XG4gIGlmICh5IDwgeTApIHkwID0geTtcbiAgaWYgKHkgPiB5MSkgeTEgPSB5O1xufVxuXG5leHBvcnQgZGVmYXVsdCBib3VuZHNTdHJlYW07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/path/bounds.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/path/centroid.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-geo/src/path/centroid.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\n// TODO Enforce positive area for exterior, negative area for interior?\n\nvar X0 = 0,\n    Y0 = 0,\n    Z0 = 0,\n    X1 = 0,\n    Y1 = 0,\n    Z1 = 0,\n    X2 = 0,\n    Y2 = 0,\n    Z2 = 0,\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar centroidStream = {\n  point: centroidPoint,\n  lineStart: centroidLineStart,\n  lineEnd: centroidLineEnd,\n  polygonStart: function() {\n    centroidStream.lineStart = centroidRingStart;\n    centroidStream.lineEnd = centroidRingEnd;\n  },\n  polygonEnd: function() {\n    centroidStream.point = centroidPoint;\n    centroidStream.lineStart = centroidLineStart;\n    centroidStream.lineEnd = centroidLineEnd;\n  },\n  result: function() {\n    var centroid = Z2 ? [X2 / Z2, Y2 / Z2]\n        : Z1 ? [X1 / Z1, Y1 / Z1]\n        : Z0 ? [X0 / Z0, Y0 / Z0]\n        : [NaN, NaN];\n    X0 = Y0 = Z0 =\n    X1 = Y1 = Z1 =\n    X2 = Y2 = Z2 = 0;\n    return centroid;\n  }\n};\n\nfunction centroidPoint(x, y) {\n  X0 += x;\n  Y0 += y;\n  ++Z0;\n}\n\nfunction centroidLineStart() {\n  centroidStream.point = centroidPointFirstLine;\n}\n\nfunction centroidPointFirstLine(x, y) {\n  centroidStream.point = centroidPointLine;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nfunction centroidPointLine(x, y) {\n  var dx = x - x0, dy = y - y0, z = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(dx * dx + dy * dy);\n  X1 += z * (x0 + x) / 2;\n  Y1 += z * (y0 + y) / 2;\n  Z1 += z;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nfunction centroidLineEnd() {\n  centroidStream.point = centroidPoint;\n}\n\nfunction centroidRingStart() {\n  centroidStream.point = centroidPointFirstRing;\n}\n\nfunction centroidRingEnd() {\n  centroidPointRing(x00, y00);\n}\n\nfunction centroidPointFirstRing(x, y) {\n  centroidStream.point = centroidPointRing;\n  centroidPoint(x00 = x0 = x, y00 = y0 = y);\n}\n\nfunction centroidPointRing(x, y) {\n  var dx = x - x0,\n      dy = y - y0,\n      z = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(dx * dx + dy * dy);\n\n  X1 += z * (x0 + x) / 2;\n  Y1 += z * (y0 + y) / 2;\n  Z1 += z;\n\n  z = y0 * x - x0 * y;\n  X2 += z * (x0 + x);\n  Y2 += z * (y0 + y);\n  Z2 += z * 3;\n  centroidPoint(x0 = x, y0 = y);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (centroidStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/path/centroid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/path/context.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-geo/src/path/context.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PathContext)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../noop.js */ \"(ssr)/./node_modules/d3-geo/src/noop.js\");\n\n\n\nfunction PathContext(context) {\n  this._context = context;\n}\n\nPathContext.prototype = {\n  _radius: 4.5,\n  pointRadius: function(_) {\n    return this._radius = _, this;\n  },\n  polygonStart: function() {\n    this._line = 0;\n  },\n  polygonEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line === 0) this._context.closePath();\n    this._point = NaN;\n  },\n  point: function(x, y) {\n    switch (this._point) {\n      case 0: {\n        this._context.moveTo(x, y);\n        this._point = 1;\n        break;\n      }\n      case 1: {\n        this._context.lineTo(x, y);\n        break;\n      }\n      default: {\n        this._context.moveTo(x + this._radius, y);\n        this._context.arc(x, y, this._radius, 0, _math_js__WEBPACK_IMPORTED_MODULE_0__.tau);\n        break;\n      }\n    }\n  },\n  result: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/path/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/path/index.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-geo/src/path/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../identity.js */ \"(ssr)/./node_modules/d3-geo/src/identity.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../stream.js */ \"(ssr)/./node_modules/d3-geo/src/stream.js\");\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./area.js */ \"(ssr)/./node_modules/d3-geo/src/path/area.js\");\n/* harmony import */ var _bounds_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./bounds.js */ \"(ssr)/./node_modules/d3-geo/src/path/bounds.js\");\n/* harmony import */ var _centroid_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./centroid.js */ \"(ssr)/./node_modules/d3-geo/src/path/centroid.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/d3-geo/src/path/context.js\");\n/* harmony import */ var _measure_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./measure.js */ \"(ssr)/./node_modules/d3-geo/src/path/measure.js\");\n/* harmony import */ var _string_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./string.js */ \"(ssr)/./node_modules/d3-geo/src/path/string.js\");\n\n\n\n\n\n\n\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(projection, context) {\n  let digits = 3,\n      pointRadius = 4.5,\n      projectionStream,\n      contextStream;\n\n  function path(object) {\n    if (object) {\n      if (typeof pointRadius === \"function\") contextStream.pointRadius(+pointRadius.apply(this, arguments));\n      (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projectionStream(contextStream));\n    }\n    return contextStream.result();\n  }\n\n  path.area = function(object) {\n    (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projectionStream(_area_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]));\n    return _area_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].result();\n  };\n\n  path.measure = function(object) {\n    (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projectionStream(_measure_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]));\n    return _measure_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].result();\n  };\n\n  path.bounds = function(object) {\n    (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projectionStream(_bounds_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]));\n    return _bounds_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].result();\n  };\n\n  path.centroid = function(object) {\n    (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projectionStream(_centroid_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]));\n    return _centroid_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].result();\n  };\n\n  path.projection = function(_) {\n    if (!arguments.length) return projection;\n    projectionStream = _ == null ? (projection = null, _identity_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]) : (projection = _).stream;\n    return path;\n  };\n\n  path.context = function(_) {\n    if (!arguments.length) return context;\n    contextStream = _ == null ? (context = null, new _string_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"](digits)) : new _context_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"](context = _);\n    if (typeof pointRadius !== \"function\") contextStream.pointRadius(pointRadius);\n    return path;\n  };\n\n  path.pointRadius = function(_) {\n    if (!arguments.length) return pointRadius;\n    pointRadius = typeof _ === \"function\" ? _ : (contextStream.pointRadius(+_), +_);\n    return path;\n  };\n\n  path.digits = function(_) {\n    if (!arguments.length) return digits;\n    if (_ == null) digits = null;\n    else {\n      const d = Math.floor(_);\n      if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);\n      digits = d;\n    }\n    if (context === null) contextStream = new _string_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"](digits);\n    return path;\n  };\n\n  return path.projection(projection).digits(digits).context(context);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/path/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/path/measure.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-geo/src/path/measure.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../noop.js */ \"(ssr)/./node_modules/d3-geo/src/noop.js\");\n\n\n\n\nvar lengthSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder(),\n    lengthRing,\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar lengthStream = {\n  point: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  lineStart: function() {\n    lengthStream.point = lengthPointFirst;\n  },\n  lineEnd: function() {\n    if (lengthRing) lengthPoint(x00, y00);\n    lengthStream.point = _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n  },\n  polygonStart: function() {\n    lengthRing = true;\n  },\n  polygonEnd: function() {\n    lengthRing = null;\n  },\n  result: function() {\n    var length = +lengthSum;\n    lengthSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n    return length;\n  }\n};\n\nfunction lengthPointFirst(x, y) {\n  lengthStream.point = lengthPoint;\n  x00 = x0 = x, y00 = y0 = y;\n}\n\nfunction lengthPoint(x, y) {\n  x0 -= x, y0 -= y;\n  lengthSum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_2__.sqrt)(x0 * x0 + y0 * y0));\n  x0 = x, y0 = y;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (lengthStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wYXRoL21lYXN1cmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUErQjtBQUNDO0FBQ0Y7O0FBRTlCLG9CQUFvQiwyQ0FBSztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsU0FBUyxnREFBSTtBQUNiO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLHlCQUF5QixnREFBSTtBQUM3QixHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxvQkFBb0IsMkNBQUs7QUFDekI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxnQkFBZ0IsOENBQUk7QUFDcEI7QUFDQTs7QUFFQSxpRUFBZSxZQUFZLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtZ2VvXFxzcmNcXHBhdGhcXG1lYXN1cmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtBZGRlcn0gZnJvbSBcImQzLWFycmF5XCI7XG5pbXBvcnQge3NxcnR9IGZyb20gXCIuLi9tYXRoLmpzXCI7XG5pbXBvcnQgbm9vcCBmcm9tIFwiLi4vbm9vcC5qc1wiO1xuXG52YXIgbGVuZ3RoU3VtID0gbmV3IEFkZGVyKCksXG4gICAgbGVuZ3RoUmluZyxcbiAgICB4MDAsXG4gICAgeTAwLFxuICAgIHgwLFxuICAgIHkwO1xuXG52YXIgbGVuZ3RoU3RyZWFtID0ge1xuICBwb2ludDogbm9vcCxcbiAgbGluZVN0YXJ0OiBmdW5jdGlvbigpIHtcbiAgICBsZW5ndGhTdHJlYW0ucG9pbnQgPSBsZW5ndGhQb2ludEZpcnN0O1xuICB9LFxuICBsaW5lRW5kOiBmdW5jdGlvbigpIHtcbiAgICBpZiAobGVuZ3RoUmluZykgbGVuZ3RoUG9pbnQoeDAwLCB5MDApO1xuICAgIGxlbmd0aFN0cmVhbS5wb2ludCA9IG5vb3A7XG4gIH0sXG4gIHBvbHlnb25TdGFydDogZnVuY3Rpb24oKSB7XG4gICAgbGVuZ3RoUmluZyA9IHRydWU7XG4gIH0sXG4gIHBvbHlnb25FbmQ6IGZ1bmN0aW9uKCkge1xuICAgIGxlbmd0aFJpbmcgPSBudWxsO1xuICB9LFxuICByZXN1bHQ6IGZ1bmN0aW9uKCkge1xuICAgIHZhciBsZW5ndGggPSArbGVuZ3RoU3VtO1xuICAgIGxlbmd0aFN1bSA9IG5ldyBBZGRlcigpO1xuICAgIHJldHVybiBsZW5ndGg7XG4gIH1cbn07XG5cbmZ1bmN0aW9uIGxlbmd0aFBvaW50Rmlyc3QoeCwgeSkge1xuICBsZW5ndGhTdHJlYW0ucG9pbnQgPSBsZW5ndGhQb2ludDtcbiAgeDAwID0geDAgPSB4LCB5MDAgPSB5MCA9IHk7XG59XG5cbmZ1bmN0aW9uIGxlbmd0aFBvaW50KHgsIHkpIHtcbiAgeDAgLT0geCwgeTAgLT0geTtcbiAgbGVuZ3RoU3VtLmFkZChzcXJ0KHgwICogeDAgKyB5MCAqIHkwKSk7XG4gIHgwID0geCwgeTAgPSB5O1xufVxuXG5leHBvcnQgZGVmYXVsdCBsZW5ndGhTdHJlYW07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/path/measure.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/path/string.js":
/*!************************************************!*\
  !*** ./node_modules/d3-geo/src/path/string.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PathString)\n/* harmony export */ });\n// Simple caching for constant-radius points.\nlet cacheDigits, cacheAppend, cacheRadius, cacheCircle;\n\nclass PathString {\n  constructor(digits) {\n    this._append = digits == null ? append : appendRound(digits);\n    this._radius = 4.5;\n    this._ = \"\";\n  }\n  pointRadius(_) {\n    this._radius = +_;\n    return this;\n  }\n  polygonStart() {\n    this._line = 0;\n  }\n  polygonEnd() {\n    this._line = NaN;\n  }\n  lineStart() {\n    this._point = 0;\n  }\n  lineEnd() {\n    if (this._line === 0) this._ += \"Z\";\n    this._point = NaN;\n  }\n  point(x, y) {\n    switch (this._point) {\n      case 0: {\n        this._append`M${x},${y}`;\n        this._point = 1;\n        break;\n      }\n      case 1: {\n        this._append`L${x},${y}`;\n        break;\n      }\n      default: {\n        this._append`M${x},${y}`;\n        if (this._radius !== cacheRadius || this._append !== cacheAppend) {\n          const r = this._radius;\n          const s = this._;\n          this._ = \"\"; // stash the old string so we can cache the circle path fragment\n          this._append`m0,${r}a${r},${r} 0 1,1 0,${-2 * r}a${r},${r} 0 1,1 0,${2 * r}z`;\n          cacheRadius = r;\n          cacheAppend = this._append;\n          cacheCircle = this._;\n          this._ = s;\n        }\n        this._ += cacheCircle;\n        break;\n      }\n    }\n  }\n  result() {\n    const result = this._;\n    this._ = \"\";\n    return result.length ? result : null;\n  }\n}\n\nfunction append(strings) {\n  let i = 1;\n  this._ += strings[0];\n  for (const j = strings.length; i < j; ++i) {\n    this._ += arguments[i] + strings[i];\n  }\n}\n\nfunction appendRound(digits) {\n  const d = Math.floor(digits);\n  if (!(d >= 0)) throw new RangeError(`invalid digits: ${digits}`);\n  if (d > 15) return append;\n  if (d !== cacheDigits) {\n    const k = 10 ** d;\n    cacheDigits = d;\n    cacheAppend = function append(strings) {\n      let i = 1;\n      this._ += strings[0];\n      for (const j = strings.length; i < j; ++i) {\n        this._ += Math.round(arguments[i] * k) / k + strings[i];\n      }\n    };\n  }\n  return cacheAppend;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/path/string.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/pointEqual.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-geo/src/pointEqual.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n  return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(a[0] - b[0]) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon && (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(a[1] - b[1]) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wb2ludEVxdWFsLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDOztBQUV2Qyw2QkFBZSxvQ0FBUztBQUN4QixTQUFTLDZDQUFHLGdCQUFnQiw2Q0FBTyxJQUFJLDZDQUFHLGdCQUFnQiw2Q0FBTztBQUNqRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1nZW9cXHNyY1xccG9pbnRFcXVhbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2FicywgZXBzaWxvbn0gZnJvbSBcIi4vbWF0aC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihhLCBiKSB7XG4gIHJldHVybiBhYnMoYVswXSAtIGJbMF0pIDwgZXBzaWxvbiAmJiBhYnMoYVsxXSAtIGJbMV0pIDwgZXBzaWxvbjtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/pointEqual.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/polygonContains.js":
/*!****************************************************!*\
  !*** ./node_modules/d3-geo/src/polygonContains.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _cartesian_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cartesian.js */ \"(ssr)/./node_modules/d3-geo/src/cartesian.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\n\n\nfunction longitude(point) {\n  return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(point[0]) <= _math_js__WEBPACK_IMPORTED_MODULE_0__.pi ? point[0] : (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(point[0]) * (((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(point[0]) + _math_js__WEBPACK_IMPORTED_MODULE_0__.pi) % _math_js__WEBPACK_IMPORTED_MODULE_0__.tau - _math_js__WEBPACK_IMPORTED_MODULE_0__.pi);\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon, point) {\n  var lambda = longitude(point),\n      phi = point[1],\n      sinPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi),\n      normal = [(0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(lambda), -(0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(lambda), 0],\n      angle = 0,\n      winding = 0;\n\n  var sum = new d3_array__WEBPACK_IMPORTED_MODULE_1__.Adder();\n\n  if (sinPhi === 1) phi = _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n  else if (sinPhi === -1) phi = -_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi - _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n\n  for (var i = 0, n = polygon.length; i < n; ++i) {\n    if (!(m = (ring = polygon[i]).length)) continue;\n    var ring,\n        m,\n        point0 = ring[m - 1],\n        lambda0 = longitude(point0),\n        phi0 = point0[1] / 2 + _math_js__WEBPACK_IMPORTED_MODULE_0__.quarterPi,\n        sinPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi0),\n        cosPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi0);\n\n    for (var j = 0; j < m; ++j, lambda0 = lambda1, sinPhi0 = sinPhi1, cosPhi0 = cosPhi1, point0 = point1) {\n      var point1 = ring[j],\n          lambda1 = longitude(point1),\n          phi1 = point1[1] / 2 + _math_js__WEBPACK_IMPORTED_MODULE_0__.quarterPi,\n          sinPhi1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi1),\n          cosPhi1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi1),\n          delta = lambda1 - lambda0,\n          sign = delta >= 0 ? 1 : -1,\n          absDelta = sign * delta,\n          antimeridian = absDelta > _math_js__WEBPACK_IMPORTED_MODULE_0__.pi,\n          k = sinPhi0 * sinPhi1;\n\n      sum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(k * sign * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(absDelta), cosPhi0 * cosPhi1 + k * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(absDelta)));\n      angle += antimeridian ? delta + sign * _math_js__WEBPACK_IMPORTED_MODULE_0__.tau : delta;\n\n      // Are the longitudes either side of the point’s meridian (lambda),\n      // and are the latitudes smaller than the parallel (phi)?\n      if (antimeridian ^ lambda0 >= lambda ^ lambda1 >= lambda) {\n        var arc = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesianCross)((0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesian)(point0), (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesian)(point1));\n        (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesianNormalizeInPlace)(arc);\n        var intersection = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesianCross)(normal, arc);\n        (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesianNormalizeInPlace)(intersection);\n        var phiArc = (antimeridian ^ delta >= 0 ? -1 : 1) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(intersection[2]);\n        if (phi > phiArc || phi === phiArc && (arc[0] || arc[1])) {\n          winding += antimeridian ^ delta >= 0 ? 1 : -1;\n        }\n      }\n    }\n  }\n\n  // First, determine whether the South pole is inside or outside:\n  //\n  // It is inside if:\n  // * the polygon winds around it in a clockwise direction.\n  // * the polygon does not (cumulatively) wind around it, but has a negative\n  //   (counter-clockwise) area.\n  //\n  // Second, count the (signed) number of times a segment crosses a lambda\n  // from the point to the South pole.  If it is zero, then the point is the\n  // same side as the South pole.\n\n  return (angle < -_math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon || angle < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon && sum < -_math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon2) ^ (winding & 1);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/polygonContains.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/albers.js":
/*!******************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/albers.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _conicEqualArea_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./conicEqualArea.js */ \"(ssr)/./node_modules/d3-geo/src/projection/conicEqualArea.js\");\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  return (0,_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])()\n      .parallels([29.5, 45.5])\n      .scale(1070)\n      .translate([480, 250])\n      .rotate([96, 0])\n      .center([-0.6, 38.7]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2FsYmVycy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpRDs7QUFFakQsNkJBQWUsc0NBQVc7QUFDMUIsU0FBUyw4REFBYztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtZ2VvXFxzcmNcXHByb2plY3Rpb25cXGFsYmVycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY29uaWNFcXVhbEFyZWEgZnJvbSBcIi4vY29uaWNFcXVhbEFyZWEuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oKSB7XG4gIHJldHVybiBjb25pY0VxdWFsQXJlYSgpXG4gICAgICAucGFyYWxsZWxzKFsyOS41LCA0NS41XSlcbiAgICAgIC5zY2FsZSgxMDcwKVxuICAgICAgLnRyYW5zbGF0ZShbNDgwLCAyNTBdKVxuICAgICAgLnJvdGF0ZShbOTYsIDBdKVxuICAgICAgLmNlbnRlcihbLTAuNiwgMzguN10pO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/albers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/albersUsa.js":
/*!*********************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/albersUsa.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _albers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./albers.js */ \"(ssr)/./node_modules/d3-geo/src/projection/albers.js\");\n/* harmony import */ var _conicEqualArea_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./conicEqualArea.js */ \"(ssr)/./node_modules/d3-geo/src/projection/conicEqualArea.js\");\n/* harmony import */ var _fit_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./fit.js */ \"(ssr)/./node_modules/d3-geo/src/projection/fit.js\");\n\n\n\n\n\n// The projections must have mutually exclusive clip regions on the sphere,\n// as this will avoid emitting interleaving lines and polygons.\nfunction multiplex(streams) {\n  var n = streams.length;\n  return {\n    point: function(x, y) { var i = -1; while (++i < n) streams[i].point(x, y); },\n    sphere: function() { var i = -1; while (++i < n) streams[i].sphere(); },\n    lineStart: function() { var i = -1; while (++i < n) streams[i].lineStart(); },\n    lineEnd: function() { var i = -1; while (++i < n) streams[i].lineEnd(); },\n    polygonStart: function() { var i = -1; while (++i < n) streams[i].polygonStart(); },\n    polygonEnd: function() { var i = -1; while (++i < n) streams[i].polygonEnd(); }\n  };\n}\n\n// A composite projection for the United States, configured by default for\n// 960×500. The projection also works quite well at 960×600 if you change the\n// scale to 1285 and adjust the translate accordingly. The set of standard\n// parallels for each region comes from USGS, which is published here:\n// http://egsc.usgs.gov/isb/pubs/MapProjections/projections.html#albers\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  var cache,\n      cacheStream,\n      lower48 = (0,_albers_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(), lower48Point,\n      alaska = (0,_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])().rotate([154, 0]).center([-2, 58.5]).parallels([55, 65]), alaskaPoint, // EPSG:3338\n      hawaii = (0,_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])().rotate([157, 0]).center([-3, 19.9]).parallels([8, 18]), hawaiiPoint, // ESRI:102007\n      point, pointStream = {point: function(x, y) { point = [x, y]; }};\n\n  function albersUsa(coordinates) {\n    var x = coordinates[0], y = coordinates[1];\n    return point = null,\n        (lower48Point.point(x, y), point)\n        || (alaskaPoint.point(x, y), point)\n        || (hawaiiPoint.point(x, y), point);\n  }\n\n  albersUsa.invert = function(coordinates) {\n    var k = lower48.scale(),\n        t = lower48.translate(),\n        x = (coordinates[0] - t[0]) / k,\n        y = (coordinates[1] - t[1]) / k;\n    return (y >= 0.120 && y < 0.234 && x >= -0.425 && x < -0.214 ? alaska\n        : y >= 0.166 && y < 0.234 && x >= -0.214 && x < -0.115 ? hawaii\n        : lower48).invert(coordinates);\n  };\n\n  albersUsa.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = multiplex([lower48.stream(cacheStream = stream), alaska.stream(stream), hawaii.stream(stream)]);\n  };\n\n  albersUsa.precision = function(_) {\n    if (!arguments.length) return lower48.precision();\n    lower48.precision(_), alaska.precision(_), hawaii.precision(_);\n    return reset();\n  };\n\n  albersUsa.scale = function(_) {\n    if (!arguments.length) return lower48.scale();\n    lower48.scale(_), alaska.scale(_ * 0.35), hawaii.scale(_);\n    return albersUsa.translate(lower48.translate());\n  };\n\n  albersUsa.translate = function(_) {\n    if (!arguments.length) return lower48.translate();\n    var k = lower48.scale(), x = +_[0], y = +_[1];\n\n    lower48Point = lower48\n        .translate(_)\n        .clipExtent([[x - 0.455 * k, y - 0.238 * k], [x + 0.455 * k, y + 0.238 * k]])\n        .stream(pointStream);\n\n    alaskaPoint = alaska\n        .translate([x - 0.307 * k, y + 0.201 * k])\n        .clipExtent([[x - 0.425 * k + _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon, y + 0.120 * k + _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon], [x - 0.214 * k - _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon, y + 0.234 * k - _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon]])\n        .stream(pointStream);\n\n    hawaiiPoint = hawaii\n        .translate([x - 0.205 * k, y + 0.212 * k])\n        .clipExtent([[x - 0.214 * k + _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon, y + 0.166 * k + _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon], [x - 0.115 * k - _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon, y + 0.234 * k - _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon]])\n        .stream(pointStream);\n\n    return reset();\n  };\n\n  albersUsa.fitExtent = function(extent, object) {\n    return (0,_fit_js__WEBPACK_IMPORTED_MODULE_3__.fitExtent)(albersUsa, extent, object);\n  };\n\n  albersUsa.fitSize = function(size, object) {\n    return (0,_fit_js__WEBPACK_IMPORTED_MODULE_3__.fitSize)(albersUsa, size, object);\n  };\n\n  albersUsa.fitWidth = function(width, object) {\n    return (0,_fit_js__WEBPACK_IMPORTED_MODULE_3__.fitWidth)(albersUsa, width, object);\n  };\n\n  albersUsa.fitHeight = function(height, object) {\n    return (0,_fit_js__WEBPACK_IMPORTED_MODULE_3__.fitHeight)(albersUsa, height, object);\n  };\n\n  function reset() {\n    cache = cacheStream = null;\n    return albersUsa;\n  }\n\n  return albersUsa.scale(1070);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/albersUsa.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/azimuthal.js":
/*!*********************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/azimuthal.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   azimuthalInvert: () => (/* binding */ azimuthalInvert),\n/* harmony export */   azimuthalRaw: () => (/* binding */ azimuthalRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\nfunction azimuthalRaw(scale) {\n  return function(x, y) {\n    var cx = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x),\n        cy = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y),\n        k = scale(cx * cy);\n        if (k === Infinity) return [2, 0];\n    return [\n      k * cy * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x),\n      k * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y)\n    ];\n  }\n}\n\nfunction azimuthalInvert(angle) {\n  return function(x, y) {\n    var z = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(x * x + y * y),\n        c = angle(z),\n        sc = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(c),\n        cc = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(c);\n    return [\n      (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(x * sc, z * cc),\n      (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(z && y * sc / z)\n    ];\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2F6aW11dGhhbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBdUQ7O0FBRWhEO0FBQ1A7QUFDQSxhQUFhLDZDQUFHO0FBQ2hCLGFBQWEsNkNBQUc7QUFDaEI7QUFDQTtBQUNBO0FBQ0EsZUFBZSw2Q0FBRztBQUNsQixVQUFVLDZDQUFHO0FBQ2I7QUFDQTtBQUNBOztBQUVPO0FBQ1A7QUFDQSxZQUFZLDhDQUFJO0FBQ2hCO0FBQ0EsYUFBYSw2Q0FBRztBQUNoQixhQUFhLDZDQUFHO0FBQ2hCO0FBQ0EsTUFBTSwrQ0FBSztBQUNYLE1BQU0sOENBQUk7QUFDVjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtZ2VvXFxzcmNcXHByb2plY3Rpb25cXGF6aW11dGhhbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2FzaW4sIGF0YW4yLCBjb3MsIHNpbiwgc3FydH0gZnJvbSBcIi4uL21hdGguanNcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIGF6aW11dGhhbFJhdyhzY2FsZSkge1xuICByZXR1cm4gZnVuY3Rpb24oeCwgeSkge1xuICAgIHZhciBjeCA9IGNvcyh4KSxcbiAgICAgICAgY3kgPSBjb3MoeSksXG4gICAgICAgIGsgPSBzY2FsZShjeCAqIGN5KTtcbiAgICAgICAgaWYgKGsgPT09IEluZmluaXR5KSByZXR1cm4gWzIsIDBdO1xuICAgIHJldHVybiBbXG4gICAgICBrICogY3kgKiBzaW4oeCksXG4gICAgICBrICogc2luKHkpXG4gICAgXTtcbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gYXppbXV0aGFsSW52ZXJ0KGFuZ2xlKSB7XG4gIHJldHVybiBmdW5jdGlvbih4LCB5KSB7XG4gICAgdmFyIHogPSBzcXJ0KHggKiB4ICsgeSAqIHkpLFxuICAgICAgICBjID0gYW5nbGUoeiksXG4gICAgICAgIHNjID0gc2luKGMpLFxuICAgICAgICBjYyA9IGNvcyhjKTtcbiAgICByZXR1cm4gW1xuICAgICAgYXRhbjIoeCAqIHNjLCB6ICogY2MpLFxuICAgICAgYXNpbih6ICYmIHkgKiBzYyAvIHopXG4gICAgXTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/azimuthal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/azimuthalEqualArea.js":
/*!******************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/azimuthalEqualArea.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   azimuthalEqualAreaRaw: () => (/* binding */ azimuthalEqualAreaRaw),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _azimuthal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./azimuthal.js */ \"(ssr)/./node_modules/d3-geo/src/projection/azimuthal.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n\n\n\n\nvar azimuthalEqualAreaRaw = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_0__.azimuthalRaw)(function(cxcy) {\n  return (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sqrt)(2 / (1 + cxcy));\n});\n\nazimuthalEqualAreaRaw.invert = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_0__.azimuthalInvert)(function(z) {\n  return 2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.asin)(z / 2);\n});\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  return (0,_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(azimuthalEqualAreaRaw)\n      .scale(124.75)\n      .clipAngle(180 - 1e-3);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2F6aW11dGhhbEVxdWFsQXJlYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFzQztBQUN1QjtBQUN6Qjs7QUFFN0IsNEJBQTRCLDJEQUFZO0FBQy9DLFNBQVMsOENBQUk7QUFDYixDQUFDOztBQUVELCtCQUErQiw4REFBZTtBQUM5QyxhQUFhLDhDQUFJO0FBQ2pCLENBQUM7O0FBRUQsNkJBQWUsc0NBQVc7QUFDMUIsU0FBUyxxREFBVTtBQUNuQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtZ2VvXFxzcmNcXHByb2plY3Rpb25cXGF6aW11dGhhbEVxdWFsQXJlYS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2FzaW4sIHNxcnR9IGZyb20gXCIuLi9tYXRoLmpzXCI7XG5pbXBvcnQge2F6aW11dGhhbFJhdywgYXppbXV0aGFsSW52ZXJ0fSBmcm9tIFwiLi9hemltdXRoYWwuanNcIjtcbmltcG9ydCBwcm9qZWN0aW9uIGZyb20gXCIuL2luZGV4LmpzXCI7XG5cbmV4cG9ydCB2YXIgYXppbXV0aGFsRXF1YWxBcmVhUmF3ID0gYXppbXV0aGFsUmF3KGZ1bmN0aW9uKGN4Y3kpIHtcbiAgcmV0dXJuIHNxcnQoMiAvICgxICsgY3hjeSkpO1xufSk7XG5cbmF6aW11dGhhbEVxdWFsQXJlYVJhdy5pbnZlcnQgPSBhemltdXRoYWxJbnZlcnQoZnVuY3Rpb24oeikge1xuICByZXR1cm4gMiAqIGFzaW4oeiAvIDIpO1xufSk7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gcHJvamVjdGlvbihhemltdXRoYWxFcXVhbEFyZWFSYXcpXG4gICAgICAuc2NhbGUoMTI0Ljc1KVxuICAgICAgLmNsaXBBbmdsZSgxODAgLSAxZS0zKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/azimuthalEqualArea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/azimuthalEquidistant.js":
/*!********************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/azimuthalEquidistant.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   azimuthalEquidistantRaw: () => (/* binding */ azimuthalEquidistantRaw),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _azimuthal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./azimuthal.js */ \"(ssr)/./node_modules/d3-geo/src/projection/azimuthal.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n\n\n\n\nvar azimuthalEquidistantRaw = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_0__.azimuthalRaw)(function(c) {\n  return (c = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.acos)(c)) && c / (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(c);\n});\n\nazimuthalEquidistantRaw.invert = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_0__.azimuthalInvert)(function(z) {\n  return z;\n});\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  return (0,_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(azimuthalEquidistantRaw)\n      .scale(79.4188)\n      .clipAngle(180 - 1e-3);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2F6aW11dGhhbEVxdWlkaXN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXFDO0FBQ3dCO0FBQ3pCOztBQUU3Qiw4QkFBOEIsMkRBQVk7QUFDakQsY0FBYyw4Q0FBSSxZQUFZLDZDQUFHO0FBQ2pDLENBQUM7O0FBRUQsaUNBQWlDLDhEQUFlO0FBQ2hEO0FBQ0EsQ0FBQzs7QUFFRCw2QkFBZSxzQ0FBVztBQUMxQixTQUFTLHFEQUFVO0FBQ25CO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1nZW9cXHNyY1xccHJvamVjdGlvblxcYXppbXV0aGFsRXF1aWRpc3RhbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHthY29zLCBzaW59IGZyb20gXCIuLi9tYXRoLmpzXCI7XG5pbXBvcnQge2F6aW11dGhhbFJhdywgYXppbXV0aGFsSW52ZXJ0fSBmcm9tIFwiLi9hemltdXRoYWwuanNcIjtcbmltcG9ydCBwcm9qZWN0aW9uIGZyb20gXCIuL2luZGV4LmpzXCI7XG5cbmV4cG9ydCB2YXIgYXppbXV0aGFsRXF1aWRpc3RhbnRSYXcgPSBhemltdXRoYWxSYXcoZnVuY3Rpb24oYykge1xuICByZXR1cm4gKGMgPSBhY29zKGMpKSAmJiBjIC8gc2luKGMpO1xufSk7XG5cbmF6aW11dGhhbEVxdWlkaXN0YW50UmF3LmludmVydCA9IGF6aW11dGhhbEludmVydChmdW5jdGlvbih6KSB7XG4gIHJldHVybiB6O1xufSk7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gcHJvamVjdGlvbihhemltdXRoYWxFcXVpZGlzdGFudFJhdylcbiAgICAgIC5zY2FsZSg3OS40MTg4KVxuICAgICAgLmNsaXBBbmdsZSgxODAgLSAxZS0zKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/azimuthalEquidistant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/conic.js":
/*!*****************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/conic.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conicProjection: () => (/* binding */ conicProjection)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n\n\n\nfunction conicProjection(projectAt) {\n  var phi0 = 0,\n      phi1 = _math_js__WEBPACK_IMPORTED_MODULE_0__.pi / 3,\n      m = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.projectionMutator)(projectAt),\n      p = m(phi0, phi1);\n\n  p.parallels = function(_) {\n    return arguments.length ? m(phi0 = _[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, phi1 = _[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians) : [phi0 * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, phi1 * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees];\n  };\n\n  return p;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2NvbmljLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDtBQUNIOztBQUV0QztBQUNQO0FBQ0EsYUFBYSx3Q0FBRTtBQUNmLFVBQVUsNERBQWlCO0FBQzNCOztBQUVBO0FBQ0EsOENBQThDLDZDQUFPLGdCQUFnQiw2Q0FBTyxZQUFZLDZDQUFPLFNBQVMsNkNBQU87QUFDL0c7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1nZW9cXHNyY1xccHJvamVjdGlvblxcY29uaWMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtkZWdyZWVzLCBwaSwgcmFkaWFuc30gZnJvbSBcIi4uL21hdGguanNcIjtcbmltcG9ydCB7cHJvamVjdGlvbk11dGF0b3J9IGZyb20gXCIuL2luZGV4LmpzXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBjb25pY1Byb2plY3Rpb24ocHJvamVjdEF0KSB7XG4gIHZhciBwaGkwID0gMCxcbiAgICAgIHBoaTEgPSBwaSAvIDMsXG4gICAgICBtID0gcHJvamVjdGlvbk11dGF0b3IocHJvamVjdEF0KSxcbiAgICAgIHAgPSBtKHBoaTAsIHBoaTEpO1xuXG4gIHAucGFyYWxsZWxzID0gZnVuY3Rpb24oXykge1xuICAgIHJldHVybiBhcmd1bWVudHMubGVuZ3RoID8gbShwaGkwID0gX1swXSAqIHJhZGlhbnMsIHBoaTEgPSBfWzFdICogcmFkaWFucykgOiBbcGhpMCAqIGRlZ3JlZXMsIHBoaTEgKiBkZWdyZWVzXTtcbiAgfTtcblxuICByZXR1cm4gcDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/conic.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/conicConformal.js":
/*!**************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/conicConformal.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conicConformalRaw: () => (/* binding */ conicConformalRaw),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _conic_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conic.js */ \"(ssr)/./node_modules/d3-geo/src/projection/conic.js\");\n/* harmony import */ var _mercator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mercator.js */ \"(ssr)/./node_modules/d3-geo/src/projection/mercator.js\");\n\n\n\n\nfunction tany(y) {\n  return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.tan)((_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + y) / 2);\n}\n\nfunction conicConformalRaw(y0, y1) {\n  var cy0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y0),\n      n = y0 === y1 ? (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y0) : (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.log)(cy0 / (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y1)) / (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.log)(tany(y1) / tany(y0)),\n      f = cy0 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.pow)(tany(y0), n) / n;\n\n  if (!n) return _mercator_js__WEBPACK_IMPORTED_MODULE_1__.mercatorRaw;\n\n  function project(x, y) {\n    if (f > 0) { if (y < -_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) y = -_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon; }\n    else { if (y > _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi - _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) y = _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi - _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon; }\n    var r = f / (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.pow)(tany(y), n);\n    return [r * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(n * x), f - r * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(n * x)];\n  }\n\n  project.invert = function(x, y) {\n    var fy = f - y, r = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(n) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(x * x + fy * fy),\n      l = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(x, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(fy)) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(fy);\n    if (fy * n < 0)\n      l -= _math_js__WEBPACK_IMPORTED_MODULE_0__.pi * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(x) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(fy);\n    return [l / n, 2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.pow)(f / r, 1 / n)) - _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi];\n  };\n\n  return project;\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  return (0,_conic_js__WEBPACK_IMPORTED_MODULE_2__.conicProjection)(conicConformalRaw)\n      .scale(109.5)\n      .parallels([30, 30]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/conicConformal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/conicEqualArea.js":
/*!**************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/conicEqualArea.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conicEqualAreaRaw: () => (/* binding */ conicEqualAreaRaw),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _conic_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conic.js */ \"(ssr)/./node_modules/d3-geo/src/projection/conic.js\");\n/* harmony import */ var _cylindricalEqualArea_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./cylindricalEqualArea.js */ \"(ssr)/./node_modules/d3-geo/src/projection/cylindricalEqualArea.js\");\n\n\n\n\nfunction conicEqualAreaRaw(y0, y1) {\n  var sy0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y0), n = (sy0 + (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y1)) / 2;\n\n  // Are the parallels symmetrical around the Equator?\n  if ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(n) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) return (0,_cylindricalEqualArea_js__WEBPACK_IMPORTED_MODULE_1__.cylindricalEqualAreaRaw)(y0);\n\n  var c = 1 + sy0 * (2 * n - sy0), r0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(c) / n;\n\n  function project(x, y) {\n    var r = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(c - 2 * n * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y)) / n;\n    return [r * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x *= n), r0 - r * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x)];\n  }\n\n  project.invert = function(x, y) {\n    var r0y = r0 - y,\n        l = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(x, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(r0y)) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(r0y);\n    if (r0y * n < 0)\n      l -= _math_js__WEBPACK_IMPORTED_MODULE_0__.pi * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(x) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(r0y);\n    return [l / n, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)((c - (x * x + r0y * r0y) * n * n) / (2 * n))];\n  };\n\n  return project;\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  return (0,_conic_js__WEBPACK_IMPORTED_MODULE_2__.conicProjection)(conicEqualAreaRaw)\n      .scale(155.424)\n      .center([0, 33.6442]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/conicEqualArea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/conicEquidistant.js":
/*!****************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/conicEquidistant.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conicEquidistantRaw: () => (/* binding */ conicEquidistantRaw),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _conic_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conic.js */ \"(ssr)/./node_modules/d3-geo/src/projection/conic.js\");\n/* harmony import */ var _equirectangular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./equirectangular.js */ \"(ssr)/./node_modules/d3-geo/src/projection/equirectangular.js\");\n\n\n\n\nfunction conicEquidistantRaw(y0, y1) {\n  var cy0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y0),\n      n = y0 === y1 ? (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y0) : (cy0 - (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y1)) / (y1 - y0),\n      g = cy0 / n + y0;\n\n  if ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(n) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) return _equirectangular_js__WEBPACK_IMPORTED_MODULE_1__.equirectangularRaw;\n\n  function project(x, y) {\n    var gy = g - y, nx = n * x;\n    return [gy * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(nx), g - gy * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(nx)];\n  }\n\n  project.invert = function(x, y) {\n    var gy = g - y,\n        l = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(x, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(gy)) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(gy);\n    if (gy * n < 0)\n      l -= _math_js__WEBPACK_IMPORTED_MODULE_0__.pi * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(x) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(gy);\n    return [l / n, g - (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(n) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(x * x + gy * gy)];\n  };\n\n  return project;\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  return (0,_conic_js__WEBPACK_IMPORTED_MODULE_2__.conicProjection)(conicEquidistantRaw)\n      .scale(131.154)\n      .center([0, 13.9389]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/conicEquidistant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/cylindricalEqualArea.js":
/*!********************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/cylindricalEqualArea.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cylindricalEqualAreaRaw: () => (/* binding */ cylindricalEqualAreaRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\nfunction cylindricalEqualAreaRaw(phi0) {\n  var cosPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi0);\n\n  function forward(lambda, phi) {\n    return [lambda * cosPhi0, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi) / cosPhi0];\n  }\n\n  forward.invert = function(x, y) {\n    return [x / cosPhi0, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(y * cosPhi0)];\n  };\n\n  return forward;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2N5bGluZHJpY2FsRXF1YWxBcmVhLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBDOztBQUVuQztBQUNQLGdCQUFnQiw2Q0FBRzs7QUFFbkI7QUFDQSw4QkFBOEIsNkNBQUc7QUFDakM7O0FBRUE7QUFDQSx5QkFBeUIsOENBQUk7QUFDN0I7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1nZW9cXHNyY1xccHJvamVjdGlvblxcY3lsaW5kcmljYWxFcXVhbEFyZWEuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHthc2luLCBjb3MsIHNpbn0gZnJvbSBcIi4uL21hdGguanNcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIGN5bGluZHJpY2FsRXF1YWxBcmVhUmF3KHBoaTApIHtcbiAgdmFyIGNvc1BoaTAgPSBjb3MocGhpMCk7XG5cbiAgZnVuY3Rpb24gZm9yd2FyZChsYW1iZGEsIHBoaSkge1xuICAgIHJldHVybiBbbGFtYmRhICogY29zUGhpMCwgc2luKHBoaSkgLyBjb3NQaGkwXTtcbiAgfVxuXG4gIGZvcndhcmQuaW52ZXJ0ID0gZnVuY3Rpb24oeCwgeSkge1xuICAgIHJldHVybiBbeCAvIGNvc1BoaTAsIGFzaW4oeSAqIGNvc1BoaTApXTtcbiAgfTtcblxuICByZXR1cm4gZm9yd2FyZDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/cylindricalEqualArea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/equalEarth.js":
/*!**********************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/equalEarth.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   equalEarthRaw: () => (/* binding */ equalEarthRaw)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\n\nvar A1 = 1.340264,\n    A2 = -0.081106,\n    A3 = 0.000893,\n    A4 = 0.003796,\n    M = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(3) / 2,\n    iterations = 12;\n\nfunction equalEarthRaw(lambda, phi) {\n  var l = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(M * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi)), l2 = l * l, l6 = l2 * l2 * l2;\n  return [\n    lambda * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(l) / (M * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2))),\n    l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2))\n  ];\n}\n\nequalEarthRaw.invert = function(x, y) {\n  var l = y, l2 = l * l, l6 = l2 * l2 * l2;\n  for (var i = 0, delta, fy, fpy; i < iterations; ++i) {\n    fy = l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2)) - y;\n    fpy = A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2);\n    l -= delta = fy / fpy, l2 = l * l, l6 = l2 * l2 * l2;\n    if ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(delta) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon2) break;\n  }\n  return [\n    M * x * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2)) / (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(l),\n    (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(l) / M)\n  ];\n};\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  return (0,_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(equalEarthRaw)\n      .scale(177.158);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/equalEarth.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/equirectangular.js":
/*!***************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/equirectangular.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   equirectangularRaw: () => (/* binding */ equirectangularRaw)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n\n\nfunction equirectangularRaw(lambda, phi) {\n  return [lambda, phi];\n}\n\nequirectangularRaw.invert = equirectangularRaw;\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  return (0,_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(equirectangularRaw)\n      .scale(152.63);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2VxdWlyZWN0YW5ndWxhci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBb0M7O0FBRTdCO0FBQ1A7QUFDQTs7QUFFQTs7QUFFQSw2QkFBZSxzQ0FBVztBQUMxQixTQUFTLHFEQUFVO0FBQ25CO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtZ2VvXFxzcmNcXHByb2plY3Rpb25cXGVxdWlyZWN0YW5ndWxhci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgcHJvamVjdGlvbiBmcm9tIFwiLi9pbmRleC5qc1wiO1xuXG5leHBvcnQgZnVuY3Rpb24gZXF1aXJlY3Rhbmd1bGFyUmF3KGxhbWJkYSwgcGhpKSB7XG4gIHJldHVybiBbbGFtYmRhLCBwaGldO1xufVxuXG5lcXVpcmVjdGFuZ3VsYXJSYXcuaW52ZXJ0ID0gZXF1aXJlY3Rhbmd1bGFyUmF3O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbigpIHtcbiAgcmV0dXJuIHByb2plY3Rpb24oZXF1aXJlY3Rhbmd1bGFyUmF3KVxuICAgICAgLnNjYWxlKDE1Mi42Myk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/equirectangular.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/fit.js":
/*!***************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/fit.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fitExtent: () => (/* binding */ fitExtent),\n/* harmony export */   fitHeight: () => (/* binding */ fitHeight),\n/* harmony export */   fitSize: () => (/* binding */ fitSize),\n/* harmony export */   fitWidth: () => (/* binding */ fitWidth)\n/* harmony export */ });\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../stream.js */ \"(ssr)/./node_modules/d3-geo/src/stream.js\");\n/* harmony import */ var _path_bounds_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../path/bounds.js */ \"(ssr)/./node_modules/d3-geo/src/path/bounds.js\");\n\n\n\nfunction fit(projection, fitBounds, object) {\n  var clip = projection.clipExtent && projection.clipExtent();\n  projection.scale(150).translate([0, 0]);\n  if (clip != null) projection.clipExtent(null);\n  (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projection.stream(_path_bounds_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]));\n  fitBounds(_path_bounds_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].result());\n  if (clip != null) projection.clipExtent(clip);\n  return projection;\n}\n\nfunction fitExtent(projection, extent, object) {\n  return fit(projection, function(b) {\n    var w = extent[1][0] - extent[0][0],\n        h = extent[1][1] - extent[0][1],\n        k = Math.min(w / (b[1][0] - b[0][0]), h / (b[1][1] - b[0][1])),\n        x = +extent[0][0] + (w - k * (b[1][0] + b[0][0])) / 2,\n        y = +extent[0][1] + (h - k * (b[1][1] + b[0][1])) / 2;\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n\nfunction fitSize(projection, size, object) {\n  return fitExtent(projection, [[0, 0], size], object);\n}\n\nfunction fitWidth(projection, width, object) {\n  return fit(projection, function(b) {\n    var w = +width,\n        k = w / (b[1][0] - b[0][0]),\n        x = (w - k * (b[1][0] + b[0][0])) / 2,\n        y = -k * b[0][1];\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n\nfunction fitHeight(projection, height, object) {\n  return fit(projection, function(b) {\n    var h = +height,\n        k = h / (b[1][1] - b[0][1]),\n        x = -k * b[0][0],\n        y = (h - k * (b[1][1] + b[0][1])) / 2;\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/fit.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/gnomonic.js":
/*!********************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/gnomonic.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   gnomonicRaw: () => (/* binding */ gnomonicRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _azimuthal_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./azimuthal.js */ \"(ssr)/./node_modules/d3-geo/src/projection/azimuthal.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n\n\n\n\nfunction gnomonicRaw(x, y) {\n  var cy = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y), k = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x) * cy;\n  return [cy * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x) / k, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y) / k];\n}\n\ngnomonicRaw.invert = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_1__.azimuthalInvert)(_math_js__WEBPACK_IMPORTED_MODULE_0__.atan);\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  return (0,_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(gnomonicRaw)\n      .scale(144.049)\n      .clipAngle(60);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2dub21vbmljLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTBDO0FBQ0s7QUFDWDs7QUFFN0I7QUFDUCxXQUFXLDZDQUFHLFNBQVMsNkNBQUc7QUFDMUIsZUFBZSw2Q0FBRyxTQUFTLDZDQUFHO0FBQzlCOztBQUVBLHFCQUFxQiw4REFBZSxDQUFDLDBDQUFJOztBQUV6Qyw2QkFBZSxzQ0FBVztBQUMxQixTQUFTLHFEQUFVO0FBQ25CO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1nZW9cXHNyY1xccHJvamVjdGlvblxcZ25vbW9uaWMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHthdGFuLCBjb3MsIHNpbn0gZnJvbSBcIi4uL21hdGguanNcIjtcbmltcG9ydCB7YXppbXV0aGFsSW52ZXJ0fSBmcm9tIFwiLi9hemltdXRoYWwuanNcIjtcbmltcG9ydCBwcm9qZWN0aW9uIGZyb20gXCIuL2luZGV4LmpzXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBnbm9tb25pY1Jhdyh4LCB5KSB7XG4gIHZhciBjeSA9IGNvcyh5KSwgayA9IGNvcyh4KSAqIGN5O1xuICByZXR1cm4gW2N5ICogc2luKHgpIC8gaywgc2luKHkpIC8ga107XG59XG5cbmdub21vbmljUmF3LmludmVydCA9IGF6aW11dGhhbEludmVydChhdGFuKTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oKSB7XG4gIHJldHVybiBwcm9qZWN0aW9uKGdub21vbmljUmF3KVxuICAgICAgLnNjYWxlKDE0NC4wNDkpXG4gICAgICAuY2xpcEFuZ2xlKDYwKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/gnomonic.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/identity.js":
/*!********************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/identity.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _clip_rectangle_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../clip/rectangle.js */ \"(ssr)/./node_modules/d3-geo/src/clip/rectangle.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../identity.js */ \"(ssr)/./node_modules/d3-geo/src/identity.js\");\n/* harmony import */ var _transform_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../transform.js */ \"(ssr)/./node_modules/d3-geo/src/transform.js\");\n/* harmony import */ var _fit_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./fit.js */ \"(ssr)/./node_modules/d3-geo/src/projection/fit.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\n\n\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  var k = 1, tx = 0, ty = 0, sx = 1, sy = 1, // scale, translate and reflect\n      alpha = 0, ca, sa, // angle\n      x0 = null, y0, x1, y1, // clip extent\n      kx = 1, ky = 1,\n      transform = (0,_transform_js__WEBPACK_IMPORTED_MODULE_0__.transformer)({\n        point: function(x, y) {\n          var p = projection([x, y])\n          this.stream.point(p[0], p[1]);\n        }\n      }),\n      postclip = _identity_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n      cache,\n      cacheStream;\n\n  function reset() {\n    kx = k * sx;\n    ky = k * sy;\n    cache = cacheStream = null;\n    return projection;\n  }\n\n  function projection (p) {\n    var x = p[0] * kx, y = p[1] * ky;\n    if (alpha) {\n      var t = y * ca - x * sa;\n      x = x * ca + y * sa;\n      y = t;\n    }    \n    return [x + tx, y + ty];\n  }\n  projection.invert = function(p) {\n    var x = p[0] - tx, y = p[1] - ty;\n    if (alpha) {\n      var t = y * ca + x * sa;\n      x = x * ca - y * sa;\n      y = t;\n    }\n    return [x / kx, y / ky];\n  };\n  projection.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = transform(postclip(cacheStream = stream));\n  };\n  projection.postclip = function(_) {\n    return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n  };\n  projection.clipExtent = function(_) {\n    return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, _identity_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) : (0,_clip_rectangle_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n  projection.scale = function(_) {\n    return arguments.length ? (k = +_, reset()) : k;\n  };\n  projection.translate = function(_) {\n    return arguments.length ? (tx = +_[0], ty = +_[1], reset()) : [tx, ty];\n  }\n  projection.angle = function(_) {\n    return arguments.length ? (alpha = _ % 360 * _math_js__WEBPACK_IMPORTED_MODULE_3__.radians, sa = (0,_math_js__WEBPACK_IMPORTED_MODULE_3__.sin)(alpha), ca = (0,_math_js__WEBPACK_IMPORTED_MODULE_3__.cos)(alpha), reset()) : alpha * _math_js__WEBPACK_IMPORTED_MODULE_3__.degrees;\n  };\n  projection.reflectX = function(_) {\n    return arguments.length ? (sx = _ ? -1 : 1, reset()) : sx < 0;\n  };\n  projection.reflectY = function(_) {\n    return arguments.length ? (sy = _ ? -1 : 1, reset()) : sy < 0;\n  };\n  projection.fitExtent = function(extent, object) {\n    return (0,_fit_js__WEBPACK_IMPORTED_MODULE_4__.fitExtent)(projection, extent, object);\n  };\n  projection.fitSize = function(size, object) {\n    return (0,_fit_js__WEBPACK_IMPORTED_MODULE_4__.fitSize)(projection, size, object);\n  };\n  projection.fitWidth = function(width, object) {\n    return (0,_fit_js__WEBPACK_IMPORTED_MODULE_4__.fitWidth)(projection, width, object);\n  };\n  projection.fitHeight = function(height, object) {\n    return (0,_fit_js__WEBPACK_IMPORTED_MODULE_4__.fitHeight)(projection, height, object);\n  };\n\n  return projection;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/identity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ projection),\n/* harmony export */   projectionMutator: () => (/* binding */ projectionMutator)\n/* harmony export */ });\n/* harmony import */ var _clip_antimeridian_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../clip/antimeridian.js */ \"(ssr)/./node_modules/d3-geo/src/clip/antimeridian.js\");\n/* harmony import */ var _clip_circle_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../clip/circle.js */ \"(ssr)/./node_modules/d3-geo/src/clip/circle.js\");\n/* harmony import */ var _clip_rectangle_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../clip/rectangle.js */ \"(ssr)/./node_modules/d3-geo/src/clip/rectangle.js\");\n/* harmony import */ var _compose_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../compose.js */ \"(ssr)/./node_modules/d3-geo/src/compose.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../identity.js */ \"(ssr)/./node_modules/d3-geo/src/identity.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _rotation_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../rotation.js */ \"(ssr)/./node_modules/d3-geo/src/rotation.js\");\n/* harmony import */ var _transform_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../transform.js */ \"(ssr)/./node_modules/d3-geo/src/transform.js\");\n/* harmony import */ var _fit_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./fit.js */ \"(ssr)/./node_modules/d3-geo/src/projection/fit.js\");\n/* harmony import */ var _resample_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./resample.js */ \"(ssr)/./node_modules/d3-geo/src/projection/resample.js\");\n\n\n\n\n\n\n\n\n\n\n\nvar transformRadians = (0,_transform_js__WEBPACK_IMPORTED_MODULE_0__.transformer)({\n  point: function(x, y) {\n    this.stream.point(x * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, y * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians);\n  }\n});\n\nfunction transformRotate(rotate) {\n  return (0,_transform_js__WEBPACK_IMPORTED_MODULE_0__.transformer)({\n    point: function(x, y) {\n      var r = rotate(x, y);\n      return this.stream.point(r[0], r[1]);\n    }\n  });\n}\n\nfunction scaleTranslate(k, dx, dy, sx, sy) {\n  function transform(x, y) {\n    x *= sx; y *= sy;\n    return [dx + k * x, dy - k * y];\n  }\n  transform.invert = function(x, y) {\n    return [(x - dx) / k * sx, (dy - y) / k * sy];\n  };\n  return transform;\n}\n\nfunction scaleTranslateRotate(k, dx, dy, sx, sy, alpha) {\n  if (!alpha) return scaleTranslate(k, dx, dy, sx, sy);\n  var cosAlpha = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(alpha),\n      sinAlpha = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(alpha),\n      a = cosAlpha * k,\n      b = sinAlpha * k,\n      ai = cosAlpha / k,\n      bi = sinAlpha / k,\n      ci = (sinAlpha * dy - cosAlpha * dx) / k,\n      fi = (sinAlpha * dx + cosAlpha * dy) / k;\n  function transform(x, y) {\n    x *= sx; y *= sy;\n    return [a * x - b * y + dx, dy - b * x - a * y];\n  }\n  transform.invert = function(x, y) {\n    return [sx * (ai * x - bi * y + ci), sy * (fi - bi * x - ai * y)];\n  };\n  return transform;\n}\n\nfunction projection(project) {\n  return projectionMutator(function() { return project; })();\n}\n\nfunction projectionMutator(projectAt) {\n  var project,\n      k = 150, // scale\n      x = 480, y = 250, // translate\n      lambda = 0, phi = 0, // center\n      deltaLambda = 0, deltaPhi = 0, deltaGamma = 0, rotate, // pre-rotate\n      alpha = 0, // post-rotate angle\n      sx = 1, // reflectX\n      sy = 1, // reflectX\n      theta = null, preclip = _clip_antimeridian_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"], // pre-clip angle\n      x0 = null, y0, x1, y1, postclip = _identity_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], // post-clip extent\n      delta2 = 0.5, // precision\n      projectResample,\n      projectTransform,\n      projectRotateTransform,\n      cache,\n      cacheStream;\n\n  function projection(point) {\n    return projectRotateTransform(point[0] * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, point[1] * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians);\n  }\n\n  function invert(point) {\n    point = projectRotateTransform.invert(point[0], point[1]);\n    return point && [point[0] * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees, point[1] * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees];\n  }\n\n  projection.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = transformRadians(transformRotate(rotate)(preclip(projectResample(postclip(cacheStream = stream)))));\n  };\n\n  projection.preclip = function(_) {\n    return arguments.length ? (preclip = _, theta = undefined, reset()) : preclip;\n  };\n\n  projection.postclip = function(_) {\n    return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n  };\n\n  projection.clipAngle = function(_) {\n    return arguments.length ? (preclip = +_ ? (0,_clip_circle_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(theta = _ * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians) : (theta = null, _clip_antimeridian_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]), reset()) : theta * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees;\n  };\n\n  projection.clipExtent = function(_) {\n    return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, _identity_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]) : (0,_clip_rectangle_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n\n  projection.scale = function(_) {\n    return arguments.length ? (k = +_, recenter()) : k;\n  };\n\n  projection.translate = function(_) {\n    return arguments.length ? (x = +_[0], y = +_[1], recenter()) : [x, y];\n  };\n\n  projection.center = function(_) {\n    return arguments.length ? (lambda = _[0] % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi = _[1] % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, recenter()) : [lambda * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees, phi * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees];\n  };\n\n  projection.rotate = function(_) {\n    return arguments.length ? (deltaLambda = _[0] % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, deltaPhi = _[1] % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, deltaGamma = _.length > 2 ? _[2] % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians : 0, recenter()) : [deltaLambda * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees, deltaPhi * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees, deltaGamma * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees];\n  };\n\n  projection.angle = function(_) {\n    return arguments.length ? (alpha = _ % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, recenter()) : alpha * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees;\n  };\n\n  projection.reflectX = function(_) {\n    return arguments.length ? (sx = _ ? -1 : 1, recenter()) : sx < 0;\n  };\n\n  projection.reflectY = function(_) {\n    return arguments.length ? (sy = _ ? -1 : 1, recenter()) : sy < 0;\n  };\n\n  projection.precision = function(_) {\n    return arguments.length ? (projectResample = (0,_resample_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(projectTransform, delta2 = _ * _), reset()) : (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sqrt)(delta2);\n  };\n\n  projection.fitExtent = function(extent, object) {\n    return (0,_fit_js__WEBPACK_IMPORTED_MODULE_7__.fitExtent)(projection, extent, object);\n  };\n\n  projection.fitSize = function(size, object) {\n    return (0,_fit_js__WEBPACK_IMPORTED_MODULE_7__.fitSize)(projection, size, object);\n  };\n\n  projection.fitWidth = function(width, object) {\n    return (0,_fit_js__WEBPACK_IMPORTED_MODULE_7__.fitWidth)(projection, width, object);\n  };\n\n  projection.fitHeight = function(height, object) {\n    return (0,_fit_js__WEBPACK_IMPORTED_MODULE_7__.fitHeight)(projection, height, object);\n  };\n\n  function recenter() {\n    var center = scaleTranslateRotate(k, 0, 0, sx, sy, alpha).apply(null, project(lambda, phi)),\n        transform = scaleTranslateRotate(k, x - center[0], y - center[1], sx, sy, alpha);\n    rotate = (0,_rotation_js__WEBPACK_IMPORTED_MODULE_8__.rotateRadians)(deltaLambda, deltaPhi, deltaGamma);\n    projectTransform = (0,_compose_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(project, transform);\n    projectRotateTransform = (0,_compose_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(rotate, projectTransform);\n    projectResample = (0,_resample_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(projectTransform, delta2);\n    return reset();\n  }\n\n  function reset() {\n    cache = cacheStream = null;\n    return projection;\n  }\n\n  return function() {\n    project = projectAt.apply(this, arguments);\n    projection.invert = project.invert && invert;\n    return recenter();\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/mercator.js":
/*!********************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/mercator.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   mercatorProjection: () => (/* binding */ mercatorProjection),\n/* harmony export */   mercatorRaw: () => (/* binding */ mercatorRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _rotation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../rotation.js */ \"(ssr)/./node_modules/d3-geo/src/rotation.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n\n\n\n\nfunction mercatorRaw(lambda, phi) {\n  return [lambda, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.log)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.tan)((_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + phi) / 2))];\n}\n\nmercatorRaw.invert = function(x, y) {\n  return [x, 2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.exp)(y)) - _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi];\n};\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  return mercatorProjection(mercatorRaw)\n      .scale(961 / _math_js__WEBPACK_IMPORTED_MODULE_0__.tau);\n}\n\nfunction mercatorProjection(project) {\n  var m = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(project),\n      center = m.center,\n      scale = m.scale,\n      translate = m.translate,\n      clipExtent = m.clipExtent,\n      x0 = null, y0, x1, y1; // clip extent\n\n  m.scale = function(_) {\n    return arguments.length ? (scale(_), reclip()) : scale();\n  };\n\n  m.translate = function(_) {\n    return arguments.length ? (translate(_), reclip()) : translate();\n  };\n\n  m.center = function(_) {\n    return arguments.length ? (center(_), reclip()) : center();\n  };\n\n  m.clipExtent = function(_) {\n    return arguments.length ? ((_ == null ? x0 = y0 = x1 = y1 = null : (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1])), reclip()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n\n  function reclip() {\n    var k = _math_js__WEBPACK_IMPORTED_MODULE_0__.pi * scale(),\n        t = m((0,_rotation_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(m.rotate()).invert([0, 0]));\n    return clipExtent(x0 == null\n        ? [[t[0] - k, t[1] - k], [t[0] + k, t[1] + k]] : project === mercatorRaw\n        ? [[Math.max(t[0] - k, x0), y0], [Math.min(t[0] + k, x1), y1]]\n        : [[x0, Math.max(t[1] - k, y0)], [x1, Math.min(t[1] + k, y1)]]);\n  }\n\n  return reclip();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/mercator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/naturalEarth1.js":
/*!*************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/naturalEarth1.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   naturalEarth1Raw: () => (/* binding */ naturalEarth1Raw)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\n\nfunction naturalEarth1Raw(lambda, phi) {\n  var phi2 = phi * phi, phi4 = phi2 * phi2;\n  return [\n    lambda * (0.8707 - 0.131979 * phi2 + phi4 * (-0.013791 + phi4 * (0.003971 * phi2 - 0.001529 * phi4))),\n    phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4)))\n  ];\n}\n\nnaturalEarth1Raw.invert = function(x, y) {\n  var phi = y, i = 25, delta;\n  do {\n    var phi2 = phi * phi, phi4 = phi2 * phi2;\n    phi -= delta = (phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4))) - y) /\n        (1.007226 + phi2 * (0.015085 * 3 + phi4 * (-0.044475 * 7 + 0.028874 * 9 * phi2 - 0.005916 * 11 * phi4)));\n  } while ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(delta) > _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon && --i > 0);\n  return [\n    x / (0.8707 + (phi2 = phi * phi) * (-0.131979 + phi2 * (-0.013791 + phi2 * phi2 * phi2 * (0.003971 - 0.001529 * phi2)))),\n    phi\n  ];\n};\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  return (0,_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(naturalEarth1Raw)\n      .scale(175.295);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL25hdHVyYWxFYXJ0aDEuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFvQztBQUNJOztBQUVqQztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLE9BQU8sNkNBQUcsVUFBVSw2Q0FBTztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDZCQUFlLHNDQUFXO0FBQzFCLFNBQVMscURBQVU7QUFDbkI7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1nZW9cXHNyY1xccHJvamVjdGlvblxcbmF0dXJhbEVhcnRoMS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgcHJvamVjdGlvbiBmcm9tIFwiLi9pbmRleC5qc1wiO1xuaW1wb3J0IHthYnMsIGVwc2lsb259IGZyb20gXCIuLi9tYXRoLmpzXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBuYXR1cmFsRWFydGgxUmF3KGxhbWJkYSwgcGhpKSB7XG4gIHZhciBwaGkyID0gcGhpICogcGhpLCBwaGk0ID0gcGhpMiAqIHBoaTI7XG4gIHJldHVybiBbXG4gICAgbGFtYmRhICogKDAuODcwNyAtIDAuMTMxOTc5ICogcGhpMiArIHBoaTQgKiAoLTAuMDEzNzkxICsgcGhpNCAqICgwLjAwMzk3MSAqIHBoaTIgLSAwLjAwMTUyOSAqIHBoaTQpKSksXG4gICAgcGhpICogKDEuMDA3MjI2ICsgcGhpMiAqICgwLjAxNTA4NSArIHBoaTQgKiAoLTAuMDQ0NDc1ICsgMC4wMjg4NzQgKiBwaGkyIC0gMC4wMDU5MTYgKiBwaGk0KSkpXG4gIF07XG59XG5cbm5hdHVyYWxFYXJ0aDFSYXcuaW52ZXJ0ID0gZnVuY3Rpb24oeCwgeSkge1xuICB2YXIgcGhpID0geSwgaSA9IDI1LCBkZWx0YTtcbiAgZG8ge1xuICAgIHZhciBwaGkyID0gcGhpICogcGhpLCBwaGk0ID0gcGhpMiAqIHBoaTI7XG4gICAgcGhpIC09IGRlbHRhID0gKHBoaSAqICgxLjAwNzIyNiArIHBoaTIgKiAoMC4wMTUwODUgKyBwaGk0ICogKC0wLjA0NDQ3NSArIDAuMDI4ODc0ICogcGhpMiAtIDAuMDA1OTE2ICogcGhpNCkpKSAtIHkpIC9cbiAgICAgICAgKDEuMDA3MjI2ICsgcGhpMiAqICgwLjAxNTA4NSAqIDMgKyBwaGk0ICogKC0wLjA0NDQ3NSAqIDcgKyAwLjAyODg3NCAqIDkgKiBwaGkyIC0gMC4wMDU5MTYgKiAxMSAqIHBoaTQpKSk7XG4gIH0gd2hpbGUgKGFicyhkZWx0YSkgPiBlcHNpbG9uICYmIC0taSA+IDApO1xuICByZXR1cm4gW1xuICAgIHggLyAoMC44NzA3ICsgKHBoaTIgPSBwaGkgKiBwaGkpICogKC0wLjEzMTk3OSArIHBoaTIgKiAoLTAuMDEzNzkxICsgcGhpMiAqIHBoaTIgKiBwaGkyICogKDAuMDAzOTcxIC0gMC4wMDE1MjkgKiBwaGkyKSkpKSxcbiAgICBwaGlcbiAgXTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gcHJvamVjdGlvbihuYXR1cmFsRWFydGgxUmF3KVxuICAgICAgLnNjYWxlKDE3NS4yOTUpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/naturalEarth1.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/orthographic.js":
/*!************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/orthographic.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   orthographicRaw: () => (/* binding */ orthographicRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _azimuthal_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./azimuthal.js */ \"(ssr)/./node_modules/d3-geo/src/projection/azimuthal.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n\n\n\n\nfunction orthographicRaw(x, y) {\n  return [(0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x), (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y)];\n}\n\northographicRaw.invert = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_1__.azimuthalInvert)(_math_js__WEBPACK_IMPORTED_MODULE_0__.asin);\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  return (0,_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(orthographicRaw)\n      .scale(249.5)\n      .clipAngle(90 + _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL29ydGhvZ3JhcGhpYy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFtRDtBQUNKO0FBQ1g7O0FBRTdCO0FBQ1AsVUFBVSw2Q0FBRyxNQUFNLDZDQUFHLEtBQUssNkNBQUc7QUFDOUI7O0FBRUEseUJBQXlCLDhEQUFlLENBQUMsMENBQUk7O0FBRTdDLDZCQUFlLHNDQUFXO0FBQzFCLFNBQVMscURBQVU7QUFDbkI7QUFDQSxzQkFBc0IsNkNBQU87QUFDN0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtZ2VvXFxzcmNcXHByb2plY3Rpb25cXG9ydGhvZ3JhcGhpYy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2FzaW4sIGNvcywgZXBzaWxvbiwgc2lufSBmcm9tIFwiLi4vbWF0aC5qc1wiO1xuaW1wb3J0IHthemltdXRoYWxJbnZlcnR9IGZyb20gXCIuL2F6aW11dGhhbC5qc1wiO1xuaW1wb3J0IHByb2plY3Rpb24gZnJvbSBcIi4vaW5kZXguanNcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIG9ydGhvZ3JhcGhpY1Jhdyh4LCB5KSB7XG4gIHJldHVybiBbY29zKHkpICogc2luKHgpLCBzaW4oeSldO1xufVxuXG5vcnRob2dyYXBoaWNSYXcuaW52ZXJ0ID0gYXppbXV0aGFsSW52ZXJ0KGFzaW4pO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbigpIHtcbiAgcmV0dXJuIHByb2plY3Rpb24ob3J0aG9ncmFwaGljUmF3KVxuICAgICAgLnNjYWxlKDI0OS41KVxuICAgICAgLmNsaXBBbmdsZSg5MCArIGVwc2lsb24pO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/orthographic.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/resample.js":
/*!********************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/resample.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cartesian_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../cartesian.js */ \"(ssr)/./node_modules/d3-geo/src/cartesian.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _transform_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../transform.js */ \"(ssr)/./node_modules/d3-geo/src/transform.js\");\n\n\n\n\nvar maxDepth = 16, // maximum depth of subdivision\n    cosMinDistance = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(30 * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians); // cos(minimum angular distance)\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(project, delta2) {\n  return +delta2 ? resample(project, delta2) : resampleNone(project);\n}\n\nfunction resampleNone(project) {\n  return (0,_transform_js__WEBPACK_IMPORTED_MODULE_1__.transformer)({\n    point: function(x, y) {\n      x = project(x, y);\n      this.stream.point(x[0], x[1]);\n    }\n  });\n}\n\nfunction resample(project, delta2) {\n\n  function resampleLineTo(x0, y0, lambda0, a0, b0, c0, x1, y1, lambda1, a1, b1, c1, depth, stream) {\n    var dx = x1 - x0,\n        dy = y1 - y0,\n        d2 = dx * dx + dy * dy;\n    if (d2 > 4 * delta2 && depth--) {\n      var a = a0 + a1,\n          b = b0 + b1,\n          c = c0 + c1,\n          m = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(a * a + b * b + c * c),\n          phi2 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(c /= m),\n          lambda2 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(c) - 1) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon || (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(lambda0 - lambda1) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon ? (lambda0 + lambda1) / 2 : (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(b, a),\n          p = project(lambda2, phi2),\n          x2 = p[0],\n          y2 = p[1],\n          dx2 = x2 - x0,\n          dy2 = y2 - y0,\n          dz = dy * dx2 - dx * dy2;\n      if (dz * dz / d2 > delta2 // perpendicular projected distance\n          || (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)((dx * dx2 + dy * dy2) / d2 - 0.5) > 0.3 // midpoint close to an end\n          || a0 * a1 + b0 * b1 + c0 * c1 < cosMinDistance) { // angular distance\n        resampleLineTo(x0, y0, lambda0, a0, b0, c0, x2, y2, lambda2, a /= m, b /= m, c, depth, stream);\n        stream.point(x2, y2);\n        resampleLineTo(x2, y2, lambda2, a, b, c, x1, y1, lambda1, a1, b1, c1, depth, stream);\n      }\n    }\n  }\n  return function(stream) {\n    var lambda00, x00, y00, a00, b00, c00, // first point\n        lambda0, x0, y0, a0, b0, c0; // previous point\n\n    var resampleStream = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: function() { stream.polygonStart(); resampleStream.lineStart = ringStart; },\n      polygonEnd: function() { stream.polygonEnd(); resampleStream.lineStart = lineStart; }\n    };\n\n    function point(x, y) {\n      x = project(x, y);\n      stream.point(x[0], x[1]);\n    }\n\n    function lineStart() {\n      x0 = NaN;\n      resampleStream.point = linePoint;\n      stream.lineStart();\n    }\n\n    function linePoint(lambda, phi) {\n      var c = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesian)([lambda, phi]), p = project(lambda, phi);\n      resampleLineTo(x0, y0, lambda0, a0, b0, c0, x0 = p[0], y0 = p[1], lambda0 = lambda, a0 = c[0], b0 = c[1], c0 = c[2], maxDepth, stream);\n      stream.point(x0, y0);\n    }\n\n    function lineEnd() {\n      resampleStream.point = point;\n      stream.lineEnd();\n    }\n\n    function ringStart() {\n      lineStart();\n      resampleStream.point = ringPoint;\n      resampleStream.lineEnd = ringEnd;\n    }\n\n    function ringPoint(lambda, phi) {\n      linePoint(lambda00 = lambda, phi), x00 = x0, y00 = y0, a00 = a0, b00 = b0, c00 = c0;\n      resampleStream.point = linePoint;\n    }\n\n    function ringEnd() {\n      resampleLineTo(x0, y0, lambda0, a0, b0, c0, x00, y00, lambda00, a00, b00, c00, maxDepth, stream);\n      resampleStream.lineEnd = lineEnd;\n      lineEnd();\n    }\n\n    return resampleStream;\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/resample.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/stereographic.js":
/*!*************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/stereographic.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   stereographicRaw: () => (/* binding */ stereographicRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _azimuthal_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./azimuthal.js */ \"(ssr)/./node_modules/d3-geo/src/projection/azimuthal.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n\n\n\n\nfunction stereographicRaw(x, y) {\n  var cy = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y), k = 1 + (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x) * cy;\n  return [cy * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x) / k, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y) / k];\n}\n\nstereographicRaw.invert = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_1__.azimuthalInvert)(function(z) {\n  return 2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan)(z);\n});\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  return (0,_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(stereographicRaw)\n      .scale(250)\n      .clipAngle(142);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL3N0ZXJlb2dyYXBoaWMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMEM7QUFDSztBQUNYOztBQUU3QjtBQUNQLFdBQVcsNkNBQUcsYUFBYSw2Q0FBRztBQUM5QixlQUFlLDZDQUFHLFNBQVMsNkNBQUc7QUFDOUI7O0FBRUEsMEJBQTBCLDhEQUFlO0FBQ3pDLGFBQWEsOENBQUk7QUFDakIsQ0FBQzs7QUFFRCw2QkFBZSxzQ0FBVztBQUMxQixTQUFTLHFEQUFVO0FBQ25CO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1nZW9cXHNyY1xccHJvamVjdGlvblxcc3RlcmVvZ3JhcGhpYy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2F0YW4sIGNvcywgc2lufSBmcm9tIFwiLi4vbWF0aC5qc1wiO1xuaW1wb3J0IHthemltdXRoYWxJbnZlcnR9IGZyb20gXCIuL2F6aW11dGhhbC5qc1wiO1xuaW1wb3J0IHByb2plY3Rpb24gZnJvbSBcIi4vaW5kZXguanNcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIHN0ZXJlb2dyYXBoaWNSYXcoeCwgeSkge1xuICB2YXIgY3kgPSBjb3MoeSksIGsgPSAxICsgY29zKHgpICogY3k7XG4gIHJldHVybiBbY3kgKiBzaW4oeCkgLyBrLCBzaW4oeSkgLyBrXTtcbn1cblxuc3RlcmVvZ3JhcGhpY1Jhdy5pbnZlcnQgPSBhemltdXRoYWxJbnZlcnQoZnVuY3Rpb24oeikge1xuICByZXR1cm4gMiAqIGF0YW4oeik7XG59KTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oKSB7XG4gIHJldHVybiBwcm9qZWN0aW9uKHN0ZXJlb2dyYXBoaWNSYXcpXG4gICAgICAuc2NhbGUoMjUwKVxuICAgICAgLmNsaXBBbmdsZSgxNDIpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/stereographic.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/transverseMercator.js":
/*!******************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/transverseMercator.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   transverseMercatorRaw: () => (/* binding */ transverseMercatorRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _mercator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mercator.js */ \"(ssr)/./node_modules/d3-geo/src/projection/mercator.js\");\n\n\n\nfunction transverseMercatorRaw(lambda, phi) {\n  return [(0,_math_js__WEBPACK_IMPORTED_MODULE_0__.log)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.tan)((_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + phi) / 2)), -lambda];\n}\n\ntransverseMercatorRaw.invert = function(x, y) {\n  return [-y, 2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.exp)(x)) - _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi];\n};\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  var m = (0,_mercator_js__WEBPACK_IMPORTED_MODULE_1__.mercatorProjection)(transverseMercatorRaw),\n      center = m.center,\n      rotate = m.rotate;\n\n  m.center = function(_) {\n    return arguments.length ? center([-_[1], _[0]]) : (_ = center(), [_[1], -_[0]]);\n  };\n\n  m.rotate = function(_) {\n    return arguments.length ? rotate([_[0], _[1], _.length > 2 ? _[2] + 90 : 90]) : (_ = rotate(), [_[0], _[1], _[2] - 90]);\n  };\n\n  return rotate([0, 0, 90])\n      .scale(159.155);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL3RyYW5zdmVyc2VNZXJjYXRvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXVEO0FBQ047O0FBRTFDO0FBQ1AsVUFBVSw2Q0FBRyxDQUFDLDZDQUFHLEVBQUUsNENBQU07QUFDekI7O0FBRUE7QUFDQSxrQkFBa0IsOENBQUksQ0FBQyw2Q0FBRyxPQUFPLDRDQUFNO0FBQ3ZDOztBQUVBLDZCQUFlLHNDQUFXO0FBQzFCLFVBQVUsZ0VBQWtCO0FBQzVCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtZ2VvXFxzcmNcXHByb2plY3Rpb25cXHRyYW5zdmVyc2VNZXJjYXRvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2F0YW4sIGV4cCwgaGFsZlBpLCBsb2csIHRhbn0gZnJvbSBcIi4uL21hdGguanNcIjtcbmltcG9ydCB7bWVyY2F0b3JQcm9qZWN0aW9ufSBmcm9tIFwiLi9tZXJjYXRvci5qc1wiO1xuXG5leHBvcnQgZnVuY3Rpb24gdHJhbnN2ZXJzZU1lcmNhdG9yUmF3KGxhbWJkYSwgcGhpKSB7XG4gIHJldHVybiBbbG9nKHRhbigoaGFsZlBpICsgcGhpKSAvIDIpKSwgLWxhbWJkYV07XG59XG5cbnRyYW5zdmVyc2VNZXJjYXRvclJhdy5pbnZlcnQgPSBmdW5jdGlvbih4LCB5KSB7XG4gIHJldHVybiBbLXksIDIgKiBhdGFuKGV4cCh4KSkgLSBoYWxmUGldO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oKSB7XG4gIHZhciBtID0gbWVyY2F0b3JQcm9qZWN0aW9uKHRyYW5zdmVyc2VNZXJjYXRvclJhdyksXG4gICAgICBjZW50ZXIgPSBtLmNlbnRlcixcbiAgICAgIHJvdGF0ZSA9IG0ucm90YXRlO1xuXG4gIG0uY2VudGVyID0gZnVuY3Rpb24oXykge1xuICAgIHJldHVybiBhcmd1bWVudHMubGVuZ3RoID8gY2VudGVyKFstX1sxXSwgX1swXV0pIDogKF8gPSBjZW50ZXIoKSwgW19bMV0sIC1fWzBdXSk7XG4gIH07XG5cbiAgbS5yb3RhdGUgPSBmdW5jdGlvbihfKSB7XG4gICAgcmV0dXJuIGFyZ3VtZW50cy5sZW5ndGggPyByb3RhdGUoW19bMF0sIF9bMV0sIF8ubGVuZ3RoID4gMiA/IF9bMl0gKyA5MCA6IDkwXSkgOiAoXyA9IHJvdGF0ZSgpLCBbX1swXSwgX1sxXSwgX1syXSAtIDkwXSk7XG4gIH07XG5cbiAgcmV0dXJuIHJvdGF0ZShbMCwgMCwgOTBdKVxuICAgICAgLnNjYWxlKDE1OS4xNTUpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/transverseMercator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/rotation.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-geo/src/rotation.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   rotateRadians: () => (/* binding */ rotateRadians)\n/* harmony export */ });\n/* harmony import */ var _compose_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./compose.js */ \"(ssr)/./node_modules/d3-geo/src/compose.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\n\nfunction rotationIdentity(lambda, phi) {\n  if ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(lambda) > _math_js__WEBPACK_IMPORTED_MODULE_0__.pi) lambda -= Math.round(lambda / _math_js__WEBPACK_IMPORTED_MODULE_0__.tau) * _math_js__WEBPACK_IMPORTED_MODULE_0__.tau;\n  return [lambda, phi];\n}\n\nrotationIdentity.invert = rotationIdentity;\n\nfunction rotateRadians(deltaLambda, deltaPhi, deltaGamma) {\n  return (deltaLambda %= _math_js__WEBPACK_IMPORTED_MODULE_0__.tau) ? (deltaPhi || deltaGamma ? (0,_compose_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(rotationLambda(deltaLambda), rotationPhiGamma(deltaPhi, deltaGamma))\n    : rotationLambda(deltaLambda))\n    : (deltaPhi || deltaGamma ? rotationPhiGamma(deltaPhi, deltaGamma)\n    : rotationIdentity);\n}\n\nfunction forwardRotationLambda(deltaLambda) {\n  return function(lambda, phi) {\n    lambda += deltaLambda;\n    if ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(lambda) > _math_js__WEBPACK_IMPORTED_MODULE_0__.pi) lambda -= Math.round(lambda / _math_js__WEBPACK_IMPORTED_MODULE_0__.tau) * _math_js__WEBPACK_IMPORTED_MODULE_0__.tau;\n    return [lambda, phi];\n  };\n}\n\nfunction rotationLambda(deltaLambda) {\n  var rotation = forwardRotationLambda(deltaLambda);\n  rotation.invert = forwardRotationLambda(-deltaLambda);\n  return rotation;\n}\n\nfunction rotationPhiGamma(deltaPhi, deltaGamma) {\n  var cosDeltaPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(deltaPhi),\n      sinDeltaPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(deltaPhi),\n      cosDeltaGamma = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(deltaGamma),\n      sinDeltaGamma = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(deltaGamma);\n\n  function rotation(lambda, phi) {\n    var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi),\n        x = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(lambda) * cosPhi,\n        y = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(lambda) * cosPhi,\n        z = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi),\n        k = z * cosDeltaPhi + x * sinDeltaPhi;\n    return [\n      (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(y * cosDeltaGamma - k * sinDeltaGamma, x * cosDeltaPhi - z * sinDeltaPhi),\n      (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(k * cosDeltaGamma + y * sinDeltaGamma)\n    ];\n  }\n\n  rotation.invert = function(lambda, phi) {\n    var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi),\n        x = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(lambda) * cosPhi,\n        y = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(lambda) * cosPhi,\n        z = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi),\n        k = z * cosDeltaGamma - y * sinDeltaGamma;\n    return [\n      (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(y * cosDeltaGamma + z * sinDeltaGamma, x * cosDeltaPhi + k * sinDeltaPhi),\n      (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(k * cosDeltaPhi - x * sinDeltaPhi)\n    ];\n  };\n\n  return rotation;\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(rotate) {\n  rotate = rotateRadians(rotate[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, rotate[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, rotate.length > 2 ? rotate[2] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians : 0);\n\n  function forward(coordinates) {\n    coordinates = rotate(coordinates[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, coordinates[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians);\n    return coordinates[0] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, coordinates[1] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, coordinates;\n  }\n\n  forward.invert = function(coordinates) {\n    coordinates = rotate.invert(coordinates[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, coordinates[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians);\n    return coordinates[0] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, coordinates[1] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, coordinates;\n  };\n\n  return forward;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9yb3RhdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQW1DO0FBQzZDOztBQUVoRjtBQUNBLE1BQU0sNkNBQUcsV0FBVyx3Q0FBRSxnQ0FBZ0MseUNBQUcsSUFBSSx5Q0FBRztBQUNoRTtBQUNBOztBQUVBOztBQUVPO0FBQ1AseUJBQXlCLHlDQUFHLDhCQUE4Qix1REFBTztBQUNqRTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDZDQUFHLFdBQVcsd0NBQUUsZ0NBQWdDLHlDQUFHLElBQUkseUNBQUc7QUFDbEU7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxvQkFBb0IsNkNBQUc7QUFDdkIsb0JBQW9CLDZDQUFHO0FBQ3ZCLHNCQUFzQiw2Q0FBRztBQUN6QixzQkFBc0IsNkNBQUc7O0FBRXpCO0FBQ0EsaUJBQWlCLDZDQUFHO0FBQ3BCLFlBQVksNkNBQUc7QUFDZixZQUFZLDZDQUFHO0FBQ2YsWUFBWSw2Q0FBRztBQUNmO0FBQ0E7QUFDQSxNQUFNLCtDQUFLO0FBQ1gsTUFBTSw4Q0FBSTtBQUNWO0FBQ0E7O0FBRUE7QUFDQSxpQkFBaUIsNkNBQUc7QUFDcEIsWUFBWSw2Q0FBRztBQUNmLFlBQVksNkNBQUc7QUFDZixZQUFZLDZDQUFHO0FBQ2Y7QUFDQTtBQUNBLE1BQU0sK0NBQUs7QUFDWCxNQUFNLDhDQUFJO0FBQ1Y7QUFDQTs7QUFFQTtBQUNBOztBQUVBLDZCQUFlLG9DQUFTO0FBQ3hCLHFDQUFxQyw2Q0FBTyxjQUFjLDZDQUFPLGtDQUFrQyw2Q0FBTzs7QUFFMUc7QUFDQSwwQ0FBMEMsNkNBQU8sbUJBQW1CLDZDQUFPO0FBQzNFLDZCQUE2Qiw2Q0FBTyxvQkFBb0IsNkNBQU87QUFDL0Q7O0FBRUE7QUFDQSxpREFBaUQsNkNBQU8sbUJBQW1CLDZDQUFPO0FBQ2xGLDZCQUE2Qiw2Q0FBTyxvQkFBb0IsNkNBQU87QUFDL0Q7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1nZW9cXHNyY1xccm90YXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNvbXBvc2UgZnJvbSBcIi4vY29tcG9zZS5qc1wiO1xuaW1wb3J0IHthYnMsIGFzaW4sIGF0YW4yLCBjb3MsIGRlZ3JlZXMsIHBpLCByYWRpYW5zLCBzaW4sIHRhdX0gZnJvbSBcIi4vbWF0aC5qc1wiO1xuXG5mdW5jdGlvbiByb3RhdGlvbklkZW50aXR5KGxhbWJkYSwgcGhpKSB7XG4gIGlmIChhYnMobGFtYmRhKSA+IHBpKSBsYW1iZGEgLT0gTWF0aC5yb3VuZChsYW1iZGEgLyB0YXUpICogdGF1O1xuICByZXR1cm4gW2xhbWJkYSwgcGhpXTtcbn1cblxucm90YXRpb25JZGVudGl0eS5pbnZlcnQgPSByb3RhdGlvbklkZW50aXR5O1xuXG5leHBvcnQgZnVuY3Rpb24gcm90YXRlUmFkaWFucyhkZWx0YUxhbWJkYSwgZGVsdGFQaGksIGRlbHRhR2FtbWEpIHtcbiAgcmV0dXJuIChkZWx0YUxhbWJkYSAlPSB0YXUpID8gKGRlbHRhUGhpIHx8IGRlbHRhR2FtbWEgPyBjb21wb3NlKHJvdGF0aW9uTGFtYmRhKGRlbHRhTGFtYmRhKSwgcm90YXRpb25QaGlHYW1tYShkZWx0YVBoaSwgZGVsdGFHYW1tYSkpXG4gICAgOiByb3RhdGlvbkxhbWJkYShkZWx0YUxhbWJkYSkpXG4gICAgOiAoZGVsdGFQaGkgfHwgZGVsdGFHYW1tYSA/IHJvdGF0aW9uUGhpR2FtbWEoZGVsdGFQaGksIGRlbHRhR2FtbWEpXG4gICAgOiByb3RhdGlvbklkZW50aXR5KTtcbn1cblxuZnVuY3Rpb24gZm9yd2FyZFJvdGF0aW9uTGFtYmRhKGRlbHRhTGFtYmRhKSB7XG4gIHJldHVybiBmdW5jdGlvbihsYW1iZGEsIHBoaSkge1xuICAgIGxhbWJkYSArPSBkZWx0YUxhbWJkYTtcbiAgICBpZiAoYWJzKGxhbWJkYSkgPiBwaSkgbGFtYmRhIC09IE1hdGgucm91bmQobGFtYmRhIC8gdGF1KSAqIHRhdTtcbiAgICByZXR1cm4gW2xhbWJkYSwgcGhpXTtcbiAgfTtcbn1cblxuZnVuY3Rpb24gcm90YXRpb25MYW1iZGEoZGVsdGFMYW1iZGEpIHtcbiAgdmFyIHJvdGF0aW9uID0gZm9yd2FyZFJvdGF0aW9uTGFtYmRhKGRlbHRhTGFtYmRhKTtcbiAgcm90YXRpb24uaW52ZXJ0ID0gZm9yd2FyZFJvdGF0aW9uTGFtYmRhKC1kZWx0YUxhbWJkYSk7XG4gIHJldHVybiByb3RhdGlvbjtcbn1cblxuZnVuY3Rpb24gcm90YXRpb25QaGlHYW1tYShkZWx0YVBoaSwgZGVsdGFHYW1tYSkge1xuICB2YXIgY29zRGVsdGFQaGkgPSBjb3MoZGVsdGFQaGkpLFxuICAgICAgc2luRGVsdGFQaGkgPSBzaW4oZGVsdGFQaGkpLFxuICAgICAgY29zRGVsdGFHYW1tYSA9IGNvcyhkZWx0YUdhbW1hKSxcbiAgICAgIHNpbkRlbHRhR2FtbWEgPSBzaW4oZGVsdGFHYW1tYSk7XG5cbiAgZnVuY3Rpb24gcm90YXRpb24obGFtYmRhLCBwaGkpIHtcbiAgICB2YXIgY29zUGhpID0gY29zKHBoaSksXG4gICAgICAgIHggPSBjb3MobGFtYmRhKSAqIGNvc1BoaSxcbiAgICAgICAgeSA9IHNpbihsYW1iZGEpICogY29zUGhpLFxuICAgICAgICB6ID0gc2luKHBoaSksXG4gICAgICAgIGsgPSB6ICogY29zRGVsdGFQaGkgKyB4ICogc2luRGVsdGFQaGk7XG4gICAgcmV0dXJuIFtcbiAgICAgIGF0YW4yKHkgKiBjb3NEZWx0YUdhbW1hIC0gayAqIHNpbkRlbHRhR2FtbWEsIHggKiBjb3NEZWx0YVBoaSAtIHogKiBzaW5EZWx0YVBoaSksXG4gICAgICBhc2luKGsgKiBjb3NEZWx0YUdhbW1hICsgeSAqIHNpbkRlbHRhR2FtbWEpXG4gICAgXTtcbiAgfVxuXG4gIHJvdGF0aW9uLmludmVydCA9IGZ1bmN0aW9uKGxhbWJkYSwgcGhpKSB7XG4gICAgdmFyIGNvc1BoaSA9IGNvcyhwaGkpLFxuICAgICAgICB4ID0gY29zKGxhbWJkYSkgKiBjb3NQaGksXG4gICAgICAgIHkgPSBzaW4obGFtYmRhKSAqIGNvc1BoaSxcbiAgICAgICAgeiA9IHNpbihwaGkpLFxuICAgICAgICBrID0geiAqIGNvc0RlbHRhR2FtbWEgLSB5ICogc2luRGVsdGFHYW1tYTtcbiAgICByZXR1cm4gW1xuICAgICAgYXRhbjIoeSAqIGNvc0RlbHRhR2FtbWEgKyB6ICogc2luRGVsdGFHYW1tYSwgeCAqIGNvc0RlbHRhUGhpICsgayAqIHNpbkRlbHRhUGhpKSxcbiAgICAgIGFzaW4oayAqIGNvc0RlbHRhUGhpIC0geCAqIHNpbkRlbHRhUGhpKVxuICAgIF07XG4gIH07XG5cbiAgcmV0dXJuIHJvdGF0aW9uO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihyb3RhdGUpIHtcbiAgcm90YXRlID0gcm90YXRlUmFkaWFucyhyb3RhdGVbMF0gKiByYWRpYW5zLCByb3RhdGVbMV0gKiByYWRpYW5zLCByb3RhdGUubGVuZ3RoID4gMiA/IHJvdGF0ZVsyXSAqIHJhZGlhbnMgOiAwKTtcblxuICBmdW5jdGlvbiBmb3J3YXJkKGNvb3JkaW5hdGVzKSB7XG4gICAgY29vcmRpbmF0ZXMgPSByb3RhdGUoY29vcmRpbmF0ZXNbMF0gKiByYWRpYW5zLCBjb29yZGluYXRlc1sxXSAqIHJhZGlhbnMpO1xuICAgIHJldHVybiBjb29yZGluYXRlc1swXSAqPSBkZWdyZWVzLCBjb29yZGluYXRlc1sxXSAqPSBkZWdyZWVzLCBjb29yZGluYXRlcztcbiAgfVxuXG4gIGZvcndhcmQuaW52ZXJ0ID0gZnVuY3Rpb24oY29vcmRpbmF0ZXMpIHtcbiAgICBjb29yZGluYXRlcyA9IHJvdGF0ZS5pbnZlcnQoY29vcmRpbmF0ZXNbMF0gKiByYWRpYW5zLCBjb29yZGluYXRlc1sxXSAqIHJhZGlhbnMpO1xuICAgIHJldHVybiBjb29yZGluYXRlc1swXSAqPSBkZWdyZWVzLCBjb29yZGluYXRlc1sxXSAqPSBkZWdyZWVzLCBjb29yZGluYXRlcztcbiAgfTtcblxuICByZXR1cm4gZm9yd2FyZDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/rotation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/stream.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-geo/src/stream.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction streamGeometry(geometry, stream) {\n  if (geometry && streamGeometryType.hasOwnProperty(geometry.type)) {\n    streamGeometryType[geometry.type](geometry, stream);\n  }\n}\n\nvar streamObjectType = {\n  Feature: function(object, stream) {\n    streamGeometry(object.geometry, stream);\n  },\n  FeatureCollection: function(object, stream) {\n    var features = object.features, i = -1, n = features.length;\n    while (++i < n) streamGeometry(features[i].geometry, stream);\n  }\n};\n\nvar streamGeometryType = {\n  Sphere: function(object, stream) {\n    stream.sphere();\n  },\n  Point: function(object, stream) {\n    object = object.coordinates;\n    stream.point(object[0], object[1], object[2]);\n  },\n  MultiPoint: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) object = coordinates[i], stream.point(object[0], object[1], object[2]);\n  },\n  LineString: function(object, stream) {\n    streamLine(object.coordinates, stream, 0);\n  },\n  MultiLineString: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) streamLine(coordinates[i], stream, 0);\n  },\n  Polygon: function(object, stream) {\n    streamPolygon(object.coordinates, stream);\n  },\n  MultiPolygon: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) streamPolygon(coordinates[i], stream);\n  },\n  GeometryCollection: function(object, stream) {\n    var geometries = object.geometries, i = -1, n = geometries.length;\n    while (++i < n) streamGeometry(geometries[i], stream);\n  }\n};\n\nfunction streamLine(coordinates, stream, closed) {\n  var i = -1, n = coordinates.length - closed, coordinate;\n  stream.lineStart();\n  while (++i < n) coordinate = coordinates[i], stream.point(coordinate[0], coordinate[1], coordinate[2]);\n  stream.lineEnd();\n}\n\nfunction streamPolygon(coordinates, stream) {\n  var i = -1, n = coordinates.length;\n  stream.polygonStart();\n  while (++i < n) streamLine(coordinates[i], stream, 1);\n  stream.polygonEnd();\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(object, stream) {\n  if (object && streamObjectType.hasOwnProperty(object.type)) {\n    streamObjectType[object.type](object, stream);\n  } else {\n    streamGeometry(object, stream);\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/transform.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-geo/src/transform.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   transformer: () => (/* binding */ transformer)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(methods) {\n  return {\n    stream: transformer(methods)\n  };\n}\n\nfunction transformer(methods) {\n  return function(stream) {\n    var s = new TransformStream;\n    for (var key in methods) s[key] = methods[key];\n    s.stream = stream;\n    return s;\n  };\n}\n\nfunction TransformStream() {}\n\nTransformStream.prototype = {\n  constructor: TransformStream,\n  point: function(x, y) { this.stream.point(x, y); },\n  sphere: function() { this.stream.sphere(); },\n  lineStart: function() { this.stream.lineStart(); },\n  lineEnd: function() { this.stream.lineEnd(); },\n  polygonStart: function() { this.stream.polygonStart(); },\n  polygonEnd: function() { this.stream.polygonEnd(); }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy90cmFuc2Zvcm0uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSw2QkFBZSxvQ0FBUztBQUN4QjtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQSwwQkFBMEIsMEJBQTBCO0FBQ3BELHVCQUF1Qix1QkFBdUI7QUFDOUMsMEJBQTBCLDBCQUEwQjtBQUNwRCx3QkFBd0Isd0JBQXdCO0FBQ2hELDZCQUE2Qiw2QkFBNkI7QUFDMUQsMkJBQTJCO0FBQzNCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGQzLWdlb1xcc3JjXFx0cmFuc2Zvcm0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24obWV0aG9kcykge1xuICByZXR1cm4ge1xuICAgIHN0cmVhbTogdHJhbnNmb3JtZXIobWV0aG9kcylcbiAgfTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHRyYW5zZm9ybWVyKG1ldGhvZHMpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uKHN0cmVhbSkge1xuICAgIHZhciBzID0gbmV3IFRyYW5zZm9ybVN0cmVhbTtcbiAgICBmb3IgKHZhciBrZXkgaW4gbWV0aG9kcykgc1trZXldID0gbWV0aG9kc1trZXldO1xuICAgIHMuc3RyZWFtID0gc3RyZWFtO1xuICAgIHJldHVybiBzO1xuICB9O1xufVxuXG5mdW5jdGlvbiBUcmFuc2Zvcm1TdHJlYW0oKSB7fVxuXG5UcmFuc2Zvcm1TdHJlYW0ucHJvdG90eXBlID0ge1xuICBjb25zdHJ1Y3RvcjogVHJhbnNmb3JtU3RyZWFtLFxuICBwb2ludDogZnVuY3Rpb24oeCwgeSkgeyB0aGlzLnN0cmVhbS5wb2ludCh4LCB5KTsgfSxcbiAgc3BoZXJlOiBmdW5jdGlvbigpIHsgdGhpcy5zdHJlYW0uc3BoZXJlKCk7IH0sXG4gIGxpbmVTdGFydDogZnVuY3Rpb24oKSB7IHRoaXMuc3RyZWFtLmxpbmVTdGFydCgpOyB9LFxuICBsaW5lRW5kOiBmdW5jdGlvbigpIHsgdGhpcy5zdHJlYW0ubGluZUVuZCgpOyB9LFxuICBwb2x5Z29uU3RhcnQ6IGZ1bmN0aW9uKCkgeyB0aGlzLnN0cmVhbS5wb2x5Z29uU3RhcnQoKTsgfSxcbiAgcG9seWdvbkVuZDogZnVuY3Rpb24oKSB7IHRoaXMuc3RyZWFtLnBvbHlnb25FbmQoKTsgfVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/transform.js\n");

/***/ })

};
;