"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_chunks_mermaid_core_timeline-definition-BDJGKUSR_mjs"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/timeline-definition-BDJGKUSR.mjs":
/*!****************************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/timeline-definition-BDJGKUSR.mjs ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: () => (/* binding */ diagram)\n/* harmony export */ });\n/* harmony import */ var _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-YTJNT7DU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-YTJNT7DU.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n/* harmony import */ var khroma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! khroma */ \"(app-pages-browser)/./node_modules/khroma/dist/methods/is_dark.js\");\n/* harmony import */ var khroma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! khroma */ \"(app-pages-browser)/./node_modules/khroma/dist/methods/lighten.js\");\n/* harmony import */ var khroma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! khroma */ \"(app-pages-browser)/./node_modules/khroma/dist/methods/darken.js\");\n\n\n// src/diagrams/timeline/parser/timeline.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [6, 8, 10, 11, 12, 14, 16, 17, 20, 21], $V1 = [1, 9], $V2 = [1, 10], $V3 = [1, 11], $V4 = [1, 12], $V5 = [1, 13], $V6 = [1, 16], $V7 = [1, 17];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"timeline\": 4, \"document\": 5, \"EOF\": 6, \"line\": 7, \"SPACE\": 8, \"statement\": 9, \"NEWLINE\": 10, \"title\": 11, \"acc_title\": 12, \"acc_title_value\": 13, \"acc_descr\": 14, \"acc_descr_value\": 15, \"acc_descr_multiline_value\": 16, \"section\": 17, \"period_statement\": 18, \"event_statement\": 19, \"period\": 20, \"event\": 21, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"timeline\", 6: \"EOF\", 8: \"SPACE\", 10: \"NEWLINE\", 11: \"title\", 12: \"acc_title\", 13: \"acc_title_value\", 14: \"acc_descr\", 15: \"acc_descr_value\", 16: \"acc_descr_multiline_value\", 17: \"section\", 20: \"period\", 21: \"event\" },\n    productions_: [0, [3, 3], [5, 0], [5, 2], [7, 2], [7, 1], [7, 1], [7, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 1], [9, 1], [9, 1], [18, 1], [19, 1]],\n    performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 1:\n          return $$[$0 - 1];\n          break;\n        case 2:\n          this.$ = [];\n          break;\n        case 3:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 4:\n        case 5:\n          this.$ = $$[$0];\n          break;\n        case 6:\n        case 7:\n          this.$ = [];\n          break;\n        case 8:\n          yy.getCommonDb().setDiagramTitle($$[$0].substr(6));\n          this.$ = $$[$0].substr(6);\n          break;\n        case 9:\n          this.$ = $$[$0].trim();\n          yy.getCommonDb().setAccTitle(this.$);\n          break;\n        case 10:\n        case 11:\n          this.$ = $$[$0].trim();\n          yy.getCommonDb().setAccDescription(this.$);\n          break;\n        case 12:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 15:\n          yy.addTask($$[$0], 0, \"\");\n          this.$ = $$[$0];\n          break;\n        case 16:\n          yy.addEvent($$[$0].substr(2));\n          this.$ = $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, o($V0, [2, 2], { 5: 3 }), { 6: [1, 4], 7: 5, 8: [1, 6], 9: 7, 10: [1, 8], 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: 14, 19: 15, 20: $V6, 21: $V7 }, o($V0, [2, 7], { 1: [2, 1] }), o($V0, [2, 3]), { 9: 18, 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: 14, 19: 15, 20: $V6, 21: $V7 }, o($V0, [2, 5]), o($V0, [2, 6]), o($V0, [2, 8]), { 13: [1, 19] }, { 15: [1, 20] }, o($V0, [2, 11]), o($V0, [2, 12]), o($V0, [2, 13]), o($V0, [2, 14]), o($V0, [2, 15]), o($V0, [2, 16]), o($V0, [2, 4]), o($V0, [2, 9]), o($V0, [2, 10])],\n    defaultActions: {},\n    parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            return 10;\n            break;\n          case 3:\n            break;\n          case 4:\n            break;\n          case 5:\n            return 4;\n            break;\n          case 6:\n            return 11;\n            break;\n          case 7:\n            this.begin(\"acc_title\");\n            return 12;\n            break;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 9:\n            this.begin(\"acc_descr\");\n            return 14;\n            break;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 11:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 14:\n            return 17;\n            break;\n          case 15:\n            return 21;\n            break;\n          case 16:\n            return 20;\n            break;\n          case 17:\n            return 6;\n            break;\n          case 18:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:#[^\\n]*)/i, /^(?:timeline\\b)/i, /^(?:title\\s[^\\n]+)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:section\\s[^:\\n]+)/i, /^(?::\\s[^:\\n]+)/i, /^(?:[^#:\\n]+)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [12, 13], \"inclusive\": false }, \"acc_descr\": { \"rules\": [10], \"inclusive\": false }, \"acc_title\": { \"rules\": [8], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 18], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar timeline_default = parser;\n\n// src/diagrams/timeline/timelineDb.js\nvar timelineDb_exports = {};\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__export)(timelineDb_exports, {\n  addEvent: () => addEvent,\n  addSection: () => addSection,\n  addTask: () => addTask,\n  addTaskOrg: () => addTaskOrg,\n  clear: () => clear2,\n  default: () => timelineDb_default,\n  getCommonDb: () => getCommonDb,\n  getSections: () => getSections,\n  getTasks: () => getTasks\n});\nvar currentSection = \"\";\nvar currentTaskId = 0;\nvar sections = [];\nvar tasks = [];\nvar rawTasks = [];\nvar getCommonDb = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.commonDb_exports, \"getCommonDb\");\nvar clear2 = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n  sections.length = 0;\n  tasks.length = 0;\n  currentSection = \"\";\n  rawTasks.length = 0;\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.clear)();\n}, \"clear\");\nvar addSection = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(txt) {\n  currentSection = txt;\n  sections.push(txt);\n}, \"addSection\");\nvar getSections = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n  return sections;\n}, \"getSections\");\nvar getTasks = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 100;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n  tasks.push(...rawTasks);\n  return tasks;\n}, \"getTasks\");\nvar addTask = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(period, length, event) {\n  const rawTask = {\n    id: currentTaskId++,\n    section: currentSection,\n    type: currentSection,\n    task: period,\n    score: length ? length : 0,\n    //if event is defined, then add it the events array\n    events: event ? [event] : []\n  };\n  rawTasks.push(rawTask);\n}, \"addTask\");\nvar addEvent = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(event) {\n  const currentTask = rawTasks.find((task) => task.id === currentTaskId - 1);\n  currentTask.events.push(event);\n}, \"addEvent\");\nvar addTaskOrg = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(descr) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: []\n  };\n  tasks.push(newTask);\n}, \"addTaskOrg\");\nvar compileTasks = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n  const compileTask = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(pos) {\n    return rawTasks[pos].processed;\n  }, \"compileTask\");\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n}, \"compileTasks\");\nvar timelineDb_default = {\n  clear: clear2,\n  getCommonDb,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  addTaskOrg,\n  addEvent\n};\n\n// src/diagrams/timeline/timelineRenderer.ts\n\n\n// src/diagrams/timeline/svgDraw.js\n\nvar MAX_SECTIONS = 12;\nvar drawRect = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(elem, rectData) {\n  const rectElem = elem.append(\"rect\");\n  rectElem.attr(\"x\", rectData.x);\n  rectElem.attr(\"y\", rectData.y);\n  rectElem.attr(\"fill\", rectData.fill);\n  rectElem.attr(\"stroke\", rectData.stroke);\n  rectElem.attr(\"width\", rectData.width);\n  rectElem.attr(\"height\", rectData.height);\n  rectElem.attr(\"rx\", rectData.rx);\n  rectElem.attr(\"ry\", rectData.ry);\n  if (rectData.class !== void 0) {\n    rectElem.attr(\"class\", rectData.class);\n  }\n  return rectElem;\n}, \"drawRect\");\nvar drawFace = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(element, faceData) {\n  const radius = 15;\n  const circleElement = element.append(\"circle\").attr(\"cx\", faceData.cx).attr(\"cy\", faceData.cy).attr(\"class\", \"face\").attr(\"r\", radius).attr(\"stroke-width\", 2).attr(\"overflow\", \"visible\");\n  const face = element.append(\"g\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx - radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx + radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  function smile(face2) {\n    const arc = (0,d3__WEBPACK_IMPORTED_MODULE_1__.arc)().startAngle(Math.PI / 2).endAngle(3 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 2) + \")\");\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(smile, \"smile\");\n  function sad(face2) {\n    const arc = (0,d3__WEBPACK_IMPORTED_MODULE_1__.arc)().startAngle(3 * Math.PI / 2).endAngle(5 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 7) + \")\");\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(sad, \"sad\");\n  function ambivalent(face2) {\n    face2.append(\"line\").attr(\"class\", \"mouth\").attr(\"stroke\", 2).attr(\"x1\", faceData.cx - 5).attr(\"y1\", faceData.cy + 7).attr(\"x2\", faceData.cx + 5).attr(\"y2\", faceData.cy + 7).attr(\"class\", \"mouth\").attr(\"stroke-width\", \"1px\").attr(\"stroke\", \"#666\");\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(ambivalent, \"ambivalent\");\n  if (faceData.score > 3) {\n    smile(face);\n  } else if (faceData.score < 3) {\n    sad(face);\n  } else {\n    ambivalent(face);\n  }\n  return circleElement;\n}, \"drawFace\");\nvar drawCircle = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(element, circleData) {\n  const circleElement = element.append(\"circle\");\n  circleElement.attr(\"cx\", circleData.cx);\n  circleElement.attr(\"cy\", circleData.cy);\n  circleElement.attr(\"class\", \"actor-\" + circleData.pos);\n  circleElement.attr(\"fill\", circleData.fill);\n  circleElement.attr(\"stroke\", circleData.stroke);\n  circleElement.attr(\"r\", circleData.r);\n  if (circleElement.class !== void 0) {\n    circleElement.attr(\"class\", circleElement.class);\n  }\n  if (circleData.title !== void 0) {\n    circleElement.append(\"title\").text(circleData.title);\n  }\n  return circleElement;\n}, \"drawCircle\");\nvar drawText = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(elem, textData) {\n  const nText = textData.text.replace(/<br\\s*\\/?>/gi, \" \");\n  const textElem = elem.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  if (textData.class !== void 0) {\n    textElem.attr(\"class\", textData.class);\n  }\n  const span = textElem.append(\"tspan\");\n  span.attr(\"x\", textData.x + textData.textMargin * 2);\n  span.text(nText);\n  return textElem;\n}, \"drawText\");\nvar drawLabel = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(elem, txtObject) {\n  function genPoints(x, y, width, height, cut) {\n    return x + \",\" + y + \" \" + (x + width) + \",\" + y + \" \" + (x + width) + \",\" + (y + height - cut) + \" \" + (x + width - cut * 1.2) + \",\" + (y + height) + \" \" + x + \",\" + (y + height);\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(genPoints, \"genPoints\");\n  const polygon = elem.append(\"polygon\");\n  polygon.attr(\"points\", genPoints(txtObject.x, txtObject.y, 50, 20, 7));\n  polygon.attr(\"class\", \"labelBox\");\n  txtObject.y = txtObject.y + txtObject.labelMargin;\n  txtObject.x = txtObject.x + 0.5 * txtObject.labelMargin;\n  drawText(elem, txtObject);\n}, \"drawLabel\");\nvar drawSection = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(elem, section, conf) {\n  const g = elem.append(\"g\");\n  const rect = getNoteRect();\n  rect.x = section.x;\n  rect.y = section.y;\n  rect.fill = section.fill;\n  rect.width = conf.width;\n  rect.height = conf.height;\n  rect.class = \"journey-section section-type-\" + section.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n  _drawTextCandidateFunc(conf)(\n    section.text,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"journey-section section-type-\" + section.num },\n    conf,\n    section.colour\n  );\n}, \"drawSection\");\nvar taskCount = -1;\nvar drawTask = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(elem, task, conf) {\n  const center = task.x + conf.width / 2;\n  const g = elem.append(\"g\");\n  taskCount++;\n  const maxHeight = 300 + 5 * 30;\n  g.append(\"line\").attr(\"id\", \"task\" + taskCount).attr(\"x1\", center).attr(\"y1\", task.y).attr(\"x2\", center).attr(\"y2\", maxHeight).attr(\"class\", \"task-line\").attr(\"stroke-width\", \"1px\").attr(\"stroke-dasharray\", \"4 2\").attr(\"stroke\", \"#666\");\n  drawFace(g, {\n    cx: center,\n    cy: 300 + (5 - task.score) * 30,\n    score: task.score\n  });\n  const rect = getNoteRect();\n  rect.x = task.x;\n  rect.y = task.y;\n  rect.fill = task.fill;\n  rect.width = conf.width;\n  rect.height = conf.height;\n  rect.class = \"task task-type-\" + task.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n  _drawTextCandidateFunc(conf)(\n    task.task,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"task\" },\n    conf,\n    task.colour\n  );\n}, \"drawTask\");\nvar drawBackgroundRect = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(elem, bounds) {\n  const rectElem = drawRect(elem, {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    class: \"rect\"\n  });\n  rectElem.lower();\n}, \"drawBackgroundRect\");\nvar getTextObj = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n  return {\n    x: 0,\n    y: 0,\n    fill: void 0,\n    \"text-anchor\": \"start\",\n    width: 100,\n    height: 100,\n    textMargin: 0,\n    rx: 0,\n    ry: 0\n  };\n}, \"getTextObj\");\nvar getNoteRect = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n  return {\n    x: 0,\n    y: 0,\n    width: 100,\n    anchor: \"start\",\n    height: 100,\n    rx: 0,\n    ry: 0\n  };\n}, \"getNoteRect\");\nvar _drawTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs, colour) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"font-color\", colour).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf, colour) {\n    const { taskFontSize, taskFontFamily } = conf;\n    const lines = content.split(/<br\\s*\\/?>/gi);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * taskFontSize - taskFontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).attr(\"fill\", colour).style(\"text-anchor\", \"middle\").style(\"font-size\", taskFontSize).style(\"font-family\", taskFontFamily);\n      text.append(\"tspan\").attr(\"x\", x + width / 2).attr(\"dy\", dy).text(lines[i]);\n      text.attr(\"y\", y + height / 2).attr(\"dominant-baseline\", \"central\").attr(\"alignment-baseline\", \"central\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf) {\n    const body = g.append(\"switch\");\n    const f = body.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height).attr(\"position\", \"fixed\");\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").attr(\"class\", \"label\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, body, x, y, width, height, textAttrs, conf);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(byFo, \"byFo\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (key in fromTextAttrsDict) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf) {\n    return conf.textPlacement === \"fo\" ? byFo : conf.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar initGraphics = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(graphics) {\n  graphics.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 5).attr(\"refY\", 2).attr(\"markerWidth\", 6).attr(\"markerHeight\", 4).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0,0 V 4 L6,2 Z\");\n}, \"initGraphics\");\nfunction wrap(text, width) {\n  text.each(function() {\n    var text2 = (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(this), words = text2.text().split(/(\\s+|<br>)/).reverse(), word, line = [], lineHeight = 1.1, y = text2.attr(\"y\"), dy = parseFloat(text2.attr(\"dy\")), tspan = text2.text(null).append(\"tspan\").attr(\"x\", 0).attr(\"y\", y).attr(\"dy\", dy + \"em\");\n    for (let j = 0; j < words.length; j++) {\n      word = words[words.length - 1 - j];\n      line.push(word);\n      tspan.text(line.join(\" \").trim());\n      if (tspan.node().getComputedTextLength() > width || word === \"<br>\") {\n        line.pop();\n        tspan.text(line.join(\" \").trim());\n        if (word === \"<br>\") {\n          line = [\"\"];\n        } else {\n          line = [word];\n        }\n        tspan = text2.append(\"tspan\").attr(\"x\", 0).attr(\"y\", y).attr(\"dy\", lineHeight + \"em\").text(word);\n      }\n    }\n  });\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(wrap, \"wrap\");\nvar drawNode = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(elem, node, fullSection, conf) {\n  const section = fullSection % MAX_SECTIONS - 1;\n  const nodeElem = elem.append(\"g\");\n  node.section = section;\n  nodeElem.attr(\n    \"class\",\n    (node.class ? node.class + \" \" : \"\") + \"timeline-node \" + (\"section-\" + section)\n  );\n  const bkgElem = nodeElem.append(\"g\");\n  const textElem = nodeElem.append(\"g\");\n  const txt = textElem.append(\"text\").text(node.descr).attr(\"dy\", \"1em\").attr(\"alignment-baseline\", \"middle\").attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").call(wrap, node.width);\n  const bbox = txt.node().getBBox();\n  const fontSize = conf.fontSize?.replace ? conf.fontSize.replace(\"px\", \"\") : conf.fontSize;\n  node.height = bbox.height + fontSize * 1.1 * 0.5 + node.padding;\n  node.height = Math.max(node.height, node.maxHeight);\n  node.width = node.width + 2 * node.padding;\n  textElem.attr(\"transform\", \"translate(\" + node.width / 2 + \", \" + node.padding / 2 + \")\");\n  defaultBkg(bkgElem, node, section, conf);\n  return node;\n}, \"drawNode\");\nvar getVirtualNodeHeight = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(elem, node, conf) {\n  const textElem = elem.append(\"g\");\n  const txt = textElem.append(\"text\").text(node.descr).attr(\"dy\", \"1em\").attr(\"alignment-baseline\", \"middle\").attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").call(wrap, node.width);\n  const bbox = txt.node().getBBox();\n  const fontSize = conf.fontSize?.replace ? conf.fontSize.replace(\"px\", \"\") : conf.fontSize;\n  textElem.remove();\n  return bbox.height + fontSize * 1.1 * 0.5 + node.padding;\n}, \"getVirtualNodeHeight\");\nvar defaultBkg = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(elem, node, section) {\n  const rd = 5;\n  elem.append(\"path\").attr(\"id\", \"node-\" + node.id).attr(\"class\", \"node-bkg node-\" + node.type).attr(\n    \"d\",\n    `M0 ${node.height - rd} v${-node.height + 2 * rd} q0,-5 5,-5 h${node.width - 2 * rd} q5,0 5,5 v${node.height - rd} H0 Z`\n  );\n  elem.append(\"line\").attr(\"class\", \"node-line-\" + section).attr(\"x1\", 0).attr(\"y1\", node.height).attr(\"x2\", node.width).attr(\"y2\", node.height);\n}, \"defaultBkg\");\nvar svgDraw_default = {\n  drawRect,\n  drawCircle,\n  drawSection,\n  drawText,\n  drawLabel,\n  drawTask,\n  drawBackgroundRect,\n  getTextObj,\n  getNoteRect,\n  initGraphics,\n  drawNode,\n  getVirtualNodeHeight\n};\n\n// src/diagrams/timeline/timelineRenderer.ts\nvar draw = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(text, id, version, diagObj) {\n  const conf = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.getConfig2)();\n  const LEFT_MARGIN = conf.leftMargin ?? 50;\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(\"timeline\", diagObj.db);\n  const securityLevel = conf.securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(\"body\");\n  const svg = root.select(\"#\" + id);\n  svg.append(\"g\");\n  const tasks2 = diagObj.db.getTasks();\n  const title = diagObj.db.getCommonDb().getDiagramTitle();\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(\"task\", tasks2);\n  svgDraw_default.initGraphics(svg);\n  const sections2 = diagObj.db.getSections();\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(\"sections\", sections2);\n  let maxSectionHeight = 0;\n  let maxTaskHeight = 0;\n  let depthY = 0;\n  let sectionBeginY = 0;\n  let masterX = 50 + LEFT_MARGIN;\n  let masterY = 50;\n  sectionBeginY = 50;\n  let sectionNumber = 0;\n  let hasSections = true;\n  sections2.forEach(function(section) {\n    const sectionNode = {\n      number: sectionNumber,\n      descr: section,\n      section: sectionNumber,\n      width: 150,\n      padding: 20,\n      maxHeight: maxSectionHeight\n    };\n    const sectionHeight = svgDraw_default.getVirtualNodeHeight(svg, sectionNode, conf);\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(\"sectionHeight before draw\", sectionHeight);\n    maxSectionHeight = Math.max(maxSectionHeight, sectionHeight + 20);\n  });\n  let maxEventCount = 0;\n  let maxEventLineLength = 0;\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(\"tasks.length\", tasks2.length);\n  for (const [i, task] of tasks2.entries()) {\n    const taskNode = {\n      number: i,\n      descr: task,\n      section: task.section,\n      width: 150,\n      padding: 20,\n      maxHeight: maxTaskHeight\n    };\n    const taskHeight = svgDraw_default.getVirtualNodeHeight(svg, taskNode, conf);\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(\"taskHeight before draw\", taskHeight);\n    maxTaskHeight = Math.max(maxTaskHeight, taskHeight + 20);\n    maxEventCount = Math.max(maxEventCount, task.events.length);\n    let maxEventLineLengthTemp = 0;\n    for (const event of task.events) {\n      const eventNode = {\n        descr: event,\n        section: task.section,\n        number: task.section,\n        width: 150,\n        padding: 20,\n        maxHeight: 50\n      };\n      maxEventLineLengthTemp += svgDraw_default.getVirtualNodeHeight(svg, eventNode, conf);\n    }\n    maxEventLineLength = Math.max(maxEventLineLength, maxEventLineLengthTemp);\n  }\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(\"maxSectionHeight before draw\", maxSectionHeight);\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(\"maxTaskHeight before draw\", maxTaskHeight);\n  if (sections2 && sections2.length > 0) {\n    sections2.forEach((section) => {\n      const tasksForSection = tasks2.filter((task) => task.section === section);\n      const sectionNode = {\n        number: sectionNumber,\n        descr: section,\n        section: sectionNumber,\n        width: 200 * Math.max(tasksForSection.length, 1) - 50,\n        padding: 20,\n        maxHeight: maxSectionHeight\n      };\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(\"sectionNode\", sectionNode);\n      const sectionNodeWrapper = svg.append(\"g\");\n      const node = svgDraw_default.drawNode(sectionNodeWrapper, sectionNode, sectionNumber, conf);\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(\"sectionNode output\", node);\n      sectionNodeWrapper.attr(\"transform\", `translate(${masterX}, ${sectionBeginY})`);\n      masterY += maxSectionHeight + 50;\n      if (tasksForSection.length > 0) {\n        drawTasks(\n          svg,\n          tasksForSection,\n          sectionNumber,\n          masterX,\n          masterY,\n          maxTaskHeight,\n          conf,\n          maxEventCount,\n          maxEventLineLength,\n          maxSectionHeight,\n          false\n        );\n      }\n      masterX += 200 * Math.max(tasksForSection.length, 1);\n      masterY = sectionBeginY;\n      sectionNumber++;\n    });\n  } else {\n    hasSections = false;\n    drawTasks(\n      svg,\n      tasks2,\n      sectionNumber,\n      masterX,\n      masterY,\n      maxTaskHeight,\n      conf,\n      maxEventCount,\n      maxEventLineLength,\n      maxSectionHeight,\n      true\n    );\n  }\n  const box = svg.node().getBBox();\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(\"bounds\", box);\n  if (title) {\n    svg.append(\"text\").text(title).attr(\"x\", box.width / 2 - LEFT_MARGIN).attr(\"font-size\", \"4ex\").attr(\"font-weight\", \"bold\").attr(\"y\", 20);\n  }\n  depthY = hasSections ? maxSectionHeight + maxTaskHeight + 150 : maxTaskHeight + 100;\n  const lineWrapper = svg.append(\"g\").attr(\"class\", \"lineWrapper\");\n  lineWrapper.append(\"line\").attr(\"x1\", LEFT_MARGIN).attr(\"y1\", depthY).attr(\"x2\", box.width + 3 * LEFT_MARGIN).attr(\"y2\", depthY).attr(\"stroke-width\", 4).attr(\"stroke\", \"black\").attr(\"marker-end\", \"url(#arrowhead)\");\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.setupGraphViewbox)(\n    void 0,\n    svg,\n    conf.timeline?.padding ?? 50,\n    conf.timeline?.useMaxWidth ?? false\n  );\n}, \"draw\");\nvar drawTasks = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(diagram2, tasks2, sectionColor, masterX, masterY, maxTaskHeight, conf, maxEventCount, maxEventLineLength, maxSectionHeight, isWithoutSections) {\n  for (const task of tasks2) {\n    const taskNode = {\n      descr: task.task,\n      section: sectionColor,\n      number: sectionColor,\n      width: 150,\n      padding: 20,\n      maxHeight: maxTaskHeight\n    };\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(\"taskNode\", taskNode);\n    const taskWrapper = diagram2.append(\"g\").attr(\"class\", \"taskWrapper\");\n    const node = svgDraw_default.drawNode(taskWrapper, taskNode, sectionColor, conf);\n    const taskHeight = node.height;\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(\"taskHeight after draw\", taskHeight);\n    taskWrapper.attr(\"transform\", `translate(${masterX}, ${masterY})`);\n    maxTaskHeight = Math.max(maxTaskHeight, taskHeight);\n    if (task.events) {\n      const lineWrapper = diagram2.append(\"g\").attr(\"class\", \"lineWrapper\");\n      let lineLength = maxTaskHeight;\n      masterY += 100;\n      lineLength = lineLength + drawEvents(diagram2, task.events, sectionColor, masterX, masterY, conf);\n      masterY -= 100;\n      lineWrapper.append(\"line\").attr(\"x1\", masterX + 190 / 2).attr(\"y1\", masterY + maxTaskHeight).attr(\"x2\", masterX + 190 / 2).attr(\n        \"y2\",\n        masterY + maxTaskHeight + (isWithoutSections ? maxTaskHeight : maxSectionHeight) + maxEventLineLength + 120\n      ).attr(\"stroke-width\", 2).attr(\"stroke\", \"black\").attr(\"marker-end\", \"url(#arrowhead)\").attr(\"stroke-dasharray\", \"5,5\");\n    }\n    masterX = masterX + 200;\n    if (isWithoutSections && !conf.timeline?.disableMulticolor) {\n      sectionColor++;\n    }\n  }\n  masterY = masterY - 10;\n}, \"drawTasks\");\nvar drawEvents = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(diagram2, events, sectionColor, masterX, masterY, conf) {\n  let maxEventHeight = 0;\n  const eventBeginY = masterY;\n  masterY = masterY + 100;\n  for (const event of events) {\n    const eventNode = {\n      descr: event,\n      section: sectionColor,\n      number: sectionColor,\n      width: 150,\n      padding: 20,\n      maxHeight: 50\n    };\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(\"eventNode\", eventNode);\n    const eventWrapper = diagram2.append(\"g\").attr(\"class\", \"eventWrapper\");\n    const node = svgDraw_default.drawNode(eventWrapper, eventNode, sectionColor, conf);\n    const eventHeight = node.height;\n    maxEventHeight = maxEventHeight + eventHeight;\n    eventWrapper.attr(\"transform\", `translate(${masterX}, ${masterY})`);\n    masterY = masterY + 10 + eventHeight;\n  }\n  masterY = eventBeginY;\n  return maxEventHeight;\n}, \"drawEvents\");\nvar timelineRenderer_default = {\n  setConf: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => {\n  }, \"setConf\"),\n  draw\n};\n\n// src/diagrams/timeline/styles.js\n\nvar genSections = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((options) => {\n  let sections2 = \"\";\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    options[\"lineColor\" + i] = options[\"lineColor\" + i] || options[\"cScaleInv\" + i];\n    if ((0,khroma__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(options[\"lineColor\" + i])) {\n      options[\"lineColor\" + i] = (0,khroma__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options[\"lineColor\" + i], 20);\n    } else {\n      options[\"lineColor\" + i] = (0,khroma__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(options[\"lineColor\" + i], 20);\n    }\n  }\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    const sw = \"\" + (17 - 3 * i);\n    sections2 += `\n    .section-${i - 1} rect, .section-${i - 1} path, .section-${i - 1} circle, .section-${i - 1} path  {\n      fill: ${options[\"cScale\" + i]};\n    }\n    .section-${i - 1} text {\n     fill: ${options[\"cScaleLabel\" + i]};\n    }\n    .node-icon-${i - 1} {\n      font-size: 40px;\n      color: ${options[\"cScaleLabel\" + i]};\n    }\n    .section-edge-${i - 1}{\n      stroke: ${options[\"cScale\" + i]};\n    }\n    .edge-depth-${i - 1}{\n      stroke-width: ${sw};\n    }\n    .section-${i - 1} line {\n      stroke: ${options[\"cScaleInv\" + i]} ;\n      stroke-width: 3;\n    }\n\n    .lineWrapper line{\n      stroke: ${options[\"cScaleLabel\" + i]} ;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n    `;\n  }\n  return sections2;\n}, \"genSections\");\nvar getStyles = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((options) => `\n  .edge {\n    stroke-width: 3;\n  }\n  ${genSections(options)}\n  .section-root rect, .section-root path, .section-root circle  {\n    fill: ${options.git0};\n  }\n  .section-root text {\n    fill: ${options.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .eventWrapper  {\n   filter: brightness(120%);\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/timeline/timeline-definition.ts\nvar diagram = {\n  db: timelineDb_exports,\n  renderer: timelineRenderer_default,\n  parser: timeline_default,\n  styles: styles_default\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/timeline-definition-BDJGKUSR.mjs\n"));

/***/ })

}]);