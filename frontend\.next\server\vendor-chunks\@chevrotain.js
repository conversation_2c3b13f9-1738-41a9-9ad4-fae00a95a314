"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@chevrotain";
exports.ids = ["vendor-chunks/@chevrotain"];
exports.modules = {

/***/ "(ssr)/./node_modules/@chevrotain/cst-dts-gen/lib/src/api.js":
/*!*************************************************************!*\
  !*** ./node_modules/@chevrotain/cst-dts-gen/lib/src/api.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateCstDts: () => (/* binding */ generateCstDts)\n/* harmony export */ });\n/* harmony import */ var _model_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./model.js */ \"(ssr)/./node_modules/@chevrotain/cst-dts-gen/lib/src/model.js\");\n/* harmony import */ var _generate_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./generate.js */ \"(ssr)/./node_modules/@chevrotain/cst-dts-gen/lib/src/generate.js\");\n\n\nconst defaultOptions = {\n    includeVisitorInterface: true,\n    visitorInterfaceName: \"ICstNodeVisitor\",\n};\nfunction generateCstDts(productions, options) {\n    const effectiveOptions = Object.assign(Object.assign({}, defaultOptions), options);\n    const model = (0,_model_js__WEBPACK_IMPORTED_MODULE_0__.buildModel)(productions);\n    return (0,_generate_js__WEBPACK_IMPORTED_MODULE_1__.genDts)(model, effectiveOptions);\n}\n//# sourceMappingURL=api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vY3N0LWR0cy1nZW4vbGliL3NyYy9hcGkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXdDO0FBQ0Q7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLDJEQUEyRDtBQUMzRCxrQkFBa0IscURBQVU7QUFDNUIsV0FBVyxvREFBTTtBQUNqQjtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEBjaGV2cm90YWluXFxjc3QtZHRzLWdlblxcbGliXFxzcmNcXGFwaS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBidWlsZE1vZGVsIH0gZnJvbSBcIi4vbW9kZWwuanNcIjtcbmltcG9ydCB7IGdlbkR0cyB9IGZyb20gXCIuL2dlbmVyYXRlLmpzXCI7XG5jb25zdCBkZWZhdWx0T3B0aW9ucyA9IHtcbiAgICBpbmNsdWRlVmlzaXRvckludGVyZmFjZTogdHJ1ZSxcbiAgICB2aXNpdG9ySW50ZXJmYWNlTmFtZTogXCJJQ3N0Tm9kZVZpc2l0b3JcIixcbn07XG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVDc3REdHMocHJvZHVjdGlvbnMsIG9wdGlvbnMpIHtcbiAgICBjb25zdCBlZmZlY3RpdmVPcHRpb25zID0gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCBkZWZhdWx0T3B0aW9ucyksIG9wdGlvbnMpO1xuICAgIGNvbnN0IG1vZGVsID0gYnVpbGRNb2RlbChwcm9kdWN0aW9ucyk7XG4gICAgcmV0dXJuIGdlbkR0cyhtb2RlbCwgZWZmZWN0aXZlT3B0aW9ucyk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcGkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/cst-dts-gen/lib/src/api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/cst-dts-gen/lib/src/generate.js":
/*!******************************************************************!*\
  !*** ./node_modules/@chevrotain/cst-dts-gen/lib/src/generate.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genDts: () => (/* binding */ genDts)\n/* harmony export */ });\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/flatten.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/map.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/isArray.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/uniq.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/reduce.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/upperFirst.js\");\n\nfunction genDts(model, options) {\n    let contentParts = [];\n    contentParts = contentParts.concat(`import type { CstNode, ICstVisitor, IToken } from \"chevrotain\";`);\n    contentParts = contentParts.concat((0,lodash_es__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,lodash_es__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(model, (node) => genCstNodeTypes(node))));\n    if (options.includeVisitorInterface) {\n        contentParts = contentParts.concat(genVisitor(options.visitorInterfaceName, model));\n    }\n    return contentParts.join(\"\\n\\n\") + \"\\n\";\n}\nfunction genCstNodeTypes(node) {\n    const nodeCstInterface = genNodeInterface(node);\n    const nodeChildrenInterface = genNodeChildrenType(node);\n    return [nodeCstInterface, nodeChildrenInterface];\n}\nfunction genNodeInterface(node) {\n    const nodeInterfaceName = getNodeInterfaceName(node.name);\n    const childrenTypeName = getNodeChildrenTypeName(node.name);\n    return `export interface ${nodeInterfaceName} extends CstNode {\n  name: \"${node.name}\";\n  children: ${childrenTypeName};\n}`;\n}\nfunction genNodeChildrenType(node) {\n    const typeName = getNodeChildrenTypeName(node.name);\n    return `export type ${typeName} = {\n  ${(0,lodash_es__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node.properties, (property) => genChildProperty(property)).join(\"\\n  \")}\n};`;\n}\nfunction genChildProperty(prop) {\n    const typeName = buildTypeString(prop.type);\n    return `${prop.name}${prop.optional ? \"?\" : \"\"}: ${typeName}[];`;\n}\nfunction genVisitor(name, nodes) {\n    return `export interface ${name}<IN, OUT> extends ICstVisitor<IN, OUT> {\n  ${(0,lodash_es__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(nodes, (node) => genVisitorFunction(node)).join(\"\\n  \")}\n}`;\n}\nfunction genVisitorFunction(node) {\n    const childrenTypeName = getNodeChildrenTypeName(node.name);\n    return `${node.name}(children: ${childrenTypeName}, param?: IN): OUT;`;\n}\nfunction buildTypeString(type) {\n    if ((0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(type)) {\n        const typeNames = (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])((0,lodash_es__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(type, (t) => getTypeString(t)));\n        const typeString = (0,lodash_es__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(typeNames, (sum, t) => sum + \" | \" + t);\n        return \"(\" + typeString + \")\";\n    }\n    else {\n        return getTypeString(type);\n    }\n}\nfunction getTypeString(type) {\n    if (type.kind === \"token\") {\n        return \"IToken\";\n    }\n    return getNodeInterfaceName(type.name);\n}\nfunction getNodeInterfaceName(ruleName) {\n    return (0,lodash_es__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(ruleName) + \"CstNode\";\n}\nfunction getNodeChildrenTypeName(ruleName) {\n    return (0,lodash_es__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(ruleName) + \"CstChildren\";\n}\n//# sourceMappingURL=generate.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/cst-dts-gen/lib/src/generate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/cst-dts-gen/lib/src/model.js":
/*!***************************************************************!*\
  !*** ./node_modules/@chevrotain/cst-dts-gen/lib/src/model.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildModel: () => (/* binding */ buildModel)\n/* harmony export */ });\n/* harmony import */ var _chevrotain_gast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @chevrotain/gast */ \"(ssr)/./node_modules/@chevrotain/gast/lib/src/api.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/values.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/map.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/groupBy.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/some.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/assign.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/flatten.js\");\n\n\nfunction buildModel(productions) {\n    const generator = new CstNodeDefinitionGenerator();\n    const allRules = (0,lodash_es__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(productions);\n    return (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(allRules, (rule) => generator.visitRule(rule));\n}\nclass CstNodeDefinitionGenerator extends _chevrotain_gast__WEBPACK_IMPORTED_MODULE_0__.GAstVisitor {\n    visitRule(node) {\n        const rawElements = this.visitEach(node.definition);\n        const grouped = (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(rawElements, (el) => el.propertyName);\n        const properties = (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(grouped, (group, propertyName) => {\n            const allNullable = !(0,lodash_es__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(group, (el) => !el.canBeNull);\n            // In an alternation with a label a property name can have\n            // multiple types.\n            let propertyType = group[0].type;\n            if (group.length > 1) {\n                propertyType = (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(group, (g) => g.type);\n            }\n            return {\n                name: propertyName,\n                type: propertyType,\n                optional: allNullable,\n            };\n        });\n        return {\n            name: node.name,\n            properties: properties,\n        };\n    }\n    visitAlternative(node) {\n        return this.visitEachAndOverrideWith(node.definition, { canBeNull: true });\n    }\n    visitOption(node) {\n        return this.visitEachAndOverrideWith(node.definition, { canBeNull: true });\n    }\n    visitRepetition(node) {\n        return this.visitEachAndOverrideWith(node.definition, { canBeNull: true });\n    }\n    visitRepetitionMandatory(node) {\n        return this.visitEach(node.definition);\n    }\n    visitRepetitionMandatoryWithSeparator(node) {\n        return this.visitEach(node.definition).concat({\n            propertyName: node.separator.name,\n            canBeNull: true,\n            type: getType(node.separator),\n        });\n    }\n    visitRepetitionWithSeparator(node) {\n        return this.visitEachAndOverrideWith(node.definition, {\n            canBeNull: true,\n        }).concat({\n            propertyName: node.separator.name,\n            canBeNull: true,\n            type: getType(node.separator),\n        });\n    }\n    visitAlternation(node) {\n        return this.visitEachAndOverrideWith(node.definition, { canBeNull: true });\n    }\n    visitTerminal(node) {\n        return [\n            {\n                propertyName: node.label || node.terminalType.name,\n                canBeNull: false,\n                type: getType(node),\n            },\n        ];\n    }\n    visitNonTerminal(node) {\n        return [\n            {\n                propertyName: node.label || node.nonTerminalName,\n                canBeNull: false,\n                type: getType(node),\n            },\n        ];\n    }\n    visitEachAndOverrideWith(definition, override) {\n        return (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this.visitEach(definition), (definition) => (0,lodash_es__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({}, definition, override));\n    }\n    visitEach(definition) {\n        return (0,lodash_es__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(definition, (definition) => this.visit(definition)));\n    }\n}\nfunction getType(production) {\n    if (production instanceof _chevrotain_gast__WEBPACK_IMPORTED_MODULE_0__.NonTerminal) {\n        return {\n            kind: \"rule\",\n            name: production.referencedRule.name,\n        };\n    }\n    return { kind: \"token\" };\n}\n//# sourceMappingURL=model.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/cst-dts-gen/lib/src/model.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/gast/lib/src/api.js":
/*!******************************************************!*\
  !*** ./node_modules/@chevrotain/gast/lib/src/api.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alternation: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.Alternation),\n/* harmony export */   Alternative: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.Alternative),\n/* harmony export */   GAstVisitor: () => (/* reexport safe */ _visitor_js__WEBPACK_IMPORTED_MODULE_1__.GAstVisitor),\n/* harmony export */   NonTerminal: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.NonTerminal),\n/* harmony export */   Option: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.Option),\n/* harmony export */   Repetition: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.Repetition),\n/* harmony export */   RepetitionMandatory: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatory),\n/* harmony export */   RepetitionMandatoryWithSeparator: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatoryWithSeparator),\n/* harmony export */   RepetitionWithSeparator: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionWithSeparator),\n/* harmony export */   Rule: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.Rule),\n/* harmony export */   Terminal: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.Terminal),\n/* harmony export */   getProductionDslName: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_2__.getProductionDslName),\n/* harmony export */   isBranchingProd: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_2__.isBranchingProd),\n/* harmony export */   isOptionalProd: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_2__.isOptionalProd),\n/* harmony export */   isSequenceProd: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_2__.isSequenceProd),\n/* harmony export */   serializeGrammar: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.serializeGrammar),\n/* harmony export */   serializeProduction: () => (/* reexport safe */ _model_js__WEBPACK_IMPORTED_MODULE_0__.serializeProduction)\n/* harmony export */ });\n/* harmony import */ var _model_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./model.js */ \"(ssr)/./node_modules/@chevrotain/gast/lib/src/model.js\");\n/* harmony import */ var _visitor_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./visitor.js */ \"(ssr)/./node_modules/@chevrotain/gast/lib/src/visitor.js\");\n/* harmony import */ var _helpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./helpers.js */ \"(ssr)/./node_modules/@chevrotain/gast/lib/src/helpers.js\");\n\n\n\n//# sourceMappingURL=api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vZ2FzdC9saWIvc3JjL2FwaS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUErTjtBQUNwTDtBQUMyRDtBQUN0RyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAY2hldnJvdGFpblxcZ2FzdFxcbGliXFxzcmNcXGFwaS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBSdWxlLCBUZXJtaW5hbCwgTm9uVGVybWluYWwsIE9wdGlvbiwgUmVwZXRpdGlvbiwgUmVwZXRpdGlvbk1hbmRhdG9yeSwgUmVwZXRpdGlvbk1hbmRhdG9yeVdpdGhTZXBhcmF0b3IsIFJlcGV0aXRpb25XaXRoU2VwYXJhdG9yLCBBbHRlcm5hdGlvbiwgQWx0ZXJuYXRpdmUsIHNlcmlhbGl6ZUdyYW1tYXIsIHNlcmlhbGl6ZVByb2R1Y3Rpb24sIH0gZnJvbSBcIi4vbW9kZWwuanNcIjtcbmV4cG9ydCB7IEdBc3RWaXNpdG9yIH0gZnJvbSBcIi4vdmlzaXRvci5qc1wiO1xuZXhwb3J0IHsgZ2V0UHJvZHVjdGlvbkRzbE5hbWUsIGlzT3B0aW9uYWxQcm9kLCBpc0JyYW5jaGluZ1Byb2QsIGlzU2VxdWVuY2VQcm9kLCB9IGZyb20gXCIuL2hlbHBlcnMuanNcIjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/gast/lib/src/api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/gast/lib/src/helpers.js":
/*!**********************************************************!*\
  !*** ./node_modules/@chevrotain/gast/lib/src/helpers.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getProductionDslName: () => (/* binding */ getProductionDslName),\n/* harmony export */   isBranchingProd: () => (/* binding */ isBranchingProd),\n/* harmony export */   isOptionalProd: () => (/* binding */ isOptionalProd),\n/* harmony export */   isSequenceProd: () => (/* binding */ isSequenceProd)\n/* harmony export */ });\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/some.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/includes.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/every.js\");\n/* harmony import */ var _model_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./model.js */ \"(ssr)/./node_modules/@chevrotain/gast/lib/src/model.js\");\n\n\nfunction isSequenceProd(prod) {\n    return (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Alternative ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Option ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Repetition ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatory ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatoryWithSeparator ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionWithSeparator ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Terminal ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Rule);\n}\nfunction isOptionalProd(prod, alreadyVisited = []) {\n    const isDirectlyOptional = prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Option ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Repetition ||\n        prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionWithSeparator;\n    if (isDirectlyOptional) {\n        return true;\n    }\n    // note that this can cause infinite loop if one optional empty TOP production has a cyclic dependency with another\n    // empty optional top rule\n    // may be indirectly optional ((A?B?C?) | (D?E?F?))\n    if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Alternation) {\n        // for OR its enough for just one of the alternatives to be optional\n        return (0,lodash_es__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(prod.definition, (subProd) => {\n            return isOptionalProd(subProd, alreadyVisited);\n        });\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.NonTerminal && (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(alreadyVisited, prod)) {\n        // avoiding stack overflow due to infinite recursion\n        return false;\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.AbstractProduction) {\n        if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.NonTerminal) {\n            alreadyVisited.push(prod);\n        }\n        return (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(prod.definition, (subProd) => {\n            return isOptionalProd(subProd, alreadyVisited);\n        });\n    }\n    else {\n        return false;\n    }\n}\nfunction isBranchingProd(prod) {\n    return prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Alternation;\n}\nfunction getProductionDslName(prod) {\n    /* istanbul ignore else */\n    if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.NonTerminal) {\n        return \"SUBRULE\";\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Option) {\n        return \"OPTION\";\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Alternation) {\n        return \"OR\";\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatory) {\n        return \"AT_LEAST_ONE\";\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatoryWithSeparator) {\n        return \"AT_LEAST_ONE_SEP\";\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionWithSeparator) {\n        return \"MANY_SEP\";\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Repetition) {\n        return \"MANY\";\n    }\n    else if (prod instanceof _model_js__WEBPACK_IMPORTED_MODULE_0__.Terminal) {\n        return \"CONSUME\";\n        /* c8 ignore next 3 */\n    }\n    else {\n        throw Error(\"non exhaustive match\");\n    }\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/gast/lib/src/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/gast/lib/src/model.js":
/*!********************************************************!*\
  !*** ./node_modules/@chevrotain/gast/lib/src/model.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbstractProduction: () => (/* binding */ AbstractProduction),\n/* harmony export */   Alternation: () => (/* binding */ Alternation),\n/* harmony export */   Alternative: () => (/* binding */ Alternative),\n/* harmony export */   NonTerminal: () => (/* binding */ NonTerminal),\n/* harmony export */   Option: () => (/* binding */ Option),\n/* harmony export */   Repetition: () => (/* binding */ Repetition),\n/* harmony export */   RepetitionMandatory: () => (/* binding */ RepetitionMandatory),\n/* harmony export */   RepetitionMandatoryWithSeparator: () => (/* binding */ RepetitionMandatoryWithSeparator),\n/* harmony export */   RepetitionWithSeparator: () => (/* binding */ RepetitionWithSeparator),\n/* harmony export */   Rule: () => (/* binding */ Rule),\n/* harmony export */   Terminal: () => (/* binding */ Terminal),\n/* harmony export */   serializeGrammar: () => (/* binding */ serializeGrammar),\n/* harmony export */   serializeProduction: () => (/* binding */ serializeProduction)\n/* harmony export */ });\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/isString.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/forEach.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/assign.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/pickBy.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/map.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/lodash-es/isRegExp.js\");\n\n// TODO: duplicated code to avoid extracting another sub-package -- how to avoid?\nfunction tokenLabel(tokType) {\n    if (hasTokenLabel(tokType)) {\n        return tokType.LABEL;\n    }\n    else {\n        return tokType.name;\n    }\n}\n// TODO: duplicated code to avoid extracting another sub-package -- how to avoid?\nfunction hasTokenLabel(obj) {\n    return (0,lodash_es__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(obj.LABEL) && obj.LABEL !== \"\";\n}\nclass AbstractProduction {\n    get definition() {\n        return this._definition;\n    }\n    set definition(value) {\n        this._definition = value;\n    }\n    constructor(_definition) {\n        this._definition = _definition;\n    }\n    accept(visitor) {\n        visitor.visit(this);\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this.definition, (prod) => {\n            prod.accept(visitor);\n        });\n    }\n}\nclass NonTerminal extends AbstractProduction {\n    constructor(options) {\n        super([]);\n        this.idx = 1;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n    set definition(definition) {\n        // immutable\n    }\n    get definition() {\n        if (this.referencedRule !== undefined) {\n            return this.referencedRule.definition;\n        }\n        return [];\n    }\n    accept(visitor) {\n        visitor.visit(this);\n        // don't visit children of a reference, we will get cyclic infinite loops if we do so\n    }\n}\nclass Rule extends AbstractProduction {\n    constructor(options) {\n        super(options.definition);\n        this.orgText = \"\";\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n}\nclass Alternative extends AbstractProduction {\n    constructor(options) {\n        super(options.definition);\n        this.ignoreAmbiguities = false;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n}\nclass Option extends AbstractProduction {\n    constructor(options) {\n        super(options.definition);\n        this.idx = 1;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n}\nclass RepetitionMandatory extends AbstractProduction {\n    constructor(options) {\n        super(options.definition);\n        this.idx = 1;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n}\nclass RepetitionMandatoryWithSeparator extends AbstractProduction {\n    constructor(options) {\n        super(options.definition);\n        this.idx = 1;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n}\nclass Repetition extends AbstractProduction {\n    constructor(options) {\n        super(options.definition);\n        this.idx = 1;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n}\nclass RepetitionWithSeparator extends AbstractProduction {\n    constructor(options) {\n        super(options.definition);\n        this.idx = 1;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n}\nclass Alternation extends AbstractProduction {\n    get definition() {\n        return this._definition;\n    }\n    set definition(value) {\n        this._definition = value;\n    }\n    constructor(options) {\n        super(options.definition);\n        this.idx = 1;\n        this.ignoreAmbiguities = false;\n        this.hasPredicates = false;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n}\nclass Terminal {\n    constructor(options) {\n        this.idx = 1;\n        (0,lodash_es__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, (0,lodash_es__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, (v) => v !== undefined));\n    }\n    accept(visitor) {\n        visitor.visit(this);\n    }\n}\nfunction serializeGrammar(topRules) {\n    return (0,lodash_es__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(topRules, serializeProduction);\n}\nfunction serializeProduction(node) {\n    function convertDefinition(definition) {\n        return (0,lodash_es__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(definition, serializeProduction);\n    }\n    /* istanbul ignore else */\n    if (node instanceof NonTerminal) {\n        const serializedNonTerminal = {\n            type: \"NonTerminal\",\n            name: node.nonTerminalName,\n            idx: node.idx,\n        };\n        if ((0,lodash_es__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node.label)) {\n            serializedNonTerminal.label = node.label;\n        }\n        return serializedNonTerminal;\n    }\n    else if (node instanceof Alternative) {\n        return {\n            type: \"Alternative\",\n            definition: convertDefinition(node.definition),\n        };\n    }\n    else if (node instanceof Option) {\n        return {\n            type: \"Option\",\n            idx: node.idx,\n            definition: convertDefinition(node.definition),\n        };\n    }\n    else if (node instanceof RepetitionMandatory) {\n        return {\n            type: \"RepetitionMandatory\",\n            idx: node.idx,\n            definition: convertDefinition(node.definition),\n        };\n    }\n    else if (node instanceof RepetitionMandatoryWithSeparator) {\n        return {\n            type: \"RepetitionMandatoryWithSeparator\",\n            idx: node.idx,\n            separator: (serializeProduction(new Terminal({ terminalType: node.separator }))),\n            definition: convertDefinition(node.definition),\n        };\n    }\n    else if (node instanceof RepetitionWithSeparator) {\n        return {\n            type: \"RepetitionWithSeparator\",\n            idx: node.idx,\n            separator: (serializeProduction(new Terminal({ terminalType: node.separator }))),\n            definition: convertDefinition(node.definition),\n        };\n    }\n    else if (node instanceof Repetition) {\n        return {\n            type: \"Repetition\",\n            idx: node.idx,\n            definition: convertDefinition(node.definition),\n        };\n    }\n    else if (node instanceof Alternation) {\n        return {\n            type: \"Alternation\",\n            idx: node.idx,\n            definition: convertDefinition(node.definition),\n        };\n    }\n    else if (node instanceof Terminal) {\n        const serializedTerminal = {\n            type: \"Terminal\",\n            name: node.terminalType.name,\n            label: tokenLabel(node.terminalType),\n            idx: node.idx,\n        };\n        if ((0,lodash_es__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node.label)) {\n            serializedTerminal.terminalLabel = node.label;\n        }\n        const pattern = node.terminalType.PATTERN;\n        if (node.terminalType.PATTERN) {\n            serializedTerminal.pattern = (0,lodash_es__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(pattern)\n                ? pattern.source\n                : pattern;\n        }\n        return serializedTerminal;\n    }\n    else if (node instanceof Rule) {\n        return {\n            type: \"Rule\",\n            name: node.name,\n            orgText: node.orgText,\n            definition: convertDefinition(node.definition),\n        };\n        /* c8 ignore next 3 */\n    }\n    else {\n        throw Error(\"non exhaustive match\");\n    }\n}\n//# sourceMappingURL=model.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/gast/lib/src/model.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/gast/lib/src/visitor.js":
/*!**********************************************************!*\
  !*** ./node_modules/@chevrotain/gast/lib/src/visitor.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GAstVisitor: () => (/* binding */ GAstVisitor)\n/* harmony export */ });\n/* harmony import */ var _model_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./model.js */ \"(ssr)/./node_modules/@chevrotain/gast/lib/src/model.js\");\n\nclass GAstVisitor {\n    visit(node) {\n        const nodeAny = node;\n        switch (nodeAny.constructor) {\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.NonTerminal:\n                return this.visitNonTerminal(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.Alternative:\n                return this.visitAlternative(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.Option:\n                return this.visitOption(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatory:\n                return this.visitRepetitionMandatory(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatoryWithSeparator:\n                return this.visitRepetitionMandatoryWithSeparator(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.RepetitionWithSeparator:\n                return this.visitRepetitionWithSeparator(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.Repetition:\n                return this.visitRepetition(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.Alternation:\n                return this.visitAlternation(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.Terminal:\n                return this.visitTerminal(nodeAny);\n            case _model_js__WEBPACK_IMPORTED_MODULE_0__.Rule:\n                return this.visitRule(nodeAny);\n            /* c8 ignore next 2 */\n            default:\n                throw Error(\"non exhaustive match\");\n        }\n    }\n    /* c8 ignore next */\n    visitNonTerminal(node) { }\n    /* c8 ignore next */\n    visitAlternative(node) { }\n    /* c8 ignore next */\n    visitOption(node) { }\n    /* c8 ignore next */\n    visitRepetition(node) { }\n    /* c8 ignore next */\n    visitRepetitionMandatory(node) { }\n    /* c8 ignore next 3 */\n    visitRepetitionMandatoryWithSeparator(node) { }\n    /* c8 ignore next */\n    visitRepetitionWithSeparator(node) { }\n    /* c8 ignore next */\n    visitAlternation(node) { }\n    /* c8 ignore next */\n    visitTerminal(node) { }\n    /* c8 ignore next */\n    visitRule(node) { }\n}\n//# sourceMappingURL=visitor.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/gast/lib/src/visitor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/api.js":
/*!***************************************************************!*\
  !*** ./node_modules/@chevrotain/regexp-to-ast/lib/src/api.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseRegExpVisitor: () => (/* reexport safe */ _base_regexp_visitor_js__WEBPACK_IMPORTED_MODULE_1__.BaseRegExpVisitor),\n/* harmony export */   RegExpParser: () => (/* reexport safe */ _regexp_parser_js__WEBPACK_IMPORTED_MODULE_0__.RegExpParser)\n/* harmony export */ });\n/* harmony import */ var _regexp_parser_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regexp-parser.js */ \"(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/regexp-parser.js\");\n/* harmony import */ var _base_regexp_visitor_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base-regexp-visitor.js */ \"(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/base-regexp-visitor.js\");\n\n\n//# sourceMappingURL=api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vcmVnZXhwLXRvLWFzdC9saWIvc3JjL2FwaS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWtEO0FBQ1c7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQGNoZXZyb3RhaW5cXHJlZ2V4cC10by1hc3RcXGxpYlxcc3JjXFxhcGkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgUmVnRXhwUGFyc2VyIH0gZnJvbSBcIi4vcmVnZXhwLXBhcnNlci5qc1wiO1xuZXhwb3J0IHsgQmFzZVJlZ0V4cFZpc2l0b3IgfSBmcm9tIFwiLi9iYXNlLXJlZ2V4cC12aXNpdG9yLmpzXCI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcGkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/base-regexp-visitor.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@chevrotain/regexp-to-ast/lib/src/base-regexp-visitor.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseRegExpVisitor: () => (/* binding */ BaseRegExpVisitor)\n/* harmony export */ });\nclass BaseRegExpVisitor {\n    visitChildren(node) {\n        for (const key in node) {\n            const child = node[key];\n            /* istanbul ignore else */\n            if (node.hasOwnProperty(key)) {\n                if (child.type !== undefined) {\n                    this.visit(child);\n                }\n                else if (Array.isArray(child)) {\n                    child.forEach((subChild) => {\n                        this.visit(subChild);\n                    }, this);\n                }\n            }\n        }\n    }\n    visit(node) {\n        switch (node.type) {\n            case \"Pattern\":\n                this.visitPattern(node);\n                break;\n            case \"Flags\":\n                this.visitFlags(node);\n                break;\n            case \"Disjunction\":\n                this.visitDisjunction(node);\n                break;\n            case \"Alternative\":\n                this.visitAlternative(node);\n                break;\n            case \"StartAnchor\":\n                this.visitStartAnchor(node);\n                break;\n            case \"EndAnchor\":\n                this.visitEndAnchor(node);\n                break;\n            case \"WordBoundary\":\n                this.visitWordBoundary(node);\n                break;\n            case \"NonWordBoundary\":\n                this.visitNonWordBoundary(node);\n                break;\n            case \"Lookahead\":\n                this.visitLookahead(node);\n                break;\n            case \"NegativeLookahead\":\n                this.visitNegativeLookahead(node);\n                break;\n            case \"Character\":\n                this.visitCharacter(node);\n                break;\n            case \"Set\":\n                this.visitSet(node);\n                break;\n            case \"Group\":\n                this.visitGroup(node);\n                break;\n            case \"GroupBackReference\":\n                this.visitGroupBackReference(node);\n                break;\n            case \"Quantifier\":\n                this.visitQuantifier(node);\n                break;\n        }\n        this.visitChildren(node);\n    }\n    visitPattern(node) { }\n    visitFlags(node) { }\n    visitDisjunction(node) { }\n    visitAlternative(node) { }\n    // Assertion\n    visitStartAnchor(node) { }\n    visitEndAnchor(node) { }\n    visitWordBoundary(node) { }\n    visitNonWordBoundary(node) { }\n    visitLookahead(node) { }\n    visitNegativeLookahead(node) { }\n    // atoms\n    visitCharacter(node) { }\n    visitSet(node) { }\n    visitGroup(node) { }\n    visitGroupBackReference(node) { }\n    visitQuantifier(node) { }\n}\n//# sourceMappingURL=base-regexp-visitor.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/base-regexp-visitor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/character-classes.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@chevrotain/regexp-to-ast/lib/src/character-classes.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   digitsCharCodes: () => (/* binding */ digitsCharCodes),\n/* harmony export */   whitespaceCodes: () => (/* binding */ whitespaceCodes),\n/* harmony export */   wordCharCodes: () => (/* binding */ wordCharCodes)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/utils.js\");\n\nconst digitsCharCodes = [];\nfor (let i = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"0\"); i <= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"9\"); i++) {\n    digitsCharCodes.push(i);\n}\nconst wordCharCodes = [(0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"_\")].concat(digitsCharCodes);\nfor (let i = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"a\"); i <= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"z\"); i++) {\n    wordCharCodes.push(i);\n}\nfor (let i = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"A\"); i <= (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"Z\"); i++) {\n    wordCharCodes.push(i);\n}\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp#character-classes\nconst whitespaceCodes = [\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\" \"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\f\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\n\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\r\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\t\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\v\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\t\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u00a0\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u1680\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2000\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2001\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2002\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2003\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2004\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2005\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2006\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2007\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2008\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2009\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u200a\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2028\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2029\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u202f\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u205f\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u3000\"),\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\ufeff\"),\n];\n//# sourceMappingURL=character-classes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/character-classes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/regexp-parser.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@chevrotain/regexp-to-ast/lib/src/regexp-parser.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RegExpParser: () => (/* binding */ RegExpParser)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/utils.js\");\n/* harmony import */ var _character_classes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./character-classes.js */ \"(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/character-classes.js\");\n\n\n// consts and utilities\nconst hexDigitPattern = /[0-9a-fA-F]/;\nconst decimalPattern = /[0-9]/;\nconst decimalPatternNoZero = /[1-9]/;\n// https://hackernoon.com/the-madness-of-parsing-real-world-javascript-regexps-d9ee336df983\n// https://www.ecma-international.org/ecma-262/8.0/index.html#prod-Pattern\nclass RegExpParser {\n    constructor() {\n        this.idx = 0;\n        this.input = \"\";\n        this.groupIdx = 0;\n    }\n    saveState() {\n        return {\n            idx: this.idx,\n            input: this.input,\n            groupIdx: this.groupIdx,\n        };\n    }\n    restoreState(newState) {\n        this.idx = newState.idx;\n        this.input = newState.input;\n        this.groupIdx = newState.groupIdx;\n    }\n    pattern(input) {\n        // parser state\n        this.idx = 0;\n        this.input = input;\n        this.groupIdx = 0;\n        this.consumeChar(\"/\");\n        const value = this.disjunction();\n        this.consumeChar(\"/\");\n        const flags = {\n            type: \"Flags\",\n            loc: { begin: this.idx, end: input.length },\n            global: false,\n            ignoreCase: false,\n            multiLine: false,\n            unicode: false,\n            sticky: false,\n        };\n        while (this.isRegExpFlag()) {\n            switch (this.popChar()) {\n                case \"g\":\n                    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.addFlag)(flags, \"global\");\n                    break;\n                case \"i\":\n                    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.addFlag)(flags, \"ignoreCase\");\n                    break;\n                case \"m\":\n                    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.addFlag)(flags, \"multiLine\");\n                    break;\n                case \"u\":\n                    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.addFlag)(flags, \"unicode\");\n                    break;\n                case \"y\":\n                    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.addFlag)(flags, \"sticky\");\n                    break;\n            }\n        }\n        if (this.idx !== this.input.length) {\n            throw Error(\"Redundant input: \" + this.input.substring(this.idx));\n        }\n        return {\n            type: \"Pattern\",\n            flags: flags,\n            value: value,\n            loc: this.loc(0),\n        };\n    }\n    disjunction() {\n        const alts = [];\n        const begin = this.idx;\n        alts.push(this.alternative());\n        while (this.peekChar() === \"|\") {\n            this.consumeChar(\"|\");\n            alts.push(this.alternative());\n        }\n        return { type: \"Disjunction\", value: alts, loc: this.loc(begin) };\n    }\n    alternative() {\n        const terms = [];\n        const begin = this.idx;\n        while (this.isTerm()) {\n            terms.push(this.term());\n        }\n        return { type: \"Alternative\", value: terms, loc: this.loc(begin) };\n    }\n    term() {\n        if (this.isAssertion()) {\n            return this.assertion();\n        }\n        else {\n            return this.atom();\n        }\n    }\n    assertion() {\n        const begin = this.idx;\n        switch (this.popChar()) {\n            case \"^\":\n                return {\n                    type: \"StartAnchor\",\n                    loc: this.loc(begin),\n                };\n            case \"$\":\n                return { type: \"EndAnchor\", loc: this.loc(begin) };\n            // '\\b' or '\\B'\n            case \"\\\\\":\n                switch (this.popChar()) {\n                    case \"b\":\n                        return {\n                            type: \"WordBoundary\",\n                            loc: this.loc(begin),\n                        };\n                    case \"B\":\n                        return {\n                            type: \"NonWordBoundary\",\n                            loc: this.loc(begin),\n                        };\n                }\n                // istanbul ignore next\n                throw Error(\"Invalid Assertion Escape\");\n            // '(?=' or '(?!'\n            case \"(\":\n                this.consumeChar(\"?\");\n                let type;\n                switch (this.popChar()) {\n                    case \"=\":\n                        type = \"Lookahead\";\n                        break;\n                    case \"!\":\n                        type = \"NegativeLookahead\";\n                        break;\n                }\n                (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_EXISTS)(type);\n                const disjunction = this.disjunction();\n                this.consumeChar(\")\");\n                return {\n                    type: type,\n                    value: disjunction,\n                    loc: this.loc(begin),\n                };\n        }\n        // istanbul ignore next\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_NEVER_REACH_HERE)();\n    }\n    quantifier(isBacktracking = false) {\n        let range = undefined;\n        const begin = this.idx;\n        switch (this.popChar()) {\n            case \"*\":\n                range = {\n                    atLeast: 0,\n                    atMost: Infinity,\n                };\n                break;\n            case \"+\":\n                range = {\n                    atLeast: 1,\n                    atMost: Infinity,\n                };\n                break;\n            case \"?\":\n                range = {\n                    atLeast: 0,\n                    atMost: 1,\n                };\n                break;\n            case \"{\":\n                const atLeast = this.integerIncludingZero();\n                switch (this.popChar()) {\n                    case \"}\":\n                        range = {\n                            atLeast: atLeast,\n                            atMost: atLeast,\n                        };\n                        break;\n                    case \",\":\n                        let atMost;\n                        if (this.isDigit()) {\n                            atMost = this.integerIncludingZero();\n                            range = {\n                                atLeast: atLeast,\n                                atMost: atMost,\n                            };\n                        }\n                        else {\n                            range = {\n                                atLeast: atLeast,\n                                atMost: Infinity,\n                            };\n                        }\n                        this.consumeChar(\"}\");\n                        break;\n                }\n                // throwing exceptions from \"ASSERT_EXISTS\" during backtracking\n                // causes severe performance degradations\n                if (isBacktracking === true && range === undefined) {\n                    return undefined;\n                }\n                (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_EXISTS)(range);\n                break;\n        }\n        // throwing exceptions from \"ASSERT_EXISTS\" during backtracking\n        // causes severe performance degradations\n        if (isBacktracking === true && range === undefined) {\n            return undefined;\n        }\n        // istanbul ignore else\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_EXISTS)(range)) {\n            if (this.peekChar(0) === \"?\") {\n                this.consumeChar(\"?\");\n                range.greedy = false;\n            }\n            else {\n                range.greedy = true;\n            }\n            range.type = \"Quantifier\";\n            range.loc = this.loc(begin);\n            return range;\n        }\n    }\n    atom() {\n        let atom;\n        const begin = this.idx;\n        switch (this.peekChar()) {\n            case \".\":\n                atom = this.dotAll();\n                break;\n            case \"\\\\\":\n                atom = this.atomEscape();\n                break;\n            case \"[\":\n                atom = this.characterClass();\n                break;\n            case \"(\":\n                atom = this.group();\n                break;\n        }\n        if (atom === undefined && this.isPatternCharacter()) {\n            atom = this.patternCharacter();\n        }\n        // istanbul ignore else\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_EXISTS)(atom)) {\n            atom.loc = this.loc(begin);\n            if (this.isQuantifier()) {\n                atom.quantifier = this.quantifier();\n            }\n            return atom;\n        }\n        // istanbul ignore next\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_NEVER_REACH_HERE)();\n    }\n    dotAll() {\n        this.consumeChar(\".\");\n        return {\n            type: \"Set\",\n            complement: true,\n            value: [(0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\n\"), (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\r\"), (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2028\"), (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u2029\")],\n        };\n    }\n    atomEscape() {\n        this.consumeChar(\"\\\\\");\n        switch (this.peekChar()) {\n            case \"1\":\n            case \"2\":\n            case \"3\":\n            case \"4\":\n            case \"5\":\n            case \"6\":\n            case \"7\":\n            case \"8\":\n            case \"9\":\n                return this.decimalEscapeAtom();\n            case \"d\":\n            case \"D\":\n            case \"s\":\n            case \"S\":\n            case \"w\":\n            case \"W\":\n                return this.characterClassEscape();\n            case \"f\":\n            case \"n\":\n            case \"r\":\n            case \"t\":\n            case \"v\":\n                return this.controlEscapeAtom();\n            case \"c\":\n                return this.controlLetterEscapeAtom();\n            case \"0\":\n                return this.nulCharacterAtom();\n            case \"x\":\n                return this.hexEscapeSequenceAtom();\n            case \"u\":\n                return this.regExpUnicodeEscapeSequenceAtom();\n            default:\n                return this.identityEscapeAtom();\n        }\n    }\n    decimalEscapeAtom() {\n        const value = this.positiveInteger();\n        return { type: \"GroupBackReference\", value: value };\n    }\n    characterClassEscape() {\n        let set;\n        let complement = false;\n        switch (this.popChar()) {\n            case \"d\":\n                set = _character_classes_js__WEBPACK_IMPORTED_MODULE_1__.digitsCharCodes;\n                break;\n            case \"D\":\n                set = _character_classes_js__WEBPACK_IMPORTED_MODULE_1__.digitsCharCodes;\n                complement = true;\n                break;\n            case \"s\":\n                set = _character_classes_js__WEBPACK_IMPORTED_MODULE_1__.whitespaceCodes;\n                break;\n            case \"S\":\n                set = _character_classes_js__WEBPACK_IMPORTED_MODULE_1__.whitespaceCodes;\n                complement = true;\n                break;\n            case \"w\":\n                set = _character_classes_js__WEBPACK_IMPORTED_MODULE_1__.wordCharCodes;\n                break;\n            case \"W\":\n                set = _character_classes_js__WEBPACK_IMPORTED_MODULE_1__.wordCharCodes;\n                complement = true;\n                break;\n        }\n        // istanbul ignore else\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_EXISTS)(set)) {\n            return { type: \"Set\", value: set, complement: complement };\n        }\n        // istanbul ignore next\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_NEVER_REACH_HERE)();\n    }\n    controlEscapeAtom() {\n        let escapeCode;\n        switch (this.popChar()) {\n            case \"f\":\n                escapeCode = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\f\");\n                break;\n            case \"n\":\n                escapeCode = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\n\");\n                break;\n            case \"r\":\n                escapeCode = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\r\");\n                break;\n            case \"t\":\n                escapeCode = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\t\");\n                break;\n            case \"v\":\n                escapeCode = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\v\");\n                break;\n        }\n        // istanbul ignore else\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_EXISTS)(escapeCode)) {\n            return { type: \"Character\", value: escapeCode };\n        }\n        // istanbul ignore next\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ASSERT_NEVER_REACH_HERE)();\n    }\n    controlLetterEscapeAtom() {\n        this.consumeChar(\"c\");\n        const letter = this.popChar();\n        if (/[a-zA-Z]/.test(letter) === false) {\n            throw Error(\"Invalid \");\n        }\n        const letterCode = letter.toUpperCase().charCodeAt(0) - 64;\n        return { type: \"Character\", value: letterCode };\n    }\n    nulCharacterAtom() {\n        // TODO implement '[lookahead ∉ DecimalDigit]'\n        // TODO: for the deprecated octal escape sequence\n        this.consumeChar(\"0\");\n        return { type: \"Character\", value: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\0\") };\n    }\n    hexEscapeSequenceAtom() {\n        this.consumeChar(\"x\");\n        return this.parseHexDigits(2);\n    }\n    regExpUnicodeEscapeSequenceAtom() {\n        this.consumeChar(\"u\");\n        return this.parseHexDigits(4);\n    }\n    identityEscapeAtom() {\n        // TODO: implement \"SourceCharacter but not UnicodeIDContinue\"\n        // // http://unicode.org/reports/tr31/#Specific_Character_Adjustments\n        const escapedChar = this.popChar();\n        return { type: \"Character\", value: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(escapedChar) };\n    }\n    classPatternCharacterAtom() {\n        switch (this.peekChar()) {\n            // istanbul ignore next\n            case \"\\n\":\n            // istanbul ignore next\n            case \"\\r\":\n            // istanbul ignore next\n            case \"\\u2028\":\n            // istanbul ignore next\n            case \"\\u2029\":\n            // istanbul ignore next\n            case \"\\\\\":\n            // istanbul ignore next\n            case \"]\":\n                throw Error(\"TBD\");\n            default:\n                const nextChar = this.popChar();\n                return { type: \"Character\", value: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(nextChar) };\n        }\n    }\n    characterClass() {\n        const set = [];\n        let complement = false;\n        this.consumeChar(\"[\");\n        if (this.peekChar(0) === \"^\") {\n            this.consumeChar(\"^\");\n            complement = true;\n        }\n        while (this.isClassAtom()) {\n            const from = this.classAtom();\n            const isFromSingleChar = from.type === \"Character\";\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isCharacter)(from) && this.isRangeDash()) {\n                this.consumeChar(\"-\");\n                const to = this.classAtom();\n                const isToSingleChar = to.type === \"Character\";\n                // a range can only be used when both sides are single characters\n                if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isCharacter)(to)) {\n                    if (to.value < from.value) {\n                        throw Error(\"Range out of order in character class\");\n                    }\n                    set.push({ from: from.value, to: to.value });\n                }\n                else {\n                    // literal dash\n                    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.insertToSet)(from.value, set);\n                    set.push((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"-\"));\n                    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.insertToSet)(to.value, set);\n                }\n            }\n            else {\n                (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.insertToSet)(from.value, set);\n            }\n        }\n        this.consumeChar(\"]\");\n        return { type: \"Set\", complement: complement, value: set };\n    }\n    classAtom() {\n        switch (this.peekChar()) {\n            // istanbul ignore next\n            case \"]\":\n            // istanbul ignore next\n            case \"\\n\":\n            // istanbul ignore next\n            case \"\\r\":\n            // istanbul ignore next\n            case \"\\u2028\":\n            // istanbul ignore next\n            case \"\\u2029\":\n                throw Error(\"TBD\");\n            case \"\\\\\":\n                return this.classEscape();\n            default:\n                return this.classPatternCharacterAtom();\n        }\n    }\n    classEscape() {\n        this.consumeChar(\"\\\\\");\n        switch (this.peekChar()) {\n            // Matches a backspace.\n            // (Not to be confused with \\b word boundary outside characterClass)\n            case \"b\":\n                this.consumeChar(\"b\");\n                return { type: \"Character\", value: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(\"\\u0008\") };\n            case \"d\":\n            case \"D\":\n            case \"s\":\n            case \"S\":\n            case \"w\":\n            case \"W\":\n                return this.characterClassEscape();\n            case \"f\":\n            case \"n\":\n            case \"r\":\n            case \"t\":\n            case \"v\":\n                return this.controlEscapeAtom();\n            case \"c\":\n                return this.controlLetterEscapeAtom();\n            case \"0\":\n                return this.nulCharacterAtom();\n            case \"x\":\n                return this.hexEscapeSequenceAtom();\n            case \"u\":\n                return this.regExpUnicodeEscapeSequenceAtom();\n            default:\n                return this.identityEscapeAtom();\n        }\n    }\n    group() {\n        let capturing = true;\n        this.consumeChar(\"(\");\n        switch (this.peekChar(0)) {\n            case \"?\":\n                this.consumeChar(\"?\");\n                this.consumeChar(\":\");\n                capturing = false;\n                break;\n            default:\n                this.groupIdx++;\n                break;\n        }\n        const value = this.disjunction();\n        this.consumeChar(\")\");\n        const groupAst = {\n            type: \"Group\",\n            capturing: capturing,\n            value: value,\n        };\n        if (capturing) {\n            groupAst[\"idx\"] = this.groupIdx;\n        }\n        return groupAst;\n    }\n    positiveInteger() {\n        let number = this.popChar();\n        // istanbul ignore next - can't ever get here due to previous lookahead checks\n        // still implementing this error checking in case this ever changes.\n        if (decimalPatternNoZero.test(number) === false) {\n            throw Error(\"Expecting a positive integer\");\n        }\n        while (decimalPattern.test(this.peekChar(0))) {\n            number += this.popChar();\n        }\n        return parseInt(number, 10);\n    }\n    integerIncludingZero() {\n        let number = this.popChar();\n        if (decimalPattern.test(number) === false) {\n            throw Error(\"Expecting an integer\");\n        }\n        while (decimalPattern.test(this.peekChar(0))) {\n            number += this.popChar();\n        }\n        return parseInt(number, 10);\n    }\n    patternCharacter() {\n        const nextChar = this.popChar();\n        switch (nextChar) {\n            // istanbul ignore next\n            case \"\\n\":\n            // istanbul ignore next\n            case \"\\r\":\n            // istanbul ignore next\n            case \"\\u2028\":\n            // istanbul ignore next\n            case \"\\u2029\":\n            // istanbul ignore next\n            case \"^\":\n            // istanbul ignore next\n            case \"$\":\n            // istanbul ignore next\n            case \"\\\\\":\n            // istanbul ignore next\n            case \".\":\n            // istanbul ignore next\n            case \"*\":\n            // istanbul ignore next\n            case \"+\":\n            // istanbul ignore next\n            case \"?\":\n            // istanbul ignore next\n            case \"(\":\n            // istanbul ignore next\n            case \")\":\n            // istanbul ignore next\n            case \"[\":\n            // istanbul ignore next\n            case \"|\":\n                // istanbul ignore next\n                throw Error(\"TBD\");\n            default:\n                return { type: \"Character\", value: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.cc)(nextChar) };\n        }\n    }\n    isRegExpFlag() {\n        switch (this.peekChar(0)) {\n            case \"g\":\n            case \"i\":\n            case \"m\":\n            case \"u\":\n            case \"y\":\n                return true;\n            default:\n                return false;\n        }\n    }\n    isRangeDash() {\n        return this.peekChar() === \"-\" && this.isClassAtom(1);\n    }\n    isDigit() {\n        return decimalPattern.test(this.peekChar(0));\n    }\n    isClassAtom(howMuch = 0) {\n        switch (this.peekChar(howMuch)) {\n            case \"]\":\n            case \"\\n\":\n            case \"\\r\":\n            case \"\\u2028\":\n            case \"\\u2029\":\n                return false;\n            default:\n                return true;\n        }\n    }\n    isTerm() {\n        return this.isAtom() || this.isAssertion();\n    }\n    isAtom() {\n        if (this.isPatternCharacter()) {\n            return true;\n        }\n        switch (this.peekChar(0)) {\n            case \".\":\n            case \"\\\\\": // atomEscape\n            case \"[\": // characterClass\n            // TODO: isAtom must be called before isAssertion - disambiguate\n            case \"(\": // group\n                return true;\n            default:\n                return false;\n        }\n    }\n    isAssertion() {\n        switch (this.peekChar(0)) {\n            case \"^\":\n            case \"$\":\n                return true;\n            // '\\b' or '\\B'\n            case \"\\\\\":\n                switch (this.peekChar(1)) {\n                    case \"b\":\n                    case \"B\":\n                        return true;\n                    default:\n                        return false;\n                }\n            // '(?=' or '(?!'\n            case \"(\":\n                return (this.peekChar(1) === \"?\" &&\n                    (this.peekChar(2) === \"=\" || this.peekChar(2) === \"!\"));\n            default:\n                return false;\n        }\n    }\n    isQuantifier() {\n        const prevState = this.saveState();\n        try {\n            return this.quantifier(true) !== undefined;\n        }\n        catch (e) {\n            return false;\n        }\n        finally {\n            this.restoreState(prevState);\n        }\n    }\n    isPatternCharacter() {\n        switch (this.peekChar()) {\n            case \"^\":\n            case \"$\":\n            case \"\\\\\":\n            case \".\":\n            case \"*\":\n            case \"+\":\n            case \"?\":\n            case \"(\":\n            case \")\":\n            case \"[\":\n            case \"|\":\n            case \"/\":\n            case \"\\n\":\n            case \"\\r\":\n            case \"\\u2028\":\n            case \"\\u2029\":\n                return false;\n            default:\n                return true;\n        }\n    }\n    parseHexDigits(howMany) {\n        let hexString = \"\";\n        for (let i = 0; i < howMany; i++) {\n            const hexChar = this.popChar();\n            if (hexDigitPattern.test(hexChar) === false) {\n                throw Error(\"Expecting a HexDecimal digits\");\n            }\n            hexString += hexChar;\n        }\n        const charCode = parseInt(hexString, 16);\n        return { type: \"Character\", value: charCode };\n    }\n    peekChar(howMuch = 0) {\n        return this.input[this.idx + howMuch];\n    }\n    popChar() {\n        const nextChar = this.peekChar(0);\n        this.consumeChar(undefined);\n        return nextChar;\n    }\n    consumeChar(char) {\n        if (char !== undefined && this.input[this.idx] !== char) {\n            throw Error(\"Expected: '\" +\n                char +\n                \"' but found: '\" +\n                this.input[this.idx] +\n                \"' at offset: \" +\n                this.idx);\n        }\n        if (this.idx >= this.input.length) {\n            throw Error(\"Unexpected end of input\");\n        }\n        this.idx++;\n    }\n    loc(begin) {\n        return { begin: begin, end: this.idx };\n    }\n}\n//# sourceMappingURL=regexp-parser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/regexp-parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@chevrotain/regexp-to-ast/lib/src/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ASSERT_EXISTS: () => (/* binding */ ASSERT_EXISTS),\n/* harmony export */   ASSERT_NEVER_REACH_HERE: () => (/* binding */ ASSERT_NEVER_REACH_HERE),\n/* harmony export */   addFlag: () => (/* binding */ addFlag),\n/* harmony export */   cc: () => (/* binding */ cc),\n/* harmony export */   insertToSet: () => (/* binding */ insertToSet),\n/* harmony export */   isCharacter: () => (/* binding */ isCharacter)\n/* harmony export */ });\nfunction cc(char) {\n    return char.charCodeAt(0);\n}\nfunction insertToSet(item, set) {\n    if (Array.isArray(item)) {\n        item.forEach(function (subItem) {\n            set.push(subItem);\n        });\n    }\n    else {\n        set.push(item);\n    }\n}\nfunction addFlag(flagObj, flagKey) {\n    if (flagObj[flagKey] === true) {\n        throw \"duplicate flag \" + flagKey;\n    }\n    const x = flagObj[flagKey];\n    flagObj[flagKey] = true;\n}\nfunction ASSERT_EXISTS(obj) {\n    // istanbul ignore next\n    if (obj === undefined) {\n        throw Error(\"Internal Error - Should never get here!\");\n    }\n    return true;\n}\n// istanbul ignore next\nfunction ASSERT_NEVER_REACH_HERE() {\n    throw Error(\"Internal Error - Should never get here!\");\n}\nfunction isCharacter(obj) {\n    return obj[\"type\"] === \"Character\";\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vcmVnZXhwLXRvLWFzdC9saWIvc3JjL3V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAY2hldnJvdGFpblxccmVnZXhwLXRvLWFzdFxcbGliXFxzcmNcXHV0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBjYyhjaGFyKSB7XG4gICAgcmV0dXJuIGNoYXIuY2hhckNvZGVBdCgwKTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBpbnNlcnRUb1NldChpdGVtLCBzZXQpIHtcbiAgICBpZiAoQXJyYXkuaXNBcnJheShpdGVtKSkge1xuICAgICAgICBpdGVtLmZvckVhY2goZnVuY3Rpb24gKHN1Ykl0ZW0pIHtcbiAgICAgICAgICAgIHNldC5wdXNoKHN1Ykl0ZW0pO1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHNldC5wdXNoKGl0ZW0pO1xuICAgIH1cbn1cbmV4cG9ydCBmdW5jdGlvbiBhZGRGbGFnKGZsYWdPYmosIGZsYWdLZXkpIHtcbiAgICBpZiAoZmxhZ09ialtmbGFnS2V5XSA9PT0gdHJ1ZSkge1xuICAgICAgICB0aHJvdyBcImR1cGxpY2F0ZSBmbGFnIFwiICsgZmxhZ0tleTtcbiAgICB9XG4gICAgY29uc3QgeCA9IGZsYWdPYmpbZmxhZ0tleV07XG4gICAgZmxhZ09ialtmbGFnS2V5XSA9IHRydWU7XG59XG5leHBvcnQgZnVuY3Rpb24gQVNTRVJUX0VYSVNUUyhvYmopIHtcbiAgICAvLyBpc3RhbmJ1bCBpZ25vcmUgbmV4dFxuICAgIGlmIChvYmogPT09IHVuZGVmaW5lZCkge1xuICAgICAgICB0aHJvdyBFcnJvcihcIkludGVybmFsIEVycm9yIC0gU2hvdWxkIG5ldmVyIGdldCBoZXJlIVwiKTtcbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG59XG4vLyBpc3RhbmJ1bCBpZ25vcmUgbmV4dFxuZXhwb3J0IGZ1bmN0aW9uIEFTU0VSVF9ORVZFUl9SRUFDSF9IRVJFKCkge1xuICAgIHRocm93IEVycm9yKFwiSW50ZXJuYWwgRXJyb3IgLSBTaG91bGQgbmV2ZXIgZ2V0IGhlcmUhXCIpO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGlzQ2hhcmFjdGVyKG9iaikge1xuICAgIHJldHVybiBvYmpbXCJ0eXBlXCJdID09PSBcIkNoYXJhY3RlclwiO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/regexp-to-ast/lib/src/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/utils/lib/src/api.js":
/*!*******************************************************!*\
  !*** ./node_modules/@chevrotain/utils/lib/src/api.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PRINT_ERROR: () => (/* reexport safe */ _print_js__WEBPACK_IMPORTED_MODULE_0__.PRINT_ERROR),\n/* harmony export */   PRINT_WARNING: () => (/* reexport safe */ _print_js__WEBPACK_IMPORTED_MODULE_0__.PRINT_WARNING),\n/* harmony export */   timer: () => (/* reexport safe */ _timer_js__WEBPACK_IMPORTED_MODULE_1__.timer),\n/* harmony export */   toFastProperties: () => (/* reexport safe */ _to_fast_properties_js__WEBPACK_IMPORTED_MODULE_2__.toFastProperties)\n/* harmony export */ });\n/* harmony import */ var _print_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./print.js */ \"(ssr)/./node_modules/@chevrotain/utils/lib/src/print.js\");\n/* harmony import */ var _timer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./timer.js */ \"(ssr)/./node_modules/@chevrotain/utils/lib/src/timer.js\");\n/* harmony import */ var _to_fast_properties_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./to-fast-properties.js */ \"(ssr)/./node_modules/@chevrotain/utils/lib/src/to-fast-properties.js\");\n\n\n\n//# sourceMappingURL=api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vdXRpbHMvbGliL3NyYy9hcGkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF3RDtBQUNyQjtBQUN3QjtBQUMzRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAY2hldnJvdGFpblxcdXRpbHNcXGxpYlxcc3JjXFxhcGkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgUFJJTlRfV0FSTklORywgUFJJTlRfRVJST1IgfSBmcm9tIFwiLi9wcmludC5qc1wiO1xuZXhwb3J0IHsgdGltZXIgfSBmcm9tIFwiLi90aW1lci5qc1wiO1xuZXhwb3J0IHsgdG9GYXN0UHJvcGVydGllcyB9IGZyb20gXCIuL3RvLWZhc3QtcHJvcGVydGllcy5qc1wiO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/utils/lib/src/api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/utils/lib/src/print.js":
/*!*********************************************************!*\
  !*** ./node_modules/@chevrotain/utils/lib/src/print.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PRINT_ERROR: () => (/* binding */ PRINT_ERROR),\n/* harmony export */   PRINT_WARNING: () => (/* binding */ PRINT_WARNING)\n/* harmony export */ });\nfunction PRINT_ERROR(msg) {\n    /* istanbul ignore else - can't override global.console in node.js */\n    if (console && console.error) {\n        console.error(`Error: ${msg}`);\n    }\n}\nfunction PRINT_WARNING(msg) {\n    /* istanbul ignore else - can't override global.console in node.js*/\n    if (console && console.warn) {\n        // TODO: modify docs accordingly\n        console.warn(`Warning: ${msg}`);\n    }\n}\n//# sourceMappingURL=print.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vdXRpbHMvbGliL3NyYy9wcmludC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBLGdDQUFnQyxJQUFJO0FBQ3BDO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxJQUFJO0FBQ3JDO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAY2hldnJvdGFpblxcdXRpbHNcXGxpYlxcc3JjXFxwcmludC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gUFJJTlRfRVJST1IobXNnKSB7XG4gICAgLyogaXN0YW5idWwgaWdub3JlIGVsc2UgLSBjYW4ndCBvdmVycmlkZSBnbG9iYWwuY29uc29sZSBpbiBub2RlLmpzICovXG4gICAgaWYgKGNvbnNvbGUgJiYgY29uc29sZS5lcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKGBFcnJvcjogJHttc2d9YCk7XG4gICAgfVxufVxuZXhwb3J0IGZ1bmN0aW9uIFBSSU5UX1dBUk5JTkcobXNnKSB7XG4gICAgLyogaXN0YW5idWwgaWdub3JlIGVsc2UgLSBjYW4ndCBvdmVycmlkZSBnbG9iYWwuY29uc29sZSBpbiBub2RlLmpzKi9cbiAgICBpZiAoY29uc29sZSAmJiBjb25zb2xlLndhcm4pIHtcbiAgICAgICAgLy8gVE9ETzogbW9kaWZ5IGRvY3MgYWNjb3JkaW5nbHlcbiAgICAgICAgY29uc29sZS53YXJuKGBXYXJuaW5nOiAke21zZ31gKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wcmludC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/utils/lib/src/print.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/utils/lib/src/timer.js":
/*!*********************************************************!*\
  !*** ./node_modules/@chevrotain/utils/lib/src/timer.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timer: () => (/* binding */ timer)\n/* harmony export */ });\nfunction timer(func) {\n    const start = new Date().getTime();\n    const val = func();\n    const end = new Date().getTime();\n    const total = end - start;\n    return { time: total, value: val };\n}\n//# sourceMappingURL=timer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vdXRpbHMvbGliL3NyYy90aW1lci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEBjaGV2cm90YWluXFx1dGlsc1xcbGliXFxzcmNcXHRpbWVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiB0aW1lcihmdW5jKSB7XG4gICAgY29uc3Qgc3RhcnQgPSBuZXcgRGF0ZSgpLmdldFRpbWUoKTtcbiAgICBjb25zdCB2YWwgPSBmdW5jKCk7XG4gICAgY29uc3QgZW5kID0gbmV3IERhdGUoKS5nZXRUaW1lKCk7XG4gICAgY29uc3QgdG90YWwgPSBlbmQgLSBzdGFydDtcbiAgICByZXR1cm4geyB0aW1lOiB0b3RhbCwgdmFsdWU6IHZhbCB9O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dGltZXIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/utils/lib/src/timer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@chevrotain/utils/lib/src/to-fast-properties.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@chevrotain/utils/lib/src/to-fast-properties.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toFastProperties: () => (/* binding */ toFastProperties)\n/* harmony export */ });\n// based on: https://github.com/petkaantonov/bluebird/blob/b97c0d2d487e8c5076e8bd897e0dcd4622d31846/src/util.js#L201-L216\nfunction toFastProperties(toBecomeFast) {\n    function FakeConstructor() { }\n    // If our object is used as a constructor, it would receive\n    FakeConstructor.prototype = toBecomeFast;\n    const fakeInstance = new FakeConstructor();\n    function fakeAccess() {\n        return typeof fakeInstance.bar;\n    }\n    // help V8 understand this is a \"real\" prototype by actually using\n    // the fake instance.\n    fakeAccess();\n    fakeAccess();\n    // Always true condition to suppress the Firefox warning of unreachable\n    // code after a return statement.\n    if (true)\n        return toBecomeFast;\n    // Eval prevents optimization of this method (even though this is dead code)\n    // - https://esbuild.github.io/content-types/#direct-eval\n    /* istanbul ignore next */\n    // tslint:disable-next-line\n    (0, eval)(toBecomeFast);\n}\n//# sourceMappingURL=to-fast-properties.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGNoZXZyb3RhaW4vdXRpbHMvbGliL3NyYy90by1mYXN0LXByb3BlcnRpZXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsSUFBQztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQGNoZXZyb3RhaW5cXHV0aWxzXFxsaWJcXHNyY1xcdG8tZmFzdC1wcm9wZXJ0aWVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGJhc2VkIG9uOiBodHRwczovL2dpdGh1Yi5jb20vcGV0a2FhbnRvbm92L2JsdWViaXJkL2Jsb2IvYjk3YzBkMmQ0ODdlOGM1MDc2ZThiZDg5N2UwZGNkNDYyMmQzMTg0Ni9zcmMvdXRpbC5qcyNMMjAxLUwyMTZcbmV4cG9ydCBmdW5jdGlvbiB0b0Zhc3RQcm9wZXJ0aWVzKHRvQmVjb21lRmFzdCkge1xuICAgIGZ1bmN0aW9uIEZha2VDb25zdHJ1Y3RvcigpIHsgfVxuICAgIC8vIElmIG91ciBvYmplY3QgaXMgdXNlZCBhcyBhIGNvbnN0cnVjdG9yLCBpdCB3b3VsZCByZWNlaXZlXG4gICAgRmFrZUNvbnN0cnVjdG9yLnByb3RvdHlwZSA9IHRvQmVjb21lRmFzdDtcbiAgICBjb25zdCBmYWtlSW5zdGFuY2UgPSBuZXcgRmFrZUNvbnN0cnVjdG9yKCk7XG4gICAgZnVuY3Rpb24gZmFrZUFjY2VzcygpIHtcbiAgICAgICAgcmV0dXJuIHR5cGVvZiBmYWtlSW5zdGFuY2UuYmFyO1xuICAgIH1cbiAgICAvLyBoZWxwIFY4IHVuZGVyc3RhbmQgdGhpcyBpcyBhIFwicmVhbFwiIHByb3RvdHlwZSBieSBhY3R1YWxseSB1c2luZ1xuICAgIC8vIHRoZSBmYWtlIGluc3RhbmNlLlxuICAgIGZha2VBY2Nlc3MoKTtcbiAgICBmYWtlQWNjZXNzKCk7XG4gICAgLy8gQWx3YXlzIHRydWUgY29uZGl0aW9uIHRvIHN1cHByZXNzIHRoZSBGaXJlZm94IHdhcm5pbmcgb2YgdW5yZWFjaGFibGVcbiAgICAvLyBjb2RlIGFmdGVyIGEgcmV0dXJuIHN0YXRlbWVudC5cbiAgICBpZiAoMSlcbiAgICAgICAgcmV0dXJuIHRvQmVjb21lRmFzdDtcbiAgICAvLyBFdmFsIHByZXZlbnRzIG9wdGltaXphdGlvbiBvZiB0aGlzIG1ldGhvZCAoZXZlbiB0aG91Z2ggdGhpcyBpcyBkZWFkIGNvZGUpXG4gICAgLy8gLSBodHRwczovL2VzYnVpbGQuZ2l0aHViLmlvL2NvbnRlbnQtdHlwZXMvI2RpcmVjdC1ldmFsXG4gICAgLyogaXN0YW5idWwgaWdub3JlIG5leHQgKi9cbiAgICAvLyB0c2xpbnQ6ZGlzYWJsZS1uZXh0LWxpbmVcbiAgICAoMCwgZXZhbCkodG9CZWNvbWVGYXN0KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRvLWZhc3QtcHJvcGVydGllcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@chevrotain/utils/lib/src/to-fast-properties.js\n");

/***/ })

};
;