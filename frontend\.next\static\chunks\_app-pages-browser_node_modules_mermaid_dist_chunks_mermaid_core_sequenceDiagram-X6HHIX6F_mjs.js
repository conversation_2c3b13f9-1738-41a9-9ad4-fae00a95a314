"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_chunks_mermaid_core_sequenceDiagram-X6HHIX6F_mjs"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-D6G4REZN.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-D6G4REZN.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   drawBackgroundRect: () => (/* binding */ drawBackgroundRect),\n/* harmony export */   drawEmbeddedImage: () => (/* binding */ drawEmbeddedImage),\n/* harmony export */   drawImage: () => (/* binding */ drawImage),\n/* harmony export */   drawRect: () => (/* binding */ drawRect),\n/* harmony export */   drawText: () => (/* binding */ drawText),\n/* harmony export */   getNoteRect: () => (/* binding */ getNoteRect),\n/* harmony export */   getTextObj: () => (/* binding */ getTextObj)\n/* harmony export */ });\n/* harmony import */ var _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-YTJNT7DU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-YTJNT7DU.mjs\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n\n\n// src/diagrams/common/svgDrawCommon.ts\n\nvar drawRect = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, rectData) => {\n  const rectElement = element.append(\"rect\");\n  rectElement.attr(\"x\", rectData.x);\n  rectElement.attr(\"y\", rectData.y);\n  rectElement.attr(\"fill\", rectData.fill);\n  rectElement.attr(\"stroke\", rectData.stroke);\n  rectElement.attr(\"width\", rectData.width);\n  rectElement.attr(\"height\", rectData.height);\n  if (rectData.name) {\n    rectElement.attr(\"name\", rectData.name);\n  }\n  if (rectData.rx) {\n    rectElement.attr(\"rx\", rectData.rx);\n  }\n  if (rectData.ry) {\n    rectElement.attr(\"ry\", rectData.ry);\n  }\n  if (rectData.attrs !== void 0) {\n    for (const attrKey in rectData.attrs) {\n      rectElement.attr(attrKey, rectData.attrs[attrKey]);\n    }\n  }\n  if (rectData.class) {\n    rectElement.attr(\"class\", rectData.class);\n  }\n  return rectElement;\n}, \"drawRect\");\nvar drawBackgroundRect = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, bounds) => {\n  const rectData = {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    stroke: bounds.stroke,\n    class: \"rect\"\n  };\n  const rectElement = drawRect(element, rectData);\n  rectElement.lower();\n}, \"drawBackgroundRect\");\nvar drawText = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, textData) => {\n  const nText = textData.text.replace(_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.lineBreakRegex, \" \");\n  const textElem = element.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  if (textData.class) {\n    textElem.attr(\"class\", textData.class);\n  }\n  const tspan = textElem.append(\"tspan\");\n  tspan.attr(\"x\", textData.x + textData.textMargin * 2);\n  tspan.text(nText);\n  return textElem;\n}, \"drawText\");\nvar drawImage = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((elem, x, y, link) => {\n  const imageElement = elem.append(\"image\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__.sanitizeUrl)(link);\n  imageElement.attr(\"xlink:href\", sanitizedLink);\n}, \"drawImage\");\nvar drawEmbeddedImage = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((element, x, y, link) => {\n  const imageElement = element.append(\"use\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__.sanitizeUrl)(link);\n  imageElement.attr(\"xlink:href\", `#${sanitizedLink}`);\n}, \"drawEmbeddedImage\");\nvar getNoteRect = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => {\n  const noteRectData = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    fill: \"#EDF2AE\",\n    stroke: \"#666\",\n    anchor: \"start\",\n    rx: 0,\n    ry: 0\n  };\n  return noteRectData;\n}, \"getNoteRect\");\nvar getTextObj = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => {\n  const testObject = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    \"text-anchor\": \"start\",\n    style: \"#666\",\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true\n  };\n  return testObject;\n}, \"getTextObj\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZXJtYWlkL2Rpc3QvY2h1bmtzL21lcm1haWQuY29yZS9jaHVuay1ENkc0UkVaTi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRzhCOztBQUU5QjtBQUNzRDtBQUN0RCwrQkFBK0IsMkRBQU07QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QseUNBQXlDLDJEQUFNO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QsK0JBQStCLDJEQUFNO0FBQ3JDLHNDQUFzQywrREFBYztBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QsZ0NBQWdDLDJEQUFNO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixvRUFBVztBQUNuQztBQUNBLENBQUM7QUFDRCx3Q0FBd0MsMkRBQU07QUFDOUM7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLG9FQUFXO0FBQ25DLHNDQUFzQyxjQUFjO0FBQ3BELENBQUM7QUFDRCxrQ0FBa0MsMkRBQU07QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELGlDQUFpQywyREFBTTtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBVUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbWVybWFpZFxcZGlzdFxcY2h1bmtzXFxtZXJtYWlkLmNvcmVcXGNodW5rLUQ2RzRSRVpOLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBfX25hbWUsXG4gIGxpbmVCcmVha1JlZ2V4XG59IGZyb20gXCIuL2NodW5rLVlUSk5UN0RVLm1qc1wiO1xuXG4vLyBzcmMvZGlhZ3JhbXMvY29tbW9uL3N2Z0RyYXdDb21tb24udHNcbmltcG9ydCB7IHNhbml0aXplVXJsIH0gZnJvbSBcIkBicmFpbnRyZWUvc2FuaXRpemUtdXJsXCI7XG52YXIgZHJhd1JlY3QgPSAvKiBAX19QVVJFX18gKi8gX19uYW1lKChlbGVtZW50LCByZWN0RGF0YSkgPT4ge1xuICBjb25zdCByZWN0RWxlbWVudCA9IGVsZW1lbnQuYXBwZW5kKFwicmVjdFwiKTtcbiAgcmVjdEVsZW1lbnQuYXR0cihcInhcIiwgcmVjdERhdGEueCk7XG4gIHJlY3RFbGVtZW50LmF0dHIoXCJ5XCIsIHJlY3REYXRhLnkpO1xuICByZWN0RWxlbWVudC5hdHRyKFwiZmlsbFwiLCByZWN0RGF0YS5maWxsKTtcbiAgcmVjdEVsZW1lbnQuYXR0cihcInN0cm9rZVwiLCByZWN0RGF0YS5zdHJva2UpO1xuICByZWN0RWxlbWVudC5hdHRyKFwid2lkdGhcIiwgcmVjdERhdGEud2lkdGgpO1xuICByZWN0RWxlbWVudC5hdHRyKFwiaGVpZ2h0XCIsIHJlY3REYXRhLmhlaWdodCk7XG4gIGlmIChyZWN0RGF0YS5uYW1lKSB7XG4gICAgcmVjdEVsZW1lbnQuYXR0cihcIm5hbWVcIiwgcmVjdERhdGEubmFtZSk7XG4gIH1cbiAgaWYgKHJlY3REYXRhLnJ4KSB7XG4gICAgcmVjdEVsZW1lbnQuYXR0cihcInJ4XCIsIHJlY3REYXRhLnJ4KTtcbiAgfVxuICBpZiAocmVjdERhdGEucnkpIHtcbiAgICByZWN0RWxlbWVudC5hdHRyKFwicnlcIiwgcmVjdERhdGEucnkpO1xuICB9XG4gIGlmIChyZWN0RGF0YS5hdHRycyAhPT0gdm9pZCAwKSB7XG4gICAgZm9yIChjb25zdCBhdHRyS2V5IGluIHJlY3REYXRhLmF0dHJzKSB7XG4gICAgICByZWN0RWxlbWVudC5hdHRyKGF0dHJLZXksIHJlY3REYXRhLmF0dHJzW2F0dHJLZXldKTtcbiAgICB9XG4gIH1cbiAgaWYgKHJlY3REYXRhLmNsYXNzKSB7XG4gICAgcmVjdEVsZW1lbnQuYXR0cihcImNsYXNzXCIsIHJlY3REYXRhLmNsYXNzKTtcbiAgfVxuICByZXR1cm4gcmVjdEVsZW1lbnQ7XG59LCBcImRyYXdSZWN0XCIpO1xudmFyIGRyYXdCYWNrZ3JvdW5kUmVjdCA9IC8qIEBfX1BVUkVfXyAqLyBfX25hbWUoKGVsZW1lbnQsIGJvdW5kcykgPT4ge1xuICBjb25zdCByZWN0RGF0YSA9IHtcbiAgICB4OiBib3VuZHMuc3RhcnR4LFxuICAgIHk6IGJvdW5kcy5zdGFydHksXG4gICAgd2lkdGg6IGJvdW5kcy5zdG9weCAtIGJvdW5kcy5zdGFydHgsXG4gICAgaGVpZ2h0OiBib3VuZHMuc3RvcHkgLSBib3VuZHMuc3RhcnR5LFxuICAgIGZpbGw6IGJvdW5kcy5maWxsLFxuICAgIHN0cm9rZTogYm91bmRzLnN0cm9rZSxcbiAgICBjbGFzczogXCJyZWN0XCJcbiAgfTtcbiAgY29uc3QgcmVjdEVsZW1lbnQgPSBkcmF3UmVjdChlbGVtZW50LCByZWN0RGF0YSk7XG4gIHJlY3RFbGVtZW50Lmxvd2VyKCk7XG59LCBcImRyYXdCYWNrZ3JvdW5kUmVjdFwiKTtcbnZhciBkcmF3VGV4dCA9IC8qIEBfX1BVUkVfXyAqLyBfX25hbWUoKGVsZW1lbnQsIHRleHREYXRhKSA9PiB7XG4gIGNvbnN0IG5UZXh0ID0gdGV4dERhdGEudGV4dC5yZXBsYWNlKGxpbmVCcmVha1JlZ2V4LCBcIiBcIik7XG4gIGNvbnN0IHRleHRFbGVtID0gZWxlbWVudC5hcHBlbmQoXCJ0ZXh0XCIpO1xuICB0ZXh0RWxlbS5hdHRyKFwieFwiLCB0ZXh0RGF0YS54KTtcbiAgdGV4dEVsZW0uYXR0cihcInlcIiwgdGV4dERhdGEueSk7XG4gIHRleHRFbGVtLmF0dHIoXCJjbGFzc1wiLCBcImxlZ2VuZFwiKTtcbiAgdGV4dEVsZW0uc3R5bGUoXCJ0ZXh0LWFuY2hvclwiLCB0ZXh0RGF0YS5hbmNob3IpO1xuICBpZiAodGV4dERhdGEuY2xhc3MpIHtcbiAgICB0ZXh0RWxlbS5hdHRyKFwiY2xhc3NcIiwgdGV4dERhdGEuY2xhc3MpO1xuICB9XG4gIGNvbnN0IHRzcGFuID0gdGV4dEVsZW0uYXBwZW5kKFwidHNwYW5cIik7XG4gIHRzcGFuLmF0dHIoXCJ4XCIsIHRleHREYXRhLnggKyB0ZXh0RGF0YS50ZXh0TWFyZ2luICogMik7XG4gIHRzcGFuLnRleHQoblRleHQpO1xuICByZXR1cm4gdGV4dEVsZW07XG59LCBcImRyYXdUZXh0XCIpO1xudmFyIGRyYXdJbWFnZSA9IC8qIEBfX1BVUkVfXyAqLyBfX25hbWUoKGVsZW0sIHgsIHksIGxpbmspID0+IHtcbiAgY29uc3QgaW1hZ2VFbGVtZW50ID0gZWxlbS5hcHBlbmQoXCJpbWFnZVwiKTtcbiAgaW1hZ2VFbGVtZW50LmF0dHIoXCJ4XCIsIHgpO1xuICBpbWFnZUVsZW1lbnQuYXR0cihcInlcIiwgeSk7XG4gIGNvbnN0IHNhbml0aXplZExpbmsgPSBzYW5pdGl6ZVVybChsaW5rKTtcbiAgaW1hZ2VFbGVtZW50LmF0dHIoXCJ4bGluazpocmVmXCIsIHNhbml0aXplZExpbmspO1xufSwgXCJkcmF3SW1hZ2VcIik7XG52YXIgZHJhd0VtYmVkZGVkSW1hZ2UgPSAvKiBAX19QVVJFX18gKi8gX19uYW1lKChlbGVtZW50LCB4LCB5LCBsaW5rKSA9PiB7XG4gIGNvbnN0IGltYWdlRWxlbWVudCA9IGVsZW1lbnQuYXBwZW5kKFwidXNlXCIpO1xuICBpbWFnZUVsZW1lbnQuYXR0cihcInhcIiwgeCk7XG4gIGltYWdlRWxlbWVudC5hdHRyKFwieVwiLCB5KTtcbiAgY29uc3Qgc2FuaXRpemVkTGluayA9IHNhbml0aXplVXJsKGxpbmspO1xuICBpbWFnZUVsZW1lbnQuYXR0cihcInhsaW5rOmhyZWZcIiwgYCMke3Nhbml0aXplZExpbmt9YCk7XG59LCBcImRyYXdFbWJlZGRlZEltYWdlXCIpO1xudmFyIGdldE5vdGVSZWN0ID0gLyogQF9fUFVSRV9fICovIF9fbmFtZSgoKSA9PiB7XG4gIGNvbnN0IG5vdGVSZWN0RGF0YSA9IHtcbiAgICB4OiAwLFxuICAgIHk6IDAsXG4gICAgd2lkdGg6IDEwMCxcbiAgICBoZWlnaHQ6IDEwMCxcbiAgICBmaWxsOiBcIiNFREYyQUVcIixcbiAgICBzdHJva2U6IFwiIzY2NlwiLFxuICAgIGFuY2hvcjogXCJzdGFydFwiLFxuICAgIHJ4OiAwLFxuICAgIHJ5OiAwXG4gIH07XG4gIHJldHVybiBub3RlUmVjdERhdGE7XG59LCBcImdldE5vdGVSZWN0XCIpO1xudmFyIGdldFRleHRPYmogPSAvKiBAX19QVVJFX18gKi8gX19uYW1lKCgpID0+IHtcbiAgY29uc3QgdGVzdE9iamVjdCA9IHtcbiAgICB4OiAwLFxuICAgIHk6IDAsXG4gICAgd2lkdGg6IDEwMCxcbiAgICBoZWlnaHQ6IDEwMCxcbiAgICBcInRleHQtYW5jaG9yXCI6IFwic3RhcnRcIixcbiAgICBzdHlsZTogXCIjNjY2XCIsXG4gICAgdGV4dE1hcmdpbjogMCxcbiAgICByeDogMCxcbiAgICByeTogMCxcbiAgICB0c3BhbjogdHJ1ZVxuICB9O1xuICByZXR1cm4gdGVzdE9iamVjdDtcbn0sIFwiZ2V0VGV4dE9ialwiKTtcblxuZXhwb3J0IHtcbiAgZHJhd1JlY3QsXG4gIGRyYXdCYWNrZ3JvdW5kUmVjdCxcbiAgZHJhd1RleHQsXG4gIGRyYXdJbWFnZSxcbiAgZHJhd0VtYmVkZGVkSW1hZ2UsXG4gIGdldE5vdGVSZWN0LFxuICBnZXRUZXh0T2JqXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-D6G4REZN.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-XZIHB7SX.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-XZIHB7SX.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImperativeState: () => (/* binding */ ImperativeState)\n/* harmony export */ });\n/* harmony import */ var _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-YTJNT7DU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-YTJNT7DU.mjs\");\n\n\n// src/utils/imperativeState.ts\nvar ImperativeState = class {\n  /**\n   * @param init - Function that creates the default state.\n   */\n  constructor(init) {\n    this.init = init;\n    this.records = this.init();\n  }\n  static {\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(this, \"ImperativeState\");\n  }\n  reset() {\n    this.records = this.init();\n  }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZXJtYWlkL2Rpc3QvY2h1bmtzL21lcm1haWQuY29yZS9jaHVuay1YWklIQjdTWC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFFOEI7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyREFBTTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBSUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbWVybWFpZFxcZGlzdFxcY2h1bmtzXFxtZXJtYWlkLmNvcmVcXGNodW5rLVhaSUhCN1NYLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBfX25hbWVcbn0gZnJvbSBcIi4vY2h1bmstWVRKTlQ3RFUubWpzXCI7XG5cbi8vIHNyYy91dGlscy9pbXBlcmF0aXZlU3RhdGUudHNcbnZhciBJbXBlcmF0aXZlU3RhdGUgPSBjbGFzcyB7XG4gIC8qKlxuICAgKiBAcGFyYW0gaW5pdCAtIEZ1bmN0aW9uIHRoYXQgY3JlYXRlcyB0aGUgZGVmYXVsdCBzdGF0ZS5cbiAgICovXG4gIGNvbnN0cnVjdG9yKGluaXQpIHtcbiAgICB0aGlzLmluaXQgPSBpbml0O1xuICAgIHRoaXMucmVjb3JkcyA9IHRoaXMuaW5pdCgpO1xuICB9XG4gIHN0YXRpYyB7XG4gICAgX19uYW1lKHRoaXMsIFwiSW1wZXJhdGl2ZVN0YXRlXCIpO1xuICB9XG4gIHJlc2V0KCkge1xuICAgIHRoaXMucmVjb3JkcyA9IHRoaXMuaW5pdCgpO1xuICB9XG59O1xuXG5leHBvcnQge1xuICBJbXBlcmF0aXZlU3RhdGVcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-XZIHB7SX.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/sequenceDiagram-X6HHIX6F.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/sequenceDiagram-X6HHIX6F.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: () => (/* binding */ diagram)\n/* harmony export */ });\n/* harmony import */ var _chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-D6G4REZN.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-D6G4REZN.mjs\");\n/* harmony import */ var _chunk_XZIHB7SX_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-XZIHB7SX.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-XZIHB7SX.mjs\");\n/* harmony import */ var _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-O4NI6UNU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-O4NI6UNU.mjs\");\n/* harmony import */ var _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-YTJNT7DU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-YTJNT7DU.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n\n\n\n\n\n// src/diagrams/sequence/parser/sequenceDiagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 2], $V1 = [1, 3], $V2 = [1, 4], $V3 = [2, 4], $V4 = [1, 9], $V5 = [1, 11], $V6 = [1, 13], $V7 = [1, 14], $V8 = [1, 16], $V9 = [1, 17], $Va = [1, 18], $Vb = [1, 24], $Vc = [1, 25], $Vd = [1, 26], $Ve = [1, 27], $Vf = [1, 28], $Vg = [1, 29], $Vh = [1, 30], $Vi = [1, 31], $Vj = [1, 32], $Vk = [1, 33], $Vl = [1, 34], $Vm = [1, 35], $Vn = [1, 36], $Vo = [1, 37], $Vp = [1, 38], $Vq = [1, 39], $Vr = [1, 41], $Vs = [1, 42], $Vt = [1, 43], $Vu = [1, 44], $Vv = [1, 45], $Vw = [1, 46], $Vx = [1, 4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 47, 48, 49, 50, 52, 53, 54, 59, 60, 61, 62, 70], $Vy = [4, 5, 16, 50, 52, 53], $Vz = [4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 50, 52, 53, 54, 59, 60, 61, 62, 70], $VA = [4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 49, 50, 52, 53, 54, 59, 60, 61, 62, 70], $VB = [4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 48, 50, 52, 53, 54, 59, 60, 61, 62, 70], $VC = [4, 5, 13, 14, 16, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 47, 50, 52, 53, 54, 59, 60, 61, 62, 70], $VD = [68, 69, 70], $VE = [1, 122];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"SPACE\": 4, \"NEWLINE\": 5, \"SD\": 6, \"document\": 7, \"line\": 8, \"statement\": 9, \"box_section\": 10, \"box_line\": 11, \"participant_statement\": 12, \"create\": 13, \"box\": 14, \"restOfLine\": 15, \"end\": 16, \"signal\": 17, \"autonumber\": 18, \"NUM\": 19, \"off\": 20, \"activate\": 21, \"actor\": 22, \"deactivate\": 23, \"note_statement\": 24, \"links_statement\": 25, \"link_statement\": 26, \"properties_statement\": 27, \"details_statement\": 28, \"title\": 29, \"legacy_title\": 30, \"acc_title\": 31, \"acc_title_value\": 32, \"acc_descr\": 33, \"acc_descr_value\": 34, \"acc_descr_multiline_value\": 35, \"loop\": 36, \"rect\": 37, \"opt\": 38, \"alt\": 39, \"else_sections\": 40, \"par\": 41, \"par_sections\": 42, \"par_over\": 43, \"critical\": 44, \"option_sections\": 45, \"break\": 46, \"option\": 47, \"and\": 48, \"else\": 49, \"participant\": 50, \"AS\": 51, \"participant_actor\": 52, \"destroy\": 53, \"note\": 54, \"placement\": 55, \"text2\": 56, \"over\": 57, \"actor_pair\": 58, \"links\": 59, \"link\": 60, \"properties\": 61, \"details\": 62, \"spaceList\": 63, \",\": 64, \"left_of\": 65, \"right_of\": 66, \"signaltype\": 67, \"+\": 68, \"-\": 69, \"ACTOR\": 70, \"SOLID_OPEN_ARROW\": 71, \"DOTTED_OPEN_ARROW\": 72, \"SOLID_ARROW\": 73, \"BIDIRECTIONAL_SOLID_ARROW\": 74, \"DOTTED_ARROW\": 75, \"BIDIRECTIONAL_DOTTED_ARROW\": 76, \"SOLID_CROSS\": 77, \"DOTTED_CROSS\": 78, \"SOLID_POINT\": 79, \"DOTTED_POINT\": 80, \"TXT\": 81, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SPACE\", 5: \"NEWLINE\", 6: \"SD\", 13: \"create\", 14: \"box\", 15: \"restOfLine\", 16: \"end\", 18: \"autonumber\", 19: \"NUM\", 20: \"off\", 21: \"activate\", 23: \"deactivate\", 29: \"title\", 30: \"legacy_title\", 31: \"acc_title\", 32: \"acc_title_value\", 33: \"acc_descr\", 34: \"acc_descr_value\", 35: \"acc_descr_multiline_value\", 36: \"loop\", 37: \"rect\", 38: \"opt\", 39: \"alt\", 41: \"par\", 43: \"par_over\", 44: \"critical\", 46: \"break\", 47: \"option\", 48: \"and\", 49: \"else\", 50: \"participant\", 51: \"AS\", 52: \"participant_actor\", 53: \"destroy\", 54: \"note\", 57: \"over\", 59: \"links\", 60: \"link\", 61: \"properties\", 62: \"details\", 64: \",\", 65: \"left_of\", 66: \"right_of\", 68: \"+\", 69: \"-\", 70: \"ACTOR\", 71: \"SOLID_OPEN_ARROW\", 72: \"DOTTED_OPEN_ARROW\", 73: \"SOLID_ARROW\", 74: \"BIDIRECTIONAL_SOLID_ARROW\", 75: \"DOTTED_ARROW\", 76: \"BIDIRECTIONAL_DOTTED_ARROW\", 77: \"SOLID_CROSS\", 78: \"DOTTED_CROSS\", 79: \"SOLID_POINT\", 80: \"DOTTED_POINT\", 81: \"TXT\" },\n    productions_: [0, [3, 2], [3, 2], [3, 2], [7, 0], [7, 2], [8, 2], [8, 1], [8, 1], [10, 0], [10, 2], [11, 2], [11, 1], [11, 1], [9, 1], [9, 2], [9, 4], [9, 2], [9, 4], [9, 3], [9, 3], [9, 2], [9, 3], [9, 3], [9, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 4], [9, 4], [9, 4], [9, 4], [9, 4], [9, 4], [9, 4], [9, 4], [45, 1], [45, 4], [42, 1], [42, 4], [40, 1], [40, 4], [12, 5], [12, 3], [12, 5], [12, 3], [12, 3], [24, 4], [24, 4], [25, 3], [26, 3], [27, 3], [28, 3], [63, 2], [63, 1], [58, 3], [58, 1], [55, 1], [55, 1], [17, 5], [17, 5], [17, 4], [22, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [67, 1], [56, 1]],\n    performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.apply($$[$0]);\n          return $$[$0];\n          break;\n        case 4:\n        case 9:\n          this.$ = [];\n          break;\n        case 5:\n        case 10:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 6:\n        case 7:\n        case 11:\n        case 12:\n          this.$ = $$[$0];\n          break;\n        case 8:\n        case 13:\n          this.$ = [];\n          break;\n        case 15:\n          $$[$0].type = \"createParticipant\";\n          this.$ = $$[$0];\n          break;\n        case 16:\n          $$[$0 - 1].unshift({ type: \"boxStart\", boxData: yy.parseBoxData($$[$0 - 2]) });\n          $$[$0 - 1].push({ type: \"boxEnd\", boxText: $$[$0 - 2] });\n          this.$ = $$[$0 - 1];\n          break;\n        case 18:\n          this.$ = { type: \"sequenceIndex\", sequenceIndex: Number($$[$0 - 2]), sequenceIndexStep: Number($$[$0 - 1]), sequenceVisible: true, signalType: yy.LINETYPE.AUTONUMBER };\n          break;\n        case 19:\n          this.$ = { type: \"sequenceIndex\", sequenceIndex: Number($$[$0 - 1]), sequenceIndexStep: 1, sequenceVisible: true, signalType: yy.LINETYPE.AUTONUMBER };\n          break;\n        case 20:\n          this.$ = { type: \"sequenceIndex\", sequenceVisible: false, signalType: yy.LINETYPE.AUTONUMBER };\n          break;\n        case 21:\n          this.$ = { type: \"sequenceIndex\", sequenceVisible: true, signalType: yy.LINETYPE.AUTONUMBER };\n          break;\n        case 22:\n          this.$ = { type: \"activeStart\", signalType: yy.LINETYPE.ACTIVE_START, actor: $$[$0 - 1].actor };\n          break;\n        case 23:\n          this.$ = { type: \"activeEnd\", signalType: yy.LINETYPE.ACTIVE_END, actor: $$[$0 - 1].actor };\n          break;\n        case 29:\n          yy.setDiagramTitle($$[$0].substring(6));\n          this.$ = $$[$0].substring(6);\n          break;\n        case 30:\n          yy.setDiagramTitle($$[$0].substring(7));\n          this.$ = $$[$0].substring(7);\n          break;\n        case 31:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 32:\n        case 33:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 34:\n          $$[$0 - 1].unshift({ type: \"loopStart\", loopText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.LOOP_START });\n          $$[$0 - 1].push({ type: \"loopEnd\", loopText: $$[$0 - 2], signalType: yy.LINETYPE.LOOP_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 35:\n          $$[$0 - 1].unshift({ type: \"rectStart\", color: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.RECT_START });\n          $$[$0 - 1].push({ type: \"rectEnd\", color: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.RECT_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 36:\n          $$[$0 - 1].unshift({ type: \"optStart\", optText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.OPT_START });\n          $$[$0 - 1].push({ type: \"optEnd\", optText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.OPT_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 37:\n          $$[$0 - 1].unshift({ type: \"altStart\", altText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.ALT_START });\n          $$[$0 - 1].push({ type: \"altEnd\", signalType: yy.LINETYPE.ALT_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 38:\n          $$[$0 - 1].unshift({ type: \"parStart\", parText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.PAR_START });\n          $$[$0 - 1].push({ type: \"parEnd\", signalType: yy.LINETYPE.PAR_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 39:\n          $$[$0 - 1].unshift({ type: \"parStart\", parText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.PAR_OVER_START });\n          $$[$0 - 1].push({ type: \"parEnd\", signalType: yy.LINETYPE.PAR_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 40:\n          $$[$0 - 1].unshift({ type: \"criticalStart\", criticalText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.CRITICAL_START });\n          $$[$0 - 1].push({ type: \"criticalEnd\", signalType: yy.LINETYPE.CRITICAL_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 41:\n          $$[$0 - 1].unshift({ type: \"breakStart\", breakText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.BREAK_START });\n          $$[$0 - 1].push({ type: \"breakEnd\", optText: yy.parseMessage($$[$0 - 2]), signalType: yy.LINETYPE.BREAK_END });\n          this.$ = $$[$0 - 1];\n          break;\n        case 43:\n          this.$ = $$[$0 - 3].concat([{ type: \"option\", optionText: yy.parseMessage($$[$0 - 1]), signalType: yy.LINETYPE.CRITICAL_OPTION }, $$[$0]]);\n          break;\n        case 45:\n          this.$ = $$[$0 - 3].concat([{ type: \"and\", parText: yy.parseMessage($$[$0 - 1]), signalType: yy.LINETYPE.PAR_AND }, $$[$0]]);\n          break;\n        case 47:\n          this.$ = $$[$0 - 3].concat([{ type: \"else\", altText: yy.parseMessage($$[$0 - 1]), signalType: yy.LINETYPE.ALT_ELSE }, $$[$0]]);\n          break;\n        case 48:\n          $$[$0 - 3].draw = \"participant\";\n          $$[$0 - 3].type = \"addParticipant\";\n          $$[$0 - 3].description = yy.parseMessage($$[$0 - 1]);\n          this.$ = $$[$0 - 3];\n          break;\n        case 49:\n          $$[$0 - 1].draw = \"participant\";\n          $$[$0 - 1].type = \"addParticipant\";\n          this.$ = $$[$0 - 1];\n          break;\n        case 50:\n          $$[$0 - 3].draw = \"actor\";\n          $$[$0 - 3].type = \"addParticipant\";\n          $$[$0 - 3].description = yy.parseMessage($$[$0 - 1]);\n          this.$ = $$[$0 - 3];\n          break;\n        case 51:\n          $$[$0 - 1].draw = \"actor\";\n          $$[$0 - 1].type = \"addParticipant\";\n          this.$ = $$[$0 - 1];\n          break;\n        case 52:\n          $$[$0 - 1].type = \"destroyParticipant\";\n          this.$ = $$[$0 - 1];\n          break;\n        case 53:\n          this.$ = [$$[$0 - 1], { type: \"addNote\", placement: $$[$0 - 2], actor: $$[$0 - 1].actor, text: $$[$0] }];\n          break;\n        case 54:\n          $$[$0 - 2] = [].concat($$[$0 - 1], $$[$0 - 1]).slice(0, 2);\n          $$[$0 - 2][0] = $$[$0 - 2][0].actor;\n          $$[$0 - 2][1] = $$[$0 - 2][1].actor;\n          this.$ = [$$[$0 - 1], { type: \"addNote\", placement: yy.PLACEMENT.OVER, actor: $$[$0 - 2].slice(0, 2), text: $$[$0] }];\n          break;\n        case 55:\n          this.$ = [$$[$0 - 1], { type: \"addLinks\", actor: $$[$0 - 1].actor, text: $$[$0] }];\n          break;\n        case 56:\n          this.$ = [$$[$0 - 1], { type: \"addALink\", actor: $$[$0 - 1].actor, text: $$[$0] }];\n          break;\n        case 57:\n          this.$ = [$$[$0 - 1], { type: \"addProperties\", actor: $$[$0 - 1].actor, text: $$[$0] }];\n          break;\n        case 58:\n          this.$ = [$$[$0 - 1], { type: \"addDetails\", actor: $$[$0 - 1].actor, text: $$[$0] }];\n          break;\n        case 61:\n          this.$ = [$$[$0 - 2], $$[$0]];\n          break;\n        case 62:\n          this.$ = $$[$0];\n          break;\n        case 63:\n          this.$ = yy.PLACEMENT.LEFTOF;\n          break;\n        case 64:\n          this.$ = yy.PLACEMENT.RIGHTOF;\n          break;\n        case 65:\n          this.$ = [\n            $$[$0 - 4],\n            $$[$0 - 1],\n            { type: \"addMessage\", from: $$[$0 - 4].actor, to: $$[$0 - 1].actor, signalType: $$[$0 - 3], msg: $$[$0], activate: true },\n            { type: \"activeStart\", signalType: yy.LINETYPE.ACTIVE_START, actor: $$[$0 - 1].actor }\n          ];\n          break;\n        case 66:\n          this.$ = [\n            $$[$0 - 4],\n            $$[$0 - 1],\n            { type: \"addMessage\", from: $$[$0 - 4].actor, to: $$[$0 - 1].actor, signalType: $$[$0 - 3], msg: $$[$0] },\n            { type: \"activeEnd\", signalType: yy.LINETYPE.ACTIVE_END, actor: $$[$0 - 4].actor }\n          ];\n          break;\n        case 67:\n          this.$ = [$$[$0 - 3], $$[$0 - 1], { type: \"addMessage\", from: $$[$0 - 3].actor, to: $$[$0 - 1].actor, signalType: $$[$0 - 2], msg: $$[$0] }];\n          break;\n        case 68:\n          this.$ = { type: \"addParticipant\", actor: $$[$0] };\n          break;\n        case 69:\n          this.$ = yy.LINETYPE.SOLID_OPEN;\n          break;\n        case 70:\n          this.$ = yy.LINETYPE.DOTTED_OPEN;\n          break;\n        case 71:\n          this.$ = yy.LINETYPE.SOLID;\n          break;\n        case 72:\n          this.$ = yy.LINETYPE.BIDIRECTIONAL_SOLID;\n          break;\n        case 73:\n          this.$ = yy.LINETYPE.DOTTED;\n          break;\n        case 74:\n          this.$ = yy.LINETYPE.BIDIRECTIONAL_DOTTED;\n          break;\n        case 75:\n          this.$ = yy.LINETYPE.SOLID_CROSS;\n          break;\n        case 76:\n          this.$ = yy.LINETYPE.DOTTED_CROSS;\n          break;\n        case 77:\n          this.$ = yy.LINETYPE.SOLID_POINT;\n          break;\n        case 78:\n          this.$ = yy.LINETYPE.DOTTED_POINT;\n          break;\n        case 79:\n          this.$ = yy.parseMessage($$[$0].trim().substring(1));\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: $V0, 5: $V1, 6: $V2 }, { 1: [3] }, { 3: 5, 4: $V0, 5: $V1, 6: $V2 }, { 3: 6, 4: $V0, 5: $V1, 6: $V2 }, o([1, 4, 5, 13, 14, 18, 21, 23, 29, 30, 31, 33, 35, 36, 37, 38, 39, 41, 43, 44, 46, 50, 52, 53, 54, 59, 60, 61, 62, 70], $V3, { 7: 7 }), { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 3], 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, o($Vx, [2, 5]), { 9: 47, 12: 12, 13: $V6, 14: $V7, 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, o($Vx, [2, 7]), o($Vx, [2, 8]), o($Vx, [2, 14]), { 12: 48, 50: $Vo, 52: $Vp, 53: $Vq }, { 15: [1, 49] }, { 5: [1, 50] }, { 5: [1, 53], 19: [1, 51], 20: [1, 52] }, { 22: 54, 70: $Vw }, { 22: 55, 70: $Vw }, { 5: [1, 56] }, { 5: [1, 57] }, { 5: [1, 58] }, { 5: [1, 59] }, { 5: [1, 60] }, o($Vx, [2, 29]), o($Vx, [2, 30]), { 32: [1, 61] }, { 34: [1, 62] }, o($Vx, [2, 33]), { 15: [1, 63] }, { 15: [1, 64] }, { 15: [1, 65] }, { 15: [1, 66] }, { 15: [1, 67] }, { 15: [1, 68] }, { 15: [1, 69] }, { 15: [1, 70] }, { 22: 71, 70: $Vw }, { 22: 72, 70: $Vw }, { 22: 73, 70: $Vw }, { 67: 74, 71: [1, 75], 72: [1, 76], 73: [1, 77], 74: [1, 78], 75: [1, 79], 76: [1, 80], 77: [1, 81], 78: [1, 82], 79: [1, 83], 80: [1, 84] }, { 55: 85, 57: [1, 86], 65: [1, 87], 66: [1, 88] }, { 22: 89, 70: $Vw }, { 22: 90, 70: $Vw }, { 22: 91, 70: $Vw }, { 22: 92, 70: $Vw }, o([5, 51, 64, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81], [2, 68]), o($Vx, [2, 6]), o($Vx, [2, 15]), o($Vy, [2, 9], { 10: 93 }), o($Vx, [2, 17]), { 5: [1, 95], 19: [1, 94] }, { 5: [1, 96] }, o($Vx, [2, 21]), { 5: [1, 97] }, { 5: [1, 98] }, o($Vx, [2, 24]), o($Vx, [2, 25]), o($Vx, [2, 26]), o($Vx, [2, 27]), o($Vx, [2, 28]), o($Vx, [2, 31]), o($Vx, [2, 32]), o($Vz, $V3, { 7: 99 }), o($Vz, $V3, { 7: 100 }), o($Vz, $V3, { 7: 101 }), o($VA, $V3, { 40: 102, 7: 103 }), o($VB, $V3, { 42: 104, 7: 105 }), o($VB, $V3, { 7: 105, 42: 106 }), o($VC, $V3, { 45: 107, 7: 108 }), o($Vz, $V3, { 7: 109 }), { 5: [1, 111], 51: [1, 110] }, { 5: [1, 113], 51: [1, 112] }, { 5: [1, 114] }, { 22: 117, 68: [1, 115], 69: [1, 116], 70: $Vw }, o($VD, [2, 69]), o($VD, [2, 70]), o($VD, [2, 71]), o($VD, [2, 72]), o($VD, [2, 73]), o($VD, [2, 74]), o($VD, [2, 75]), o($VD, [2, 76]), o($VD, [2, 77]), o($VD, [2, 78]), { 22: 118, 70: $Vw }, { 22: 120, 58: 119, 70: $Vw }, { 70: [2, 63] }, { 70: [2, 64] }, { 56: 121, 81: $VE }, { 56: 123, 81: $VE }, { 56: 124, 81: $VE }, { 56: 125, 81: $VE }, { 4: [1, 128], 5: [1, 130], 11: 127, 12: 129, 16: [1, 126], 50: $Vo, 52: $Vp, 53: $Vq }, { 5: [1, 131] }, o($Vx, [2, 19]), o($Vx, [2, 20]), o($Vx, [2, 22]), o($Vx, [2, 23]), { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [1, 132], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [1, 133], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [1, 134], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 16: [1, 135] }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [2, 46], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 49: [1, 136], 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 16: [1, 137] }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [2, 44], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 48: [1, 138], 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 16: [1, 139] }, { 16: [1, 140] }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [2, 42], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 47: [1, 141], 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 4: $V4, 5: $V5, 8: 8, 9: 10, 12: 12, 13: $V6, 14: $V7, 16: [1, 142], 17: 15, 18: $V8, 21: $V9, 22: 40, 23: $Va, 24: 19, 25: 20, 26: 21, 27: 22, 28: 23, 29: $Vb, 30: $Vc, 31: $Vd, 33: $Ve, 35: $Vf, 36: $Vg, 37: $Vh, 38: $Vi, 39: $Vj, 41: $Vk, 43: $Vl, 44: $Vm, 46: $Vn, 50: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 59: $Vs, 60: $Vt, 61: $Vu, 62: $Vv, 70: $Vw }, { 15: [1, 143] }, o($Vx, [2, 49]), { 15: [1, 144] }, o($Vx, [2, 51]), o($Vx, [2, 52]), { 22: 145, 70: $Vw }, { 22: 146, 70: $Vw }, { 56: 147, 81: $VE }, { 56: 148, 81: $VE }, { 56: 149, 81: $VE }, { 64: [1, 150], 81: [2, 62] }, { 5: [2, 55] }, { 5: [2, 79] }, { 5: [2, 56] }, { 5: [2, 57] }, { 5: [2, 58] }, o($Vx, [2, 16]), o($Vy, [2, 10]), { 12: 151, 50: $Vo, 52: $Vp, 53: $Vq }, o($Vy, [2, 12]), o($Vy, [2, 13]), o($Vx, [2, 18]), o($Vx, [2, 34]), o($Vx, [2, 35]), o($Vx, [2, 36]), o($Vx, [2, 37]), { 15: [1, 152] }, o($Vx, [2, 38]), { 15: [1, 153] }, o($Vx, [2, 39]), o($Vx, [2, 40]), { 15: [1, 154] }, o($Vx, [2, 41]), { 5: [1, 155] }, { 5: [1, 156] }, { 56: 157, 81: $VE }, { 56: 158, 81: $VE }, { 5: [2, 67] }, { 5: [2, 53] }, { 5: [2, 54] }, { 22: 159, 70: $Vw }, o($Vy, [2, 11]), o($VA, $V3, { 7: 103, 40: 160 }), o($VB, $V3, { 7: 105, 42: 161 }), o($VC, $V3, { 7: 108, 45: 162 }), o($Vx, [2, 48]), o($Vx, [2, 50]), { 5: [2, 65] }, { 5: [2, 66] }, { 81: [2, 61] }, { 16: [2, 47] }, { 16: [2, 45] }, { 16: [2, 43] }],\n    defaultActions: { 5: [2, 1], 6: [2, 2], 87: [2, 63], 88: [2, 64], 121: [2, 55], 122: [2, 79], 123: [2, 56], 124: [2, 57], 125: [2, 58], 147: [2, 67], 148: [2, 53], 149: [2, 54], 157: [2, 65], 158: [2, 66], 159: [2, 61], 160: [2, 47], 161: [2, 45], 162: [2, 43] },\n    parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 5;\n            break;\n          case 1:\n            break;\n          case 2:\n            break;\n          case 3:\n            break;\n          case 4:\n            break;\n          case 5:\n            break;\n          case 6:\n            return 19;\n            break;\n          case 7:\n            this.begin(\"LINE\");\n            return 14;\n            break;\n          case 8:\n            this.begin(\"ID\");\n            return 50;\n            break;\n          case 9:\n            this.begin(\"ID\");\n            return 52;\n            break;\n          case 10:\n            return 13;\n            break;\n          case 11:\n            this.begin(\"ID\");\n            return 53;\n            break;\n          case 12:\n            yy_.yytext = yy_.yytext.trim();\n            this.begin(\"ALIAS\");\n            return 70;\n            break;\n          case 13:\n            this.popState();\n            this.popState();\n            this.begin(\"LINE\");\n            return 51;\n            break;\n          case 14:\n            this.popState();\n            this.popState();\n            return 5;\n            break;\n          case 15:\n            this.begin(\"LINE\");\n            return 36;\n            break;\n          case 16:\n            this.begin(\"LINE\");\n            return 37;\n            break;\n          case 17:\n            this.begin(\"LINE\");\n            return 38;\n            break;\n          case 18:\n            this.begin(\"LINE\");\n            return 39;\n            break;\n          case 19:\n            this.begin(\"LINE\");\n            return 49;\n            break;\n          case 20:\n            this.begin(\"LINE\");\n            return 41;\n            break;\n          case 21:\n            this.begin(\"LINE\");\n            return 43;\n            break;\n          case 22:\n            this.begin(\"LINE\");\n            return 48;\n            break;\n          case 23:\n            this.begin(\"LINE\");\n            return 44;\n            break;\n          case 24:\n            this.begin(\"LINE\");\n            return 47;\n            break;\n          case 25:\n            this.begin(\"LINE\");\n            return 46;\n            break;\n          case 26:\n            this.popState();\n            return 15;\n            break;\n          case 27:\n            return 16;\n            break;\n          case 28:\n            return 65;\n            break;\n          case 29:\n            return 66;\n            break;\n          case 30:\n            return 59;\n            break;\n          case 31:\n            return 60;\n            break;\n          case 32:\n            return 61;\n            break;\n          case 33:\n            return 62;\n            break;\n          case 34:\n            return 57;\n            break;\n          case 35:\n            return 54;\n            break;\n          case 36:\n            this.begin(\"ID\");\n            return 21;\n            break;\n          case 37:\n            this.begin(\"ID\");\n            return 23;\n            break;\n          case 38:\n            return 29;\n            break;\n          case 39:\n            return 30;\n            break;\n          case 40:\n            this.begin(\"acc_title\");\n            return 31;\n            break;\n          case 41:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 42:\n            this.begin(\"acc_descr\");\n            return 33;\n            break;\n          case 43:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 44:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 45:\n            this.popState();\n            break;\n          case 46:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 47:\n            return 6;\n            break;\n          case 48:\n            return 18;\n            break;\n          case 49:\n            return 20;\n            break;\n          case 50:\n            return 64;\n            break;\n          case 51:\n            return 5;\n            break;\n          case 52:\n            yy_.yytext = yy_.yytext.trim();\n            return 70;\n            break;\n          case 53:\n            return 73;\n            break;\n          case 54:\n            return 74;\n            break;\n          case 55:\n            return 75;\n            break;\n          case 56:\n            return 76;\n            break;\n          case 57:\n            return 71;\n            break;\n          case 58:\n            return 72;\n            break;\n          case 59:\n            return 77;\n            break;\n          case 60:\n            return 78;\n            break;\n          case 61:\n            return 79;\n            break;\n          case 62:\n            return 80;\n            break;\n          case 63:\n            return 81;\n            break;\n          case 64:\n            return 68;\n            break;\n          case 65:\n            return 69;\n            break;\n          case 66:\n            return 5;\n            break;\n          case 67:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:((?!\\n)\\s)+)/i, /^(?:#[^\\n]*)/i, /^(?:%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[0-9]+(?=[ \\n]+))/i, /^(?:box\\b)/i, /^(?:participant\\b)/i, /^(?:actor\\b)/i, /^(?:create\\b)/i, /^(?:destroy\\b)/i, /^(?:[^\\<->\\->:\\n,;]+?([\\-]*[^\\<->\\->:\\n,;]+?)*?(?=((?!\\n)\\s)+as(?!\\n)\\s|[#\\n;]|$))/i, /^(?:as\\b)/i, /^(?:(?:))/i, /^(?:loop\\b)/i, /^(?:rect\\b)/i, /^(?:opt\\b)/i, /^(?:alt\\b)/i, /^(?:else\\b)/i, /^(?:par\\b)/i, /^(?:par_over\\b)/i, /^(?:and\\b)/i, /^(?:critical\\b)/i, /^(?:option\\b)/i, /^(?:break\\b)/i, /^(?:(?:[:]?(?:no)?wrap)?[^#\\n;]*)/i, /^(?:end\\b)/i, /^(?:left of\\b)/i, /^(?:right of\\b)/i, /^(?:links\\b)/i, /^(?:link\\b)/i, /^(?:properties\\b)/i, /^(?:details\\b)/i, /^(?:over\\b)/i, /^(?:note\\b)/i, /^(?:activate\\b)/i, /^(?:deactivate\\b)/i, /^(?:title\\s[^#\\n;]+)/i, /^(?:title:\\s[^#\\n;]+)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:sequenceDiagram\\b)/i, /^(?:autonumber\\b)/i, /^(?:off\\b)/i, /^(?:,)/i, /^(?:;)/i, /^(?:[^\\+\\<->\\->:\\n,;]+((?!(-x|--x|-\\)|--\\)))[\\-]*[^\\+\\<->\\->:\\n,;]+)*)/i, /^(?:->>)/i, /^(?:<<->>)/i, /^(?:-->>)/i, /^(?:<<-->>)/i, /^(?:->)/i, /^(?:-->)/i, /^(?:-[x])/i, /^(?:--[x])/i, /^(?:-[\\)])/i, /^(?:--[\\)])/i, /^(?::(?:(?:no)?wrap)?[^#\\n;]+)/i, /^(?:\\+)/i, /^(?:-)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [45, 46], \"inclusive\": false }, \"acc_descr\": { \"rules\": [43], \"inclusive\": false }, \"acc_title\": { \"rules\": [41], \"inclusive\": false }, \"ID\": { \"rules\": [2, 3, 12], \"inclusive\": false }, \"ALIAS\": { \"rules\": [2, 3, 13, 14], \"inclusive\": false }, \"LINE\": { \"rules\": [2, 3, 26], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 44, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar sequenceDiagram_default = parser;\n\n// src/diagrams/sequence/sequenceDb.ts\nvar LINETYPE = {\n  SOLID: 0,\n  DOTTED: 1,\n  NOTE: 2,\n  SOLID_CROSS: 3,\n  DOTTED_CROSS: 4,\n  SOLID_OPEN: 5,\n  DOTTED_OPEN: 6,\n  LOOP_START: 10,\n  LOOP_END: 11,\n  ALT_START: 12,\n  ALT_ELSE: 13,\n  ALT_END: 14,\n  OPT_START: 15,\n  OPT_END: 16,\n  ACTIVE_START: 17,\n  ACTIVE_END: 18,\n  PAR_START: 19,\n  PAR_AND: 20,\n  PAR_END: 21,\n  RECT_START: 22,\n  RECT_END: 23,\n  SOLID_POINT: 24,\n  DOTTED_POINT: 25,\n  AUTONUMBER: 26,\n  CRITICAL_START: 27,\n  CRITICAL_OPTION: 28,\n  CRITICAL_END: 29,\n  BREAK_START: 30,\n  BREAK_END: 31,\n  PAR_OVER_START: 32,\n  BIDIRECTIONAL_SOLID: 33,\n  BIDIRECTIONAL_DOTTED: 34\n};\nvar ARROWTYPE = {\n  FILLED: 0,\n  OPEN: 1\n};\nvar PLACEMENT = {\n  LEFTOF: 0,\n  RIGHTOF: 1,\n  OVER: 2\n};\nvar SequenceDB = class {\n  constructor() {\n    this.state = new _chunk_XZIHB7SX_mjs__WEBPACK_IMPORTED_MODULE_1__.ImperativeState(() => ({\n      prevActor: void 0,\n      actors: /* @__PURE__ */ new Map(),\n      createdActors: /* @__PURE__ */ new Map(),\n      destroyedActors: /* @__PURE__ */ new Map(),\n      boxes: [],\n      messages: [],\n      notes: [],\n      sequenceNumbersEnabled: false,\n      wrapEnabled: void 0,\n      currentBox: void 0,\n      lastCreated: void 0,\n      lastDestroyed: void 0\n    }));\n    this.setAccTitle = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.setAccTitle;\n    this.setAccDescription = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.setAccDescription;\n    this.setDiagramTitle = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.setDiagramTitle;\n    this.getAccTitle = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getAccTitle;\n    this.getAccDescription = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getAccDescription;\n    this.getDiagramTitle = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getDiagramTitle;\n    this.apply = this.apply.bind(this);\n    this.parseBoxData = this.parseBoxData.bind(this);\n    this.parseMessage = this.parseMessage.bind(this);\n    this.clear();\n    this.setWrap((0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)().wrap);\n    this.LINETYPE = LINETYPE;\n    this.ARROWTYPE = ARROWTYPE;\n    this.PLACEMENT = PLACEMENT;\n  }\n  static {\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(this, \"SequenceDB\");\n  }\n  addBox(data) {\n    this.state.records.boxes.push({\n      name: data.text,\n      wrap: data.wrap ?? this.autoWrap(),\n      fill: data.color,\n      actorKeys: []\n    });\n    this.state.records.currentBox = this.state.records.boxes.slice(-1)[0];\n  }\n  addActor(id, name, description, type) {\n    let assignedBox = this.state.records.currentBox;\n    const old = this.state.records.actors.get(id);\n    if (old) {\n      if (this.state.records.currentBox && old.box && this.state.records.currentBox !== old.box) {\n        throw new Error(\n          `A same participant should only be defined in one Box: ${old.name} can't be in '${old.box.name}' and in '${this.state.records.currentBox.name}' at the same time.`\n        );\n      }\n      assignedBox = old.box ? old.box : this.state.records.currentBox;\n      old.box = assignedBox;\n      if (old && name === old.name && description == null) {\n        return;\n      }\n    }\n    if (description?.text == null) {\n      description = { text: name, type };\n    }\n    if (type == null || description.text == null) {\n      description = { text: name, type };\n    }\n    this.state.records.actors.set(id, {\n      box: assignedBox,\n      name,\n      description: description.text,\n      wrap: description.wrap ?? this.autoWrap(),\n      prevActor: this.state.records.prevActor,\n      links: {},\n      properties: {},\n      actorCnt: null,\n      rectData: null,\n      type: type ?? \"participant\"\n    });\n    if (this.state.records.prevActor) {\n      const prevActorInRecords = this.state.records.actors.get(this.state.records.prevActor);\n      if (prevActorInRecords) {\n        prevActorInRecords.nextActor = id;\n      }\n    }\n    if (this.state.records.currentBox) {\n      this.state.records.currentBox.actorKeys.push(id);\n    }\n    this.state.records.prevActor = id;\n  }\n  activationCount(part) {\n    let i;\n    let count = 0;\n    if (!part) {\n      return 0;\n    }\n    for (i = 0; i < this.state.records.messages.length; i++) {\n      if (this.state.records.messages[i].type === this.LINETYPE.ACTIVE_START && this.state.records.messages[i].from === part) {\n        count++;\n      }\n      if (this.state.records.messages[i].type === this.LINETYPE.ACTIVE_END && this.state.records.messages[i].from === part) {\n        count--;\n      }\n    }\n    return count;\n  }\n  addMessage(idFrom, idTo, message, answer) {\n    this.state.records.messages.push({\n      id: this.state.records.messages.length.toString(),\n      from: idFrom,\n      to: idTo,\n      message: message.text,\n      wrap: message.wrap ?? this.autoWrap(),\n      answer\n    });\n  }\n  addSignal(idFrom, idTo, message, messageType, activate = false) {\n    if (messageType === this.LINETYPE.ACTIVE_END) {\n      const cnt = this.activationCount(idFrom ?? \"\");\n      if (cnt < 1) {\n        const error = new Error(\"Trying to inactivate an inactive participant (\" + idFrom + \")\");\n        error.hash = {\n          text: \"->>-\",\n          token: \"->>-\",\n          line: \"1\",\n          loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },\n          expected: [\"'ACTIVE_PARTICIPANT'\"]\n        };\n        throw error;\n      }\n    }\n    this.state.records.messages.push({\n      id: this.state.records.messages.length.toString(),\n      from: idFrom,\n      to: idTo,\n      message: message?.text ?? \"\",\n      wrap: message?.wrap ?? this.autoWrap(),\n      type: messageType,\n      activate\n    });\n    return true;\n  }\n  hasAtLeastOneBox() {\n    return this.state.records.boxes.length > 0;\n  }\n  hasAtLeastOneBoxWithTitle() {\n    return this.state.records.boxes.some((b) => b.name);\n  }\n  getMessages() {\n    return this.state.records.messages;\n  }\n  getBoxes() {\n    return this.state.records.boxes;\n  }\n  getActors() {\n    return this.state.records.actors;\n  }\n  getCreatedActors() {\n    return this.state.records.createdActors;\n  }\n  getDestroyedActors() {\n    return this.state.records.destroyedActors;\n  }\n  getActor(id) {\n    return this.state.records.actors.get(id);\n  }\n  getActorKeys() {\n    return [...this.state.records.actors.keys()];\n  }\n  enableSequenceNumbers() {\n    this.state.records.sequenceNumbersEnabled = true;\n  }\n  disableSequenceNumbers() {\n    this.state.records.sequenceNumbersEnabled = false;\n  }\n  showSequenceNumbers() {\n    return this.state.records.sequenceNumbersEnabled;\n  }\n  setWrap(wrapSetting) {\n    this.state.records.wrapEnabled = wrapSetting;\n  }\n  extractWrap(text) {\n    if (text === void 0) {\n      return {};\n    }\n    text = text.trim();\n    const wrap = /^:?wrap:/.exec(text) !== null ? true : /^:?nowrap:/.exec(text) !== null ? false : void 0;\n    const cleanedText = (wrap === void 0 ? text : text.replace(/^:?(?:no)?wrap:/, \"\")).trim();\n    return { cleanedText, wrap };\n  }\n  autoWrap() {\n    if (this.state.records.wrapEnabled !== void 0) {\n      return this.state.records.wrapEnabled;\n    }\n    return (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)().sequence?.wrap ?? false;\n  }\n  clear() {\n    this.state.reset();\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.clear)();\n  }\n  parseMessage(str) {\n    const trimmedStr = str.trim();\n    const { wrap, cleanedText } = this.extractWrap(trimmedStr);\n    const message = {\n      text: cleanedText,\n      wrap\n    };\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.debug(`parseMessage: ${JSON.stringify(message)}`);\n    return message;\n  }\n  // We expect the box statement to be color first then description\n  // The color can be rgb,rgba,hsl,hsla, or css code names  #hex codes are not supported for now because of the way the char # is handled\n  // We extract first segment as color, the rest of the line is considered as text\n  parseBoxData(str) {\n    const match = /^((?:rgba?|hsla?)\\s*\\(.*\\)|\\w*)(.*)$/.exec(str);\n    let color = match?.[1] ? match[1].trim() : \"transparent\";\n    let title = match?.[2] ? match[2].trim() : void 0;\n    if (window?.CSS) {\n      if (!window.CSS.supports(\"color\", color)) {\n        color = \"transparent\";\n        title = str.trim();\n      }\n    } else {\n      const style = new Option().style;\n      style.color = color;\n      if (style.color !== color) {\n        color = \"transparent\";\n        title = str.trim();\n      }\n    }\n    const { wrap, cleanedText } = this.extractWrap(title);\n    return {\n      text: cleanedText ? (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.sanitizeText)(cleanedText, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)()) : void 0,\n      color,\n      wrap\n    };\n  }\n  addNote(actor, placement, message) {\n    const note = {\n      actor,\n      placement,\n      message: message.text,\n      wrap: message.wrap ?? this.autoWrap()\n    };\n    const actors = [].concat(actor, actor);\n    this.state.records.notes.push(note);\n    this.state.records.messages.push({\n      id: this.state.records.messages.length.toString(),\n      from: actors[0],\n      to: actors[1],\n      message: message.text,\n      wrap: message.wrap ?? this.autoWrap(),\n      type: this.LINETYPE.NOTE,\n      placement\n    });\n  }\n  addLinks(actorId, text) {\n    const actor = this.getActor(actorId);\n    try {\n      let sanitizedText = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.sanitizeText)(text.text, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)());\n      sanitizedText = sanitizedText.replace(/&equals;/g, \"=\");\n      sanitizedText = sanitizedText.replace(/&amp;/g, \"&\");\n      const links = JSON.parse(sanitizedText);\n      this.insertLinks(actor, links);\n    } catch (e) {\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.error(\"error while parsing actor link text\", e);\n    }\n  }\n  addALink(actorId, text) {\n    const actor = this.getActor(actorId);\n    try {\n      const links = {};\n      let sanitizedText = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.sanitizeText)(text.text, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)());\n      const sep = sanitizedText.indexOf(\"@\");\n      sanitizedText = sanitizedText.replace(/&equals;/g, \"=\");\n      sanitizedText = sanitizedText.replace(/&amp;/g, \"&\");\n      const label = sanitizedText.slice(0, sep - 1).trim();\n      const link = sanitizedText.slice(sep + 1).trim();\n      links[label] = link;\n      this.insertLinks(actor, links);\n    } catch (e) {\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.error(\"error while parsing actor link text\", e);\n    }\n  }\n  insertLinks(actor, links) {\n    if (actor.links == null) {\n      actor.links = links;\n    } else {\n      for (const key in links) {\n        actor.links[key] = links[key];\n      }\n    }\n  }\n  addProperties(actorId, text) {\n    const actor = this.getActor(actorId);\n    try {\n      const sanitizedText = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.sanitizeText)(text.text, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)());\n      const properties = JSON.parse(sanitizedText);\n      this.insertProperties(actor, properties);\n    } catch (e) {\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.error(\"error while parsing actor properties text\", e);\n    }\n  }\n  insertProperties(actor, properties) {\n    if (actor.properties == null) {\n      actor.properties = properties;\n    } else {\n      for (const key in properties) {\n        actor.properties[key] = properties[key];\n      }\n    }\n  }\n  boxEnd() {\n    this.state.records.currentBox = void 0;\n  }\n  addDetails(actorId, text) {\n    const actor = this.getActor(actorId);\n    const elem = document.getElementById(text.text);\n    try {\n      const text2 = elem.innerHTML;\n      const details = JSON.parse(text2);\n      if (details.properties) {\n        this.insertProperties(actor, details.properties);\n      }\n      if (details.links) {\n        this.insertLinks(actor, details.links);\n      }\n    } catch (e) {\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.error(\"error while parsing actor details text\", e);\n    }\n  }\n  getActorProperty(actor, key) {\n    if (actor?.properties !== void 0) {\n      return actor.properties[key];\n    }\n    return void 0;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-redundant-type-constituents\n  apply(param) {\n    if (Array.isArray(param)) {\n      param.forEach((item) => {\n        this.apply(item);\n      });\n    } else {\n      switch (param.type) {\n        case \"sequenceIndex\":\n          this.state.records.messages.push({\n            id: this.state.records.messages.length.toString(),\n            from: void 0,\n            to: void 0,\n            message: {\n              start: param.sequenceIndex,\n              step: param.sequenceIndexStep,\n              visible: param.sequenceVisible\n            },\n            wrap: false,\n            type: param.signalType\n          });\n          break;\n        case \"addParticipant\":\n          this.addActor(param.actor, param.actor, param.description, param.draw);\n          break;\n        case \"createParticipant\":\n          if (this.state.records.actors.has(param.actor)) {\n            throw new Error(\n              \"It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior\"\n            );\n          }\n          this.state.records.lastCreated = param.actor;\n          this.addActor(param.actor, param.actor, param.description, param.draw);\n          this.state.records.createdActors.set(param.actor, this.state.records.messages.length);\n          break;\n        case \"destroyParticipant\":\n          this.state.records.lastDestroyed = param.actor;\n          this.state.records.destroyedActors.set(param.actor, this.state.records.messages.length);\n          break;\n        case \"activeStart\":\n          this.addSignal(param.actor, void 0, void 0, param.signalType);\n          break;\n        case \"activeEnd\":\n          this.addSignal(param.actor, void 0, void 0, param.signalType);\n          break;\n        case \"addNote\":\n          this.addNote(param.actor, param.placement, param.text);\n          break;\n        case \"addLinks\":\n          this.addLinks(param.actor, param.text);\n          break;\n        case \"addALink\":\n          this.addALink(param.actor, param.text);\n          break;\n        case \"addProperties\":\n          this.addProperties(param.actor, param.text);\n          break;\n        case \"addDetails\":\n          this.addDetails(param.actor, param.text);\n          break;\n        case \"addMessage\":\n          if (this.state.records.lastCreated) {\n            if (param.to !== this.state.records.lastCreated) {\n              throw new Error(\n                \"The created participant \" + this.state.records.lastCreated.name + \" does not have an associated creating message after its declaration. Please check the sequence diagram.\"\n              );\n            } else {\n              this.state.records.lastCreated = void 0;\n            }\n          } else if (this.state.records.lastDestroyed) {\n            if (param.to !== this.state.records.lastDestroyed && param.from !== this.state.records.lastDestroyed) {\n              throw new Error(\n                \"The destroyed participant \" + this.state.records.lastDestroyed.name + \" does not have an associated destroying message after its declaration. Please check the sequence diagram.\"\n              );\n            } else {\n              this.state.records.lastDestroyed = void 0;\n            }\n          }\n          this.addSignal(param.from, param.to, param.msg, param.signalType, param.activate);\n          break;\n        case \"boxStart\":\n          this.addBox(param.boxData);\n          break;\n        case \"boxEnd\":\n          this.boxEnd();\n          break;\n        case \"loopStart\":\n          this.addSignal(void 0, void 0, param.loopText, param.signalType);\n          break;\n        case \"loopEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"rectStart\":\n          this.addSignal(void 0, void 0, param.color, param.signalType);\n          break;\n        case \"rectEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"optStart\":\n          this.addSignal(void 0, void 0, param.optText, param.signalType);\n          break;\n        case \"optEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"altStart\":\n          this.addSignal(void 0, void 0, param.altText, param.signalType);\n          break;\n        case \"else\":\n          this.addSignal(void 0, void 0, param.altText, param.signalType);\n          break;\n        case \"altEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"setAccTitle\":\n          (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.setAccTitle)(param.text);\n          break;\n        case \"parStart\":\n          this.addSignal(void 0, void 0, param.parText, param.signalType);\n          break;\n        case \"and\":\n          this.addSignal(void 0, void 0, param.parText, param.signalType);\n          break;\n        case \"parEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"criticalStart\":\n          this.addSignal(void 0, void 0, param.criticalText, param.signalType);\n          break;\n        case \"option\":\n          this.addSignal(void 0, void 0, param.optionText, param.signalType);\n          break;\n        case \"criticalEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n        case \"breakStart\":\n          this.addSignal(void 0, void 0, param.breakText, param.signalType);\n          break;\n        case \"breakEnd\":\n          this.addSignal(void 0, void 0, void 0, param.signalType);\n          break;\n      }\n    }\n  }\n  getConfig() {\n    return (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)().sequence;\n  }\n};\n\n// src/diagrams/sequence/styles.js\nvar getStyles = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((options) => `.actor {\n    stroke: ${options.actorBorder};\n    fill: ${options.actorBkg};\n  }\n\n  text.actor > tspan {\n    fill: ${options.actorTextColor};\n    stroke: none;\n  }\n\n  .actor-line {\n    stroke: ${options.actorLineColor};\n  }\n\n  .messageLine0 {\n    stroke-width: 1.5;\n    stroke-dasharray: none;\n    stroke: ${options.signalColor};\n  }\n\n  .messageLine1 {\n    stroke-width: 1.5;\n    stroke-dasharray: 2, 2;\n    stroke: ${options.signalColor};\n  }\n\n  #arrowhead path {\n    fill: ${options.signalColor};\n    stroke: ${options.signalColor};\n  }\n\n  .sequenceNumber {\n    fill: ${options.sequenceNumberColor};\n  }\n\n  #sequencenumber {\n    fill: ${options.signalColor};\n  }\n\n  #crosshead path {\n    fill: ${options.signalColor};\n    stroke: ${options.signalColor};\n  }\n\n  .messageText {\n    fill: ${options.signalTextColor};\n    stroke: none;\n  }\n\n  .labelBox {\n    stroke: ${options.labelBoxBorderColor};\n    fill: ${options.labelBoxBkgColor};\n  }\n\n  .labelText, .labelText > tspan {\n    fill: ${options.labelTextColor};\n    stroke: none;\n  }\n\n  .loopText, .loopText > tspan {\n    fill: ${options.loopTextColor};\n    stroke: none;\n  }\n\n  .loopLine {\n    stroke-width: 2px;\n    stroke-dasharray: 2, 2;\n    stroke: ${options.labelBoxBorderColor};\n    fill: ${options.labelBoxBorderColor};\n  }\n\n  .note {\n    //stroke: #decc93;\n    stroke: ${options.noteBorderColor};\n    fill: ${options.noteBkgColor};\n  }\n\n  .noteText, .noteText > tspan {\n    fill: ${options.noteTextColor};\n    stroke: none;\n  }\n\n  .activation0 {\n    fill: ${options.activationBkgColor};\n    stroke: ${options.activationBorderColor};\n  }\n\n  .activation1 {\n    fill: ${options.activationBkgColor};\n    stroke: ${options.activationBorderColor};\n  }\n\n  .activation2 {\n    fill: ${options.activationBkgColor};\n    stroke: ${options.activationBorderColor};\n  }\n\n  .actorPopupMenu {\n    position: absolute;\n  }\n\n  .actorPopupMenuPanel {\n    position: absolute;\n    fill: ${options.actorBkg};\n    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);\n    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));\n}\n  .actor-man line {\n    stroke: ${options.actorBorder};\n    fill: ${options.actorBkg};\n  }\n  .actor-man circle, line {\n    stroke: ${options.actorBorder};\n    fill: ${options.actorBkg};\n    stroke-width: 2px;\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/sequence/sequenceRenderer.ts\n\n\n// src/diagrams/sequence/svgDraw.js\n\nvar ACTOR_TYPE_WIDTH = 18 * 2;\nvar TOP_ACTOR_CLASS = \"actor-top\";\nvar BOTTOM_ACTOR_CLASS = \"actor-bottom\";\nvar ACTOR_BOX_CLASS = \"actor-box\";\nvar ACTOR_MAN_FIGURE_CLASS = \"actor-man\";\nvar drawRect2 = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem, rectData) {\n  return (0,_chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__.drawRect)(elem, rectData);\n}, \"drawRect\");\nvar drawPopup = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem, actor, minMenuWidth, textAttrs, forceMenus) {\n  if (actor.links === void 0 || actor.links === null || Object.keys(actor.links).length === 0) {\n    return { height: 0, width: 0 };\n  }\n  const links = actor.links;\n  const actorCnt2 = actor.actorCnt;\n  const rectData = actor.rectData;\n  var displayValue = \"none\";\n  if (forceMenus) {\n    displayValue = \"block !important\";\n  }\n  const g = elem.append(\"g\");\n  g.attr(\"id\", \"actor\" + actorCnt2 + \"_popup\");\n  g.attr(\"class\", \"actorPopupMenu\");\n  g.attr(\"display\", displayValue);\n  var actorClass = \"\";\n  if (rectData.class !== void 0) {\n    actorClass = \" \" + rectData.class;\n  }\n  let menuWidth = rectData.width > minMenuWidth ? rectData.width : minMenuWidth;\n  const rectElem = g.append(\"rect\");\n  rectElem.attr(\"class\", \"actorPopupMenuPanel\" + actorClass);\n  rectElem.attr(\"x\", rectData.x);\n  rectElem.attr(\"y\", rectData.height);\n  rectElem.attr(\"fill\", rectData.fill);\n  rectElem.attr(\"stroke\", rectData.stroke);\n  rectElem.attr(\"width\", menuWidth);\n  rectElem.attr(\"height\", rectData.height);\n  rectElem.attr(\"rx\", rectData.rx);\n  rectElem.attr(\"ry\", rectData.ry);\n  if (links != null) {\n    var linkY = 20;\n    for (let key in links) {\n      var linkElem = g.append(\"a\");\n      var sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_5__.sanitizeUrl)(links[key]);\n      linkElem.attr(\"xlink:href\", sanitizedLink);\n      linkElem.attr(\"target\", \"_blank\");\n      _drawMenuItemTextCandidateFunc(textAttrs)(\n        key,\n        linkElem,\n        rectData.x + 10,\n        rectData.height + linkY,\n        menuWidth,\n        20,\n        { class: \"actor\" },\n        textAttrs\n      );\n      linkY += 30;\n    }\n  }\n  rectElem.attr(\"height\", linkY);\n  return { height: rectData.height + linkY, width: menuWidth };\n}, \"drawPopup\");\nvar popupMenuToggle = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(popId) {\n  return \"var pu = document.getElementById('\" + popId + \"'); if (pu != null) { pu.style.display = pu.style.display == 'block' ? 'none' : 'block'; }\";\n}, \"popupMenuToggle\");\nvar drawKatex = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(async function(elem, textData, msgModel = null) {\n  let textElem = elem.append(\"foreignObject\");\n  const lines = await (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.renderKatex)(textData.text, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig)());\n  const divElem = textElem.append(\"xhtml:div\").attr(\"style\", \"width: fit-content;\").attr(\"xmlns\", \"http://www.w3.org/1999/xhtml\").html(lines);\n  const dim = divElem.node().getBoundingClientRect();\n  textElem.attr(\"height\", Math.round(dim.height)).attr(\"width\", Math.round(dim.width));\n  if (textData.class === \"noteText\") {\n    const rectElem = elem.node().firstChild;\n    rectElem.setAttribute(\"height\", dim.height + 2 * textData.textMargin);\n    const rectDim = rectElem.getBBox();\n    textElem.attr(\"x\", Math.round(rectDim.x + rectDim.width / 2 - dim.width / 2)).attr(\"y\", Math.round(rectDim.y + rectDim.height / 2 - dim.height / 2));\n  } else if (msgModel) {\n    let { startx, stopx, starty } = msgModel;\n    if (startx > stopx) {\n      const temp = startx;\n      startx = stopx;\n      stopx = temp;\n    }\n    textElem.attr(\"x\", Math.round(startx + Math.abs(startx - stopx) / 2 - dim.width / 2));\n    if (textData.class === \"loopText\") {\n      textElem.attr(\"y\", Math.round(starty));\n    } else {\n      textElem.attr(\"y\", Math.round(starty - dim.height));\n    }\n  }\n  return [textElem];\n}, \"drawKatex\");\nvar drawText = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem, textData) {\n  let prevTextHeight = 0;\n  let textHeight = 0;\n  const lines = textData.text.split(_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.lineBreakRegex);\n  const [_textFontSize, _textFontSizePx] = (0,_chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.parseFontSize)(textData.fontSize);\n  let textElems = [];\n  let dy = 0;\n  let yfunc = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(() => textData.y, \"yfunc\");\n  if (textData.valign !== void 0 && textData.textMargin !== void 0 && textData.textMargin > 0) {\n    switch (textData.valign) {\n      case \"top\":\n      case \"start\":\n        yfunc = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(() => Math.round(textData.y + textData.textMargin), \"yfunc\");\n        break;\n      case \"middle\":\n      case \"center\":\n        yfunc = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(() => Math.round(textData.y + (prevTextHeight + textHeight + textData.textMargin) / 2), \"yfunc\");\n        break;\n      case \"bottom\":\n      case \"end\":\n        yfunc = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(() => Math.round(\n          textData.y + (prevTextHeight + textHeight + 2 * textData.textMargin) - textData.textMargin\n        ), \"yfunc\");\n        break;\n    }\n  }\n  if (textData.anchor !== void 0 && textData.textMargin !== void 0 && textData.width !== void 0) {\n    switch (textData.anchor) {\n      case \"left\":\n      case \"start\":\n        textData.x = Math.round(textData.x + textData.textMargin);\n        textData.anchor = \"start\";\n        textData.dominantBaseline = \"middle\";\n        textData.alignmentBaseline = \"middle\";\n        break;\n      case \"middle\":\n      case \"center\":\n        textData.x = Math.round(textData.x + textData.width / 2);\n        textData.anchor = \"middle\";\n        textData.dominantBaseline = \"middle\";\n        textData.alignmentBaseline = \"middle\";\n        break;\n      case \"right\":\n      case \"end\":\n        textData.x = Math.round(textData.x + textData.width - textData.textMargin);\n        textData.anchor = \"end\";\n        textData.dominantBaseline = \"middle\";\n        textData.alignmentBaseline = \"middle\";\n        break;\n    }\n  }\n  for (let [i, line] of lines.entries()) {\n    if (textData.textMargin !== void 0 && textData.textMargin === 0 && _textFontSize !== void 0) {\n      dy = i * _textFontSize;\n    }\n    const textElem = elem.append(\"text\");\n    textElem.attr(\"x\", textData.x);\n    textElem.attr(\"y\", yfunc());\n    if (textData.anchor !== void 0) {\n      textElem.attr(\"text-anchor\", textData.anchor).attr(\"dominant-baseline\", textData.dominantBaseline).attr(\"alignment-baseline\", textData.alignmentBaseline);\n    }\n    if (textData.fontFamily !== void 0) {\n      textElem.style(\"font-family\", textData.fontFamily);\n    }\n    if (_textFontSizePx !== void 0) {\n      textElem.style(\"font-size\", _textFontSizePx);\n    }\n    if (textData.fontWeight !== void 0) {\n      textElem.style(\"font-weight\", textData.fontWeight);\n    }\n    if (textData.fill !== void 0) {\n      textElem.attr(\"fill\", textData.fill);\n    }\n    if (textData.class !== void 0) {\n      textElem.attr(\"class\", textData.class);\n    }\n    if (textData.dy !== void 0) {\n      textElem.attr(\"dy\", textData.dy);\n    } else if (dy !== 0) {\n      textElem.attr(\"dy\", dy);\n    }\n    const text = line || _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.ZERO_WIDTH_SPACE;\n    if (textData.tspan) {\n      const span = textElem.append(\"tspan\");\n      span.attr(\"x\", textData.x);\n      if (textData.fill !== void 0) {\n        span.attr(\"fill\", textData.fill);\n      }\n      span.text(text);\n    } else {\n      textElem.text(text);\n    }\n    if (textData.valign !== void 0 && textData.textMargin !== void 0 && textData.textMargin > 0) {\n      textHeight += (textElem._groups || textElem)[0][0].getBBox().height;\n      prevTextHeight = textHeight;\n    }\n    textElems.push(textElem);\n  }\n  return textElems;\n}, \"drawText\");\nvar drawLabel = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem, txtObject) {\n  function genPoints(x, y, width, height, cut) {\n    return x + \",\" + y + \" \" + (x + width) + \",\" + y + \" \" + (x + width) + \",\" + (y + height - cut) + \" \" + (x + width - cut * 1.2) + \",\" + (y + height) + \" \" + x + \",\" + (y + height);\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(genPoints, \"genPoints\");\n  const polygon = elem.append(\"polygon\");\n  polygon.attr(\"points\", genPoints(txtObject.x, txtObject.y, txtObject.width, txtObject.height, 7));\n  polygon.attr(\"class\", \"labelBox\");\n  txtObject.y = txtObject.y + txtObject.height / 2;\n  drawText(elem, txtObject);\n  return polygon;\n}, \"drawLabel\");\nvar actorCnt = -1;\nvar fixLifeLineHeights = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((diagram2, actors, actorKeys, conf2) => {\n  if (!diagram2.select) {\n    return;\n  }\n  actorKeys.forEach((actorKey) => {\n    const actor = actors.get(actorKey);\n    const actorDOM = diagram2.select(\"#actor\" + actor.actorCnt);\n    if (!conf2.mirrorActors && actor.stopy) {\n      actorDOM.attr(\"y2\", actor.stopy + actor.height / 2);\n    } else if (conf2.mirrorActors) {\n      actorDOM.attr(\"y2\", actor.stopy);\n    }\n  });\n}, \"fixLifeLineHeights\");\nvar drawActorTypeParticipant = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem, actor, conf2, isFooter) {\n  const actorY = isFooter ? actor.stopy : actor.starty;\n  const center = actor.x + actor.width / 2;\n  const centerY = actorY + actor.height;\n  const boxplusLineGroup = elem.append(\"g\").lower();\n  var g = boxplusLineGroup;\n  if (!isFooter) {\n    actorCnt++;\n    if (Object.keys(actor.links || {}).length && !conf2.forceMenus) {\n      g.attr(\"onclick\", popupMenuToggle(`actor${actorCnt}_popup`)).attr(\"cursor\", \"pointer\");\n    }\n    g.append(\"line\").attr(\"id\", \"actor\" + actorCnt).attr(\"x1\", center).attr(\"y1\", centerY).attr(\"x2\", center).attr(\"y2\", 2e3).attr(\"class\", \"actor-line 200\").attr(\"stroke-width\", \"0.5px\").attr(\"stroke\", \"#999\").attr(\"name\", actor.name);\n    g = boxplusLineGroup.append(\"g\");\n    actor.actorCnt = actorCnt;\n    if (actor.links != null) {\n      g.attr(\"id\", \"root-\" + actorCnt);\n    }\n  }\n  const rect = (0,_chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__.getNoteRect)();\n  var cssclass = \"actor\";\n  if (actor.properties?.class) {\n    cssclass = actor.properties.class;\n  } else {\n    rect.fill = \"#eaeaea\";\n  }\n  if (isFooter) {\n    cssclass += ` ${BOTTOM_ACTOR_CLASS}`;\n  } else {\n    cssclass += ` ${TOP_ACTOR_CLASS}`;\n  }\n  rect.x = actor.x;\n  rect.y = actorY;\n  rect.width = actor.width;\n  rect.height = actor.height;\n  rect.class = cssclass;\n  rect.rx = 3;\n  rect.ry = 3;\n  rect.name = actor.name;\n  const rectElem = drawRect2(g, rect);\n  actor.rectData = rect;\n  if (actor.properties?.icon) {\n    const iconSrc = actor.properties.icon.trim();\n    if (iconSrc.charAt(0) === \"@\") {\n      (0,_chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__.drawEmbeddedImage)(g, rect.x + rect.width - 20, rect.y + 10, iconSrc.substr(1));\n    } else {\n      (0,_chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__.drawImage)(g, rect.x + rect.width - 20, rect.y + 10, iconSrc);\n    }\n  }\n  _drawTextCandidateFunc(conf2, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.hasKatex)(actor.description))(\n    actor.description,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: `actor ${ACTOR_BOX_CLASS}` },\n    conf2\n  );\n  let height = actor.height;\n  if (rectElem.node) {\n    const bounds2 = rectElem.node().getBBox();\n    actor.height = bounds2.height;\n    height = bounds2.height;\n  }\n  return height;\n}, \"drawActorTypeParticipant\");\nvar drawActorTypeActor = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem, actor, conf2, isFooter) {\n  const actorY = isFooter ? actor.stopy : actor.starty;\n  const center = actor.x + actor.width / 2;\n  const centerY = actorY + 80;\n  const line = elem.append(\"g\").lower();\n  if (!isFooter) {\n    actorCnt++;\n    line.append(\"line\").attr(\"id\", \"actor\" + actorCnt).attr(\"x1\", center).attr(\"y1\", centerY).attr(\"x2\", center).attr(\"y2\", 2e3).attr(\"class\", \"actor-line 200\").attr(\"stroke-width\", \"0.5px\").attr(\"stroke\", \"#999\").attr(\"name\", actor.name);\n    actor.actorCnt = actorCnt;\n  }\n  const actElem = elem.append(\"g\");\n  let cssClass = ACTOR_MAN_FIGURE_CLASS;\n  if (isFooter) {\n    cssClass += ` ${BOTTOM_ACTOR_CLASS}`;\n  } else {\n    cssClass += ` ${TOP_ACTOR_CLASS}`;\n  }\n  actElem.attr(\"class\", cssClass);\n  actElem.attr(\"name\", actor.name);\n  const rect = (0,_chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__.getNoteRect)();\n  rect.x = actor.x;\n  rect.y = actorY;\n  rect.fill = \"#eaeaea\";\n  rect.width = actor.width;\n  rect.height = actor.height;\n  rect.class = \"actor\";\n  rect.rx = 3;\n  rect.ry = 3;\n  actElem.append(\"line\").attr(\"id\", \"actor-man-torso\" + actorCnt).attr(\"x1\", center).attr(\"y1\", actorY + 25).attr(\"x2\", center).attr(\"y2\", actorY + 45);\n  actElem.append(\"line\").attr(\"id\", \"actor-man-arms\" + actorCnt).attr(\"x1\", center - ACTOR_TYPE_WIDTH / 2).attr(\"y1\", actorY + 33).attr(\"x2\", center + ACTOR_TYPE_WIDTH / 2).attr(\"y2\", actorY + 33);\n  actElem.append(\"line\").attr(\"x1\", center - ACTOR_TYPE_WIDTH / 2).attr(\"y1\", actorY + 60).attr(\"x2\", center).attr(\"y2\", actorY + 45);\n  actElem.append(\"line\").attr(\"x1\", center).attr(\"y1\", actorY + 45).attr(\"x2\", center + ACTOR_TYPE_WIDTH / 2 - 2).attr(\"y2\", actorY + 60);\n  const circle = actElem.append(\"circle\");\n  circle.attr(\"cx\", actor.x + actor.width / 2);\n  circle.attr(\"cy\", actorY + 10);\n  circle.attr(\"r\", 15);\n  circle.attr(\"width\", actor.width);\n  circle.attr(\"height\", actor.height);\n  const bounds2 = actElem.node().getBBox();\n  actor.height = bounds2.height;\n  _drawTextCandidateFunc(conf2, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.hasKatex)(actor.description))(\n    actor.description,\n    actElem,\n    rect.x,\n    rect.y + 35,\n    rect.width,\n    rect.height,\n    { class: `actor ${ACTOR_MAN_FIGURE_CLASS}` },\n    conf2\n  );\n  return actor.height;\n}, \"drawActorTypeActor\");\nvar drawActor = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(async function(elem, actor, conf2, isFooter) {\n  switch (actor.type) {\n    case \"actor\":\n      return await drawActorTypeActor(elem, actor, conf2, isFooter);\n    case \"participant\":\n      return await drawActorTypeParticipant(elem, actor, conf2, isFooter);\n  }\n}, \"drawActor\");\nvar drawBox = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem, box, conf2) {\n  const boxplusTextGroup = elem.append(\"g\");\n  const g = boxplusTextGroup;\n  drawBackgroundRect2(g, box);\n  if (box.name) {\n    _drawTextCandidateFunc(conf2)(\n      box.name,\n      g,\n      box.x,\n      box.y + (box.textMaxHeight || 0) / 2,\n      box.width,\n      0,\n      { class: \"text\" },\n      conf2\n    );\n  }\n  g.lower();\n}, \"drawBox\");\nvar anchorElement = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem) {\n  return elem.append(\"g\");\n}, \"anchorElement\");\nvar drawActivation = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem, bounds2, verticalPos, conf2, actorActivations2) {\n  const rect = (0,_chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__.getNoteRect)();\n  const g = bounds2.anchored;\n  rect.x = bounds2.startx;\n  rect.y = bounds2.starty;\n  rect.class = \"activation\" + actorActivations2 % 3;\n  rect.width = bounds2.stopx - bounds2.startx;\n  rect.height = verticalPos - bounds2.starty;\n  drawRect2(g, rect);\n}, \"drawActivation\");\nvar drawLoop = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(async function(elem, loopModel, labelText, conf2) {\n  const {\n    boxMargin,\n    boxTextMargin,\n    labelBoxHeight,\n    labelBoxWidth,\n    messageFontFamily: fontFamily,\n    messageFontSize: fontSize,\n    messageFontWeight: fontWeight\n  } = conf2;\n  const g = elem.append(\"g\");\n  const drawLoopLine = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(startx, starty, stopx, stopy) {\n    return g.append(\"line\").attr(\"x1\", startx).attr(\"y1\", starty).attr(\"x2\", stopx).attr(\"y2\", stopy).attr(\"class\", \"loopLine\");\n  }, \"drawLoopLine\");\n  drawLoopLine(loopModel.startx, loopModel.starty, loopModel.stopx, loopModel.starty);\n  drawLoopLine(loopModel.stopx, loopModel.starty, loopModel.stopx, loopModel.stopy);\n  drawLoopLine(loopModel.startx, loopModel.stopy, loopModel.stopx, loopModel.stopy);\n  drawLoopLine(loopModel.startx, loopModel.starty, loopModel.startx, loopModel.stopy);\n  if (loopModel.sections !== void 0) {\n    loopModel.sections.forEach(function(item) {\n      drawLoopLine(loopModel.startx, item.y, loopModel.stopx, item.y).style(\n        \"stroke-dasharray\",\n        \"3, 3\"\n      );\n    });\n  }\n  let txt = (0,_chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__.getTextObj)();\n  txt.text = labelText;\n  txt.x = loopModel.startx;\n  txt.y = loopModel.starty;\n  txt.fontFamily = fontFamily;\n  txt.fontSize = fontSize;\n  txt.fontWeight = fontWeight;\n  txt.anchor = \"middle\";\n  txt.valign = \"middle\";\n  txt.tspan = false;\n  txt.width = labelBoxWidth || 50;\n  txt.height = labelBoxHeight || 20;\n  txt.textMargin = boxTextMargin;\n  txt.class = \"labelText\";\n  drawLabel(g, txt);\n  txt = getTextObj2();\n  txt.text = loopModel.title;\n  txt.x = loopModel.startx + labelBoxWidth / 2 + (loopModel.stopx - loopModel.startx) / 2;\n  txt.y = loopModel.starty + boxMargin + boxTextMargin;\n  txt.anchor = \"middle\";\n  txt.valign = \"middle\";\n  txt.textMargin = boxTextMargin;\n  txt.class = \"loopText\";\n  txt.fontFamily = fontFamily;\n  txt.fontSize = fontSize;\n  txt.fontWeight = fontWeight;\n  txt.wrap = true;\n  let textElem = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.hasKatex)(txt.text) ? await drawKatex(g, txt, loopModel) : drawText(g, txt);\n  if (loopModel.sectionTitles !== void 0) {\n    for (const [idx, item] of Object.entries(loopModel.sectionTitles)) {\n      if (item.message) {\n        txt.text = item.message;\n        txt.x = loopModel.startx + (loopModel.stopx - loopModel.startx) / 2;\n        txt.y = loopModel.sections[idx].y + boxMargin + boxTextMargin;\n        txt.class = \"loopText\";\n        txt.anchor = \"middle\";\n        txt.valign = \"middle\";\n        txt.tspan = false;\n        txt.fontFamily = fontFamily;\n        txt.fontSize = fontSize;\n        txt.fontWeight = fontWeight;\n        txt.wrap = loopModel.wrap;\n        if ((0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.hasKatex)(txt.text)) {\n          loopModel.starty = loopModel.sections[idx].y;\n          await drawKatex(g, txt, loopModel);\n        } else {\n          drawText(g, txt);\n        }\n        let sectionHeight = Math.round(\n          textElem.map((te) => (te._groups || te)[0][0].getBBox().height).reduce((acc, curr) => acc + curr)\n        );\n        loopModel.sections[idx].height += sectionHeight - (boxMargin + boxTextMargin);\n      }\n    }\n  }\n  loopModel.height = Math.round(loopModel.stopy - loopModel.starty);\n  return g;\n}, \"drawLoop\");\nvar drawBackgroundRect2 = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem, bounds2) {\n  (0,_chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__.drawBackgroundRect)(elem, bounds2);\n}, \"drawBackgroundRect\");\nvar insertDatabaseIcon = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"database\").attr(\"fill-rule\", \"evenodd\").attr(\"clip-rule\", \"evenodd\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z\"\n  );\n}, \"insertDatabaseIcon\");\nvar insertComputerIcon = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"computer\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z\"\n  );\n}, \"insertComputerIcon\");\nvar insertClockIcon = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"clock\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z\"\n  );\n}, \"insertClockIcon\");\nvar insertArrowHead = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 7.9).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto-start-reverse\").append(\"path\").attr(\"d\", \"M -1 0 L 10 5 L 0 10 z\");\n}, \"insertArrowHead\");\nvar insertArrowFilledHead = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"filled-head\").attr(\"refX\", 15.5).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L14,7 L9,1 Z\");\n}, \"insertArrowFilledHead\");\nvar insertSequenceNumber = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"sequencenumber\").attr(\"refX\", 15).attr(\"refY\", 15).attr(\"markerWidth\", 60).attr(\"markerHeight\", 40).attr(\"orient\", \"auto\").append(\"circle\").attr(\"cx\", 15).attr(\"cy\", 15).attr(\"r\", 6);\n}, \"insertSequenceNumber\");\nvar insertArrowCrossHead = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(elem) {\n  const defs = elem.append(\"defs\");\n  const marker = defs.append(\"marker\").attr(\"id\", \"crosshead\").attr(\"markerWidth\", 15).attr(\"markerHeight\", 8).attr(\"orient\", \"auto\").attr(\"refX\", 4).attr(\"refY\", 4.5);\n  marker.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1pt\").attr(\"d\", \"M 1,2 L 6,7 M 6,2 L 1,7\");\n}, \"insertArrowCrossHead\");\nvar getTextObj2 = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n  return {\n    x: 0,\n    y: 0,\n    fill: void 0,\n    anchor: void 0,\n    style: \"#666\",\n    width: void 0,\n    height: void 0,\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true,\n    valign: void 0\n  };\n}, \"getTextObj\");\nvar getNoteRect2 = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n  return {\n    x: 0,\n    y: 0,\n    fill: \"#EDF2AE\",\n    stroke: \"#666\",\n    width: 100,\n    anchor: \"start\",\n    height: 100,\n    rx: 0,\n    ry: 0\n  };\n}, \"getNoteRect\");\nvar _drawTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2) {\n    const { actorFontSize, actorFontFamily, actorFontWeight } = conf2;\n    const [_actorFontSize, _actorFontSizePx] = (0,_chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.parseFontSize)(actorFontSize);\n    const lines = content.split(_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * _actorFontSize - _actorFontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).style(\"text-anchor\", \"middle\").style(\"font-size\", _actorFontSizePx).style(\"font-weight\", actorFontWeight).style(\"font-family\", actorFontFamily);\n      text.append(\"tspan\").attr(\"x\", x + width / 2).attr(\"dy\", dy).text(lines[i]);\n      text.attr(\"y\", y + height / 2).attr(\"dominant-baseline\", \"central\").attr(\"alignment-baseline\", \"central\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const s = g.append(\"switch\");\n    const f = s.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, s, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(byFo, \"byFo\");\n  async function byKatex(content, g, x, y, width, height, textAttrs, conf2) {\n    const dim = await (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.calculateMathMLDimensions)(content, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig)());\n    const s = g.append(\"switch\");\n    const f = s.append(\"foreignObject\").attr(\"x\", x + width / 2 - dim.width / 2).attr(\"y\", y + height / 2 - dim.height / 2).attr(\"width\", dim.width).attr(\"height\", dim.height);\n    const text = f.append(\"xhtml:div\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").html(await (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.renderKatex)(content, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig)()));\n    byTspan(content, s, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(byKatex, \"byKatex\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf2, hasKatex2 = false) {\n    if (hasKatex2) {\n      return byKatex;\n    }\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar _drawMenuItemTextCandidateFunc = /* @__PURE__ */ function() {\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g.append(\"text\").attr(\"x\", x).attr(\"y\", y).style(\"text-anchor\", \"start\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(byText, \"byText\");\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2) {\n    const { actorFontSize, actorFontFamily, actorFontWeight } = conf2;\n    const lines = content.split(_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * actorFontSize - actorFontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x).attr(\"y\", y).style(\"text-anchor\", \"start\").style(\"font-size\", actorFontSize).style(\"font-weight\", actorFontWeight).style(\"font-family\", actorFontFamily);\n      text.append(\"tspan\").attr(\"x\", x).attr(\"dy\", dy).text(lines[i]);\n      text.attr(\"y\", y + height / 2).attr(\"dominant-baseline\", \"central\").attr(\"alignment-baseline\", \"central\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(byTspan, \"byTspan\");\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const s = g.append(\"switch\");\n    const f = s.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, s, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(byFo, \"byFo\");\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(_setTextAttrs, \"_setTextAttrs\");\n  return function(conf2) {\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nvar svgDraw_default = {\n  drawRect: drawRect2,\n  drawText,\n  drawLabel,\n  drawActor,\n  drawBox,\n  drawPopup,\n  anchorElement,\n  drawActivation,\n  drawLoop,\n  drawBackgroundRect: drawBackgroundRect2,\n  insertArrowHead,\n  insertArrowFilledHead,\n  insertSequenceNumber,\n  insertArrowCrossHead,\n  insertDatabaseIcon,\n  insertComputerIcon,\n  insertClockIcon,\n  getTextObj: getTextObj2,\n  getNoteRect: getNoteRect2,\n  fixLifeLineHeights,\n  sanitizeUrl: _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_5__.sanitizeUrl\n};\n\n// src/diagrams/sequence/sequenceRenderer.ts\nvar conf = {};\nvar bounds = {\n  data: {\n    startx: void 0,\n    stopx: void 0,\n    starty: void 0,\n    stopy: void 0\n  },\n  verticalPos: 0,\n  sequenceItems: [],\n  activations: [],\n  models: {\n    getHeight: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n      return Math.max.apply(\n        null,\n        this.actors.length === 0 ? [0] : this.actors.map((actor) => actor.height || 0)\n      ) + (this.loops.length === 0 ? 0 : this.loops.map((it) => it.height || 0).reduce((acc, h) => acc + h)) + (this.messages.length === 0 ? 0 : this.messages.map((it) => it.height || 0).reduce((acc, h) => acc + h)) + (this.notes.length === 0 ? 0 : this.notes.map((it) => it.height || 0).reduce((acc, h) => acc + h));\n    }, \"getHeight\"),\n    clear: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n      this.actors = [];\n      this.boxes = [];\n      this.loops = [];\n      this.messages = [];\n      this.notes = [];\n    }, \"clear\"),\n    addBox: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(boxModel) {\n      this.boxes.push(boxModel);\n    }, \"addBox\"),\n    addActor: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(actorModel) {\n      this.actors.push(actorModel);\n    }, \"addActor\"),\n    addLoop: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(loopModel) {\n      this.loops.push(loopModel);\n    }, \"addLoop\"),\n    addMessage: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(msgModel) {\n      this.messages.push(msgModel);\n    }, \"addMessage\"),\n    addNote: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(noteModel) {\n      this.notes.push(noteModel);\n    }, \"addNote\"),\n    lastActor: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n      return this.actors[this.actors.length - 1];\n    }, \"lastActor\"),\n    lastLoop: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n      return this.loops[this.loops.length - 1];\n    }, \"lastLoop\"),\n    lastMessage: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n      return this.messages[this.messages.length - 1];\n    }, \"lastMessage\"),\n    lastNote: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n      return this.notes[this.notes.length - 1];\n    }, \"lastNote\"),\n    actors: [],\n    boxes: [],\n    loops: [],\n    messages: [],\n    notes: []\n  },\n  init: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n    this.sequenceItems = [];\n    this.activations = [];\n    this.models.clear();\n    this.data = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0\n    };\n    this.verticalPos = 0;\n    setConf((0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)());\n  }, \"init\"),\n  updateVal: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(obj, key, val, fun) {\n    if (obj[key] === void 0) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  }, \"updateVal\"),\n  updateBounds: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(startx, starty, stopx, stopy) {\n    const _self = this;\n    let cnt = 0;\n    function updateFn(type) {\n      return /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function updateItemBounds(item) {\n        cnt++;\n        const n = _self.sequenceItems.length - cnt + 1;\n        _self.updateVal(item, \"starty\", starty - n * conf.boxMargin, Math.min);\n        _self.updateVal(item, \"stopy\", stopy + n * conf.boxMargin, Math.max);\n        _self.updateVal(bounds.data, \"startx\", startx - n * conf.boxMargin, Math.min);\n        _self.updateVal(bounds.data, \"stopx\", stopx + n * conf.boxMargin, Math.max);\n        if (!(type === \"activation\")) {\n          _self.updateVal(item, \"startx\", startx - n * conf.boxMargin, Math.min);\n          _self.updateVal(item, \"stopx\", stopx + n * conf.boxMargin, Math.max);\n          _self.updateVal(bounds.data, \"starty\", starty - n * conf.boxMargin, Math.min);\n          _self.updateVal(bounds.data, \"stopy\", stopy + n * conf.boxMargin, Math.max);\n        }\n      }, \"updateItemBounds\");\n    }\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(updateFn, \"updateFn\");\n    this.sequenceItems.forEach(updateFn());\n    this.activations.forEach(updateFn(\"activation\"));\n  }, \"updateBounds\"),\n  insert: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(startx, starty, stopx, stopy) {\n    const _startx = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMin(startx, stopx);\n    const _stopx = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(startx, stopx);\n    const _starty = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMin(starty, stopy);\n    const _stopy = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(starty, stopy);\n    this.updateVal(bounds.data, \"startx\", _startx, Math.min);\n    this.updateVal(bounds.data, \"starty\", _starty, Math.min);\n    this.updateVal(bounds.data, \"stopx\", _stopx, Math.max);\n    this.updateVal(bounds.data, \"stopy\", _stopy, Math.max);\n    this.updateBounds(_startx, _starty, _stopx, _stopy);\n  }, \"insert\"),\n  newActivation: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(message, diagram2, actors) {\n    const actorRect = actors.get(message.from);\n    const stackedSize = actorActivations(message.from).length || 0;\n    const x = actorRect.x + actorRect.width / 2 + (stackedSize - 1) * conf.activationWidth / 2;\n    this.activations.push({\n      startx: x,\n      starty: this.verticalPos + 2,\n      stopx: x + conf.activationWidth,\n      stopy: void 0,\n      actor: message.from,\n      anchored: svgDraw_default.anchorElement(diagram2)\n    });\n  }, \"newActivation\"),\n  endActivation: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(message) {\n    const lastActorActivationIdx = this.activations.map(function(activation) {\n      return activation.actor;\n    }).lastIndexOf(message.from);\n    return this.activations.splice(lastActorActivationIdx, 1)[0];\n  }, \"endActivation\"),\n  createLoop: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(title = { message: void 0, wrap: false, width: void 0 }, fill) {\n    return {\n      startx: void 0,\n      starty: this.verticalPos,\n      stopx: void 0,\n      stopy: void 0,\n      title: title.message,\n      wrap: title.wrap,\n      width: title.width,\n      height: 0,\n      fill\n    };\n  }, \"createLoop\"),\n  newLoop: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(title = { message: void 0, wrap: false, width: void 0 }, fill) {\n    this.sequenceItems.push(this.createLoop(title, fill));\n  }, \"newLoop\"),\n  endLoop: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n    return this.sequenceItems.pop();\n  }, \"endLoop\"),\n  isLoopOverlap: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n    return this.sequenceItems.length ? this.sequenceItems[this.sequenceItems.length - 1].overlap : false;\n  }, \"isLoopOverlap\"),\n  addSectionToLoop: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(message) {\n    const loop = this.sequenceItems.pop();\n    loop.sections = loop.sections || [];\n    loop.sectionTitles = loop.sectionTitles || [];\n    loop.sections.push({ y: bounds.getVerticalPos(), height: 0 });\n    loop.sectionTitles.push(message);\n    this.sequenceItems.push(loop);\n  }, \"addSectionToLoop\"),\n  saveVerticalPos: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n    if (this.isLoopOverlap()) {\n      this.savedVerticalPos = this.verticalPos;\n    }\n  }, \"saveVerticalPos\"),\n  resetVerticalPos: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n    if (this.isLoopOverlap()) {\n      this.verticalPos = this.savedVerticalPos;\n    }\n  }, \"resetVerticalPos\"),\n  bumpVerticalPos: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(bump) {\n    this.verticalPos = this.verticalPos + bump;\n    this.data.stopy = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(this.data.stopy, this.verticalPos);\n  }, \"bumpVerticalPos\"),\n  getVerticalPos: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n    return this.verticalPos;\n  }, \"getVerticalPos\"),\n  getBounds: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function() {\n    return { bounds: this.data, models: this.models };\n  }, \"getBounds\")\n};\nvar drawNote = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(async function(elem, noteModel) {\n  bounds.bumpVerticalPos(conf.boxMargin);\n  noteModel.height = conf.boxMargin;\n  noteModel.starty = bounds.getVerticalPos();\n  const rect = (0,_chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__.getNoteRect)();\n  rect.x = noteModel.startx;\n  rect.y = noteModel.starty;\n  rect.width = noteModel.width || conf.width;\n  rect.class = \"note\";\n  const g = elem.append(\"g\");\n  const rectElem = svgDraw_default.drawRect(g, rect);\n  const textObj = (0,_chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__.getTextObj)();\n  textObj.x = noteModel.startx;\n  textObj.y = noteModel.starty;\n  textObj.width = rect.width;\n  textObj.dy = \"1em\";\n  textObj.text = noteModel.message;\n  textObj.class = \"noteText\";\n  textObj.fontFamily = conf.noteFontFamily;\n  textObj.fontSize = conf.noteFontSize;\n  textObj.fontWeight = conf.noteFontWeight;\n  textObj.anchor = conf.noteAlign;\n  textObj.textMargin = conf.noteMargin;\n  textObj.valign = \"center\";\n  const textElem = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.hasKatex)(textObj.text) ? await drawKatex(g, textObj) : drawText(g, textObj);\n  const textHeight = Math.round(\n    textElem.map((te) => (te._groups || te)[0][0].getBBox().height).reduce((acc, curr) => acc + curr)\n  );\n  rectElem.attr(\"height\", textHeight + 2 * conf.noteMargin);\n  noteModel.height += textHeight + 2 * conf.noteMargin;\n  bounds.bumpVerticalPos(textHeight + 2 * conf.noteMargin);\n  noteModel.stopy = noteModel.starty + textHeight + 2 * conf.noteMargin;\n  noteModel.stopx = noteModel.startx + rect.width;\n  bounds.insert(noteModel.startx, noteModel.starty, noteModel.stopx, noteModel.stopy);\n  bounds.models.addNote(noteModel);\n}, \"drawNote\");\nvar messageFont = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((cnf) => {\n  return {\n    fontFamily: cnf.messageFontFamily,\n    fontSize: cnf.messageFontSize,\n    fontWeight: cnf.messageFontWeight\n  };\n}, \"messageFont\");\nvar noteFont = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((cnf) => {\n  return {\n    fontFamily: cnf.noteFontFamily,\n    fontSize: cnf.noteFontSize,\n    fontWeight: cnf.noteFontWeight\n  };\n}, \"noteFont\");\nvar actorFont = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((cnf) => {\n  return {\n    fontFamily: cnf.actorFontFamily,\n    fontSize: cnf.actorFontSize,\n    fontWeight: cnf.actorFontWeight\n  };\n}, \"actorFont\");\nasync function boundMessage(_diagram, msgModel) {\n  bounds.bumpVerticalPos(10);\n  const { startx, stopx, message } = msgModel;\n  const lines = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.splitBreaks(message).length;\n  const isKatexMsg = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.hasKatex)(message);\n  const textDims = isKatexMsg ? await (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.calculateMathMLDimensions)(message, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)()) : _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.calculateTextDimensions(message, messageFont(conf));\n  if (!isKatexMsg) {\n    const lineHeight = textDims.height / lines;\n    msgModel.height += lineHeight;\n    bounds.bumpVerticalPos(lineHeight);\n  }\n  let lineStartY;\n  let totalOffset = textDims.height - 10;\n  const textWidth = textDims.width;\n  if (startx === stopx) {\n    lineStartY = bounds.getVerticalPos() + totalOffset;\n    if (!conf.rightAngles) {\n      totalOffset += conf.boxMargin;\n      lineStartY = bounds.getVerticalPos() + totalOffset;\n    }\n    totalOffset += 30;\n    const dx = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(textWidth / 2, conf.width / 2);\n    bounds.insert(\n      startx - dx,\n      bounds.getVerticalPos() - 10 + totalOffset,\n      stopx + dx,\n      bounds.getVerticalPos() + 30 + totalOffset\n    );\n  } else {\n    totalOffset += conf.boxMargin;\n    lineStartY = bounds.getVerticalPos() + totalOffset;\n    bounds.insert(startx, lineStartY - 10, stopx, lineStartY);\n  }\n  bounds.bumpVerticalPos(totalOffset);\n  msgModel.height += totalOffset;\n  msgModel.stopy = msgModel.starty + msgModel.height;\n  bounds.insert(msgModel.fromBounds, msgModel.starty, msgModel.toBounds, msgModel.stopy);\n  return lineStartY;\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(boundMessage, \"boundMessage\");\nvar drawMessage = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(async function(diagram2, msgModel, lineStartY, diagObj) {\n  const { startx, stopx, starty, message, type, sequenceIndex, sequenceVisible } = msgModel;\n  const textDims = _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.calculateTextDimensions(message, messageFont(conf));\n  const textObj = (0,_chunk_D6G4REZN_mjs__WEBPACK_IMPORTED_MODULE_0__.getTextObj)();\n  textObj.x = startx;\n  textObj.y = starty + 10;\n  textObj.width = stopx - startx;\n  textObj.class = \"messageText\";\n  textObj.dy = \"1em\";\n  textObj.text = message;\n  textObj.fontFamily = conf.messageFontFamily;\n  textObj.fontSize = conf.messageFontSize;\n  textObj.fontWeight = conf.messageFontWeight;\n  textObj.anchor = conf.messageAlign;\n  textObj.valign = \"center\";\n  textObj.textMargin = conf.wrapPadding;\n  textObj.tspan = false;\n  if ((0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.hasKatex)(textObj.text)) {\n    await drawKatex(diagram2, textObj, { startx, stopx, starty: lineStartY });\n  } else {\n    drawText(diagram2, textObj);\n  }\n  const textWidth = textDims.width;\n  let line;\n  if (startx === stopx) {\n    if (conf.rightAngles) {\n      line = diagram2.append(\"path\").attr(\n        \"d\",\n        `M  ${startx},${lineStartY} H ${startx + _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(conf.width / 2, textWidth / 2)} V ${lineStartY + 25} H ${startx}`\n      );\n    } else {\n      line = diagram2.append(\"path\").attr(\n        \"d\",\n        \"M \" + startx + \",\" + lineStartY + \" C \" + (startx + 60) + \",\" + (lineStartY - 10) + \" \" + (startx + 60) + \",\" + (lineStartY + 30) + \" \" + startx + \",\" + (lineStartY + 20)\n      );\n    }\n  } else {\n    line = diagram2.append(\"line\");\n    line.attr(\"x1\", startx);\n    line.attr(\"y1\", lineStartY);\n    line.attr(\"x2\", stopx);\n    line.attr(\"y2\", lineStartY);\n  }\n  if (type === diagObj.db.LINETYPE.DOTTED || type === diagObj.db.LINETYPE.DOTTED_CROSS || type === diagObj.db.LINETYPE.DOTTED_POINT || type === diagObj.db.LINETYPE.DOTTED_OPEN || type === diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED) {\n    line.style(\"stroke-dasharray\", \"3, 3\");\n    line.attr(\"class\", \"messageLine1\");\n  } else {\n    line.attr(\"class\", \"messageLine0\");\n  }\n  let url = \"\";\n  if (conf.arrowMarkerAbsolute) {\n    url = window.location.protocol + \"//\" + window.location.host + window.location.pathname + window.location.search;\n    url = url.replace(/\\(/g, \"\\\\(\");\n    url = url.replace(/\\)/g, \"\\\\)\");\n  }\n  line.attr(\"stroke-width\", 2);\n  line.attr(\"stroke\", \"none\");\n  line.style(\"fill\", \"none\");\n  if (type === diagObj.db.LINETYPE.SOLID || type === diagObj.db.LINETYPE.DOTTED) {\n    line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n  }\n  if (type === diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID || type === diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED) {\n    line.attr(\"marker-start\", \"url(\" + url + \"#arrowhead)\");\n    line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n  }\n  if (type === diagObj.db.LINETYPE.SOLID_POINT || type === diagObj.db.LINETYPE.DOTTED_POINT) {\n    line.attr(\"marker-end\", \"url(\" + url + \"#filled-head)\");\n  }\n  if (type === diagObj.db.LINETYPE.SOLID_CROSS || type === diagObj.db.LINETYPE.DOTTED_CROSS) {\n    line.attr(\"marker-end\", \"url(\" + url + \"#crosshead)\");\n  }\n  if (sequenceVisible || conf.showSequenceNumbers) {\n    line.attr(\"marker-start\", \"url(\" + url + \"#sequencenumber)\");\n    diagram2.append(\"text\").attr(\"x\", startx).attr(\"y\", lineStartY + 4).attr(\"font-family\", \"sans-serif\").attr(\"font-size\", \"12px\").attr(\"text-anchor\", \"middle\").attr(\"class\", \"sequenceNumber\").text(sequenceIndex);\n  }\n}, \"drawMessage\");\nvar addActorRenderingData = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(diagram2, actors, createdActors, actorKeys, verticalPos, messages, isFooter) {\n  let prevWidth = 0;\n  let prevMargin = 0;\n  let prevBox = void 0;\n  let maxHeight = 0;\n  for (const actorKey of actorKeys) {\n    const actor = actors.get(actorKey);\n    const box = actor.box;\n    if (prevBox && prevBox != box) {\n      if (!isFooter) {\n        bounds.models.addBox(prevBox);\n      }\n      prevMargin += conf.boxMargin + prevBox.margin;\n    }\n    if (box && box != prevBox) {\n      if (!isFooter) {\n        box.x = prevWidth + prevMargin;\n        box.y = verticalPos;\n      }\n      prevMargin += box.margin;\n    }\n    actor.width = actor.width || conf.width;\n    actor.height = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(actor.height || conf.height, conf.height);\n    actor.margin = actor.margin || conf.actorMargin;\n    maxHeight = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(maxHeight, actor.height);\n    if (createdActors.get(actor.name)) {\n      prevMargin += actor.width / 2;\n    }\n    actor.x = prevWidth + prevMargin;\n    actor.starty = bounds.getVerticalPos();\n    bounds.insert(actor.x, verticalPos, actor.x + actor.width, actor.height);\n    prevWidth += actor.width + prevMargin;\n    if (actor.box) {\n      actor.box.width = prevWidth + box.margin - actor.box.x;\n    }\n    prevMargin = actor.margin;\n    prevBox = actor.box;\n    bounds.models.addActor(actor);\n  }\n  if (prevBox && !isFooter) {\n    bounds.models.addBox(prevBox);\n  }\n  bounds.bumpVerticalPos(maxHeight);\n}, \"addActorRenderingData\");\nvar drawActors = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(async function(diagram2, actors, actorKeys, isFooter) {\n  if (!isFooter) {\n    for (const actorKey of actorKeys) {\n      const actor = actors.get(actorKey);\n      await svgDraw_default.drawActor(diagram2, actor, conf, false);\n    }\n  } else {\n    let maxHeight = 0;\n    bounds.bumpVerticalPos(conf.boxMargin * 2);\n    for (const actorKey of actorKeys) {\n      const actor = actors.get(actorKey);\n      if (!actor.stopy) {\n        actor.stopy = bounds.getVerticalPos();\n      }\n      const height = await svgDraw_default.drawActor(diagram2, actor, conf, true);\n      maxHeight = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(maxHeight, height);\n    }\n    bounds.bumpVerticalPos(maxHeight + conf.boxMargin);\n  }\n}, \"drawActors\");\nvar drawActorsPopup = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(diagram2, actors, actorKeys, doc) {\n  let maxHeight = 0;\n  let maxWidth = 0;\n  for (const actorKey of actorKeys) {\n    const actor = actors.get(actorKey);\n    const minMenuWidth = getRequiredPopupWidth(actor);\n    const menuDimensions = svgDraw_default.drawPopup(\n      diagram2,\n      actor,\n      minMenuWidth,\n      conf,\n      conf.forceMenus,\n      doc\n    );\n    if (menuDimensions.height > maxHeight) {\n      maxHeight = menuDimensions.height;\n    }\n    if (menuDimensions.width + actor.x > maxWidth) {\n      maxWidth = menuDimensions.width + actor.x;\n    }\n  }\n  return { maxHeight, maxWidth };\n}, \"drawActorsPopup\");\nvar setConf = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(cnf) {\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.assignWithDepth_default)(conf, cnf);\n  if (cnf.fontFamily) {\n    conf.actorFontFamily = conf.noteFontFamily = conf.messageFontFamily = cnf.fontFamily;\n  }\n  if (cnf.fontSize) {\n    conf.actorFontSize = conf.noteFontSize = conf.messageFontSize = cnf.fontSize;\n  }\n  if (cnf.fontWeight) {\n    conf.actorFontWeight = conf.noteFontWeight = conf.messageFontWeight = cnf.fontWeight;\n  }\n}, \"setConf\");\nvar actorActivations = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(actor) {\n  return bounds.activations.filter(function(activation) {\n    return activation.actor === actor;\n  });\n}, \"actorActivations\");\nvar activationBounds = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(actor, actors) {\n  const actorObj = actors.get(actor);\n  const activations = actorActivations(actor);\n  const left = activations.reduce(\n    function(acc, activation) {\n      return _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMin(acc, activation.startx);\n    },\n    actorObj.x + actorObj.width / 2 - 1\n  );\n  const right = activations.reduce(\n    function(acc, activation) {\n      return _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(acc, activation.stopx);\n    },\n    actorObj.x + actorObj.width / 2 + 1\n  );\n  return [left, right];\n}, \"activationBounds\");\nfunction adjustLoopHeightForWrap(loopWidths, msg, preMargin, postMargin, addLoopFn) {\n  bounds.bumpVerticalPos(preMargin);\n  let heightAdjust = postMargin;\n  if (msg.id && msg.message && loopWidths[msg.id]) {\n    const loopWidth = loopWidths[msg.id].width;\n    const textConf = messageFont(conf);\n    msg.message = _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.wrapLabel(`[${msg.message}]`, loopWidth - 2 * conf.wrapPadding, textConf);\n    msg.width = loopWidth;\n    msg.wrap = true;\n    const textDims = _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.calculateTextDimensions(msg.message, textConf);\n    const totalOffset = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(textDims.height, conf.labelBoxHeight);\n    heightAdjust = postMargin + totalOffset;\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.debug(`${totalOffset} - ${msg.message}`);\n  }\n  addLoopFn(msg);\n  bounds.bumpVerticalPos(heightAdjust);\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(adjustLoopHeightForWrap, \"adjustLoopHeightForWrap\");\nfunction adjustCreatedDestroyedData(msg, msgModel, lineStartY, index, actors, createdActors, destroyedActors) {\n  function receiverAdjustment(actor, adjustment) {\n    if (actor.x < actors.get(msg.from).x) {\n      bounds.insert(\n        msgModel.stopx - adjustment,\n        msgModel.starty,\n        msgModel.startx,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.stopx = msgModel.stopx + adjustment;\n    } else {\n      bounds.insert(\n        msgModel.startx,\n        msgModel.starty,\n        msgModel.stopx + adjustment,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.stopx = msgModel.stopx - adjustment;\n    }\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(receiverAdjustment, \"receiverAdjustment\");\n  function senderAdjustment(actor, adjustment) {\n    if (actor.x < actors.get(msg.to).x) {\n      bounds.insert(\n        msgModel.startx - adjustment,\n        msgModel.starty,\n        msgModel.stopx,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.startx = msgModel.startx + adjustment;\n    } else {\n      bounds.insert(\n        msgModel.stopx,\n        msgModel.starty,\n        msgModel.startx + adjustment,\n        msgModel.stopy + actor.height / 2 + conf.noteMargin\n      );\n      msgModel.startx = msgModel.startx - adjustment;\n    }\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(senderAdjustment, \"senderAdjustment\");\n  if (createdActors.get(msg.to) == index) {\n    const actor = actors.get(msg.to);\n    const adjustment = actor.type == \"actor\" ? ACTOR_TYPE_WIDTH / 2 + 3 : actor.width / 2 + 3;\n    receiverAdjustment(actor, adjustment);\n    actor.starty = lineStartY - actor.height / 2;\n    bounds.bumpVerticalPos(actor.height / 2);\n  } else if (destroyedActors.get(msg.from) == index) {\n    const actor = actors.get(msg.from);\n    if (conf.mirrorActors) {\n      const adjustment = actor.type == \"actor\" ? ACTOR_TYPE_WIDTH / 2 : actor.width / 2;\n      senderAdjustment(actor, adjustment);\n    }\n    actor.stopy = lineStartY - actor.height / 2;\n    bounds.bumpVerticalPos(actor.height / 2);\n  } else if (destroyedActors.get(msg.to) == index) {\n    const actor = actors.get(msg.to);\n    if (conf.mirrorActors) {\n      const adjustment = actor.type == \"actor\" ? ACTOR_TYPE_WIDTH / 2 + 3 : actor.width / 2 + 3;\n      receiverAdjustment(actor, adjustment);\n    }\n    actor.stopy = lineStartY - actor.height / 2;\n    bounds.bumpVerticalPos(actor.height / 2);\n  }\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(adjustCreatedDestroyedData, \"adjustCreatedDestroyedData\");\nvar draw = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(async function(_text, id, _version, diagObj) {\n  const { securityLevel, sequence } = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)();\n  conf = sequence;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_4__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_4__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_4__.select)(\"body\");\n  const doc = securityLevel === \"sandbox\" ? sandboxElement.nodes()[0].contentDocument : document;\n  bounds.init();\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.debug(diagObj.db);\n  const diagram2 = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : (0,d3__WEBPACK_IMPORTED_MODULE_4__.select)(`[id=\"${id}\"]`);\n  const actors = diagObj.db.getActors();\n  const createdActors = diagObj.db.getCreatedActors();\n  const destroyedActors = diagObj.db.getDestroyedActors();\n  const boxes = diagObj.db.getBoxes();\n  let actorKeys = diagObj.db.getActorKeys();\n  const messages = diagObj.db.getMessages();\n  const title = diagObj.db.getDiagramTitle();\n  const hasBoxes = diagObj.db.hasAtLeastOneBox();\n  const hasBoxTitles = diagObj.db.hasAtLeastOneBoxWithTitle();\n  const maxMessageWidthPerActor = await getMaxMessageWidthPerActor(actors, messages, diagObj);\n  conf.height = await calculateActorMargins(actors, maxMessageWidthPerActor, boxes);\n  svgDraw_default.insertComputerIcon(diagram2);\n  svgDraw_default.insertDatabaseIcon(diagram2);\n  svgDraw_default.insertClockIcon(diagram2);\n  if (hasBoxes) {\n    bounds.bumpVerticalPos(conf.boxMargin);\n    if (hasBoxTitles) {\n      bounds.bumpVerticalPos(boxes[0].textMaxHeight);\n    }\n  }\n  if (conf.hideUnusedParticipants === true) {\n    const newActors = /* @__PURE__ */ new Set();\n    messages.forEach((message) => {\n      newActors.add(message.from);\n      newActors.add(message.to);\n    });\n    actorKeys = actorKeys.filter((actorKey) => newActors.has(actorKey));\n  }\n  addActorRenderingData(diagram2, actors, createdActors, actorKeys, 0, messages, false);\n  const loopWidths = await calculateLoopBounds(messages, actors, maxMessageWidthPerActor, diagObj);\n  svgDraw_default.insertArrowHead(diagram2);\n  svgDraw_default.insertArrowCrossHead(diagram2);\n  svgDraw_default.insertArrowFilledHead(diagram2);\n  svgDraw_default.insertSequenceNumber(diagram2);\n  function activeEnd(msg, verticalPos) {\n    const activationData = bounds.endActivation(msg);\n    if (activationData.starty + 18 > verticalPos) {\n      activationData.starty = verticalPos - 6;\n      verticalPos += 12;\n    }\n    svgDraw_default.drawActivation(\n      diagram2,\n      activationData,\n      verticalPos,\n      conf,\n      actorActivations(msg.from).length\n    );\n    bounds.insert(activationData.startx, verticalPos - 10, activationData.stopx, verticalPos);\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(activeEnd, \"activeEnd\");\n  let sequenceIndex = 1;\n  let sequenceIndexStep = 1;\n  const messagesToDraw = [];\n  const backgrounds = [];\n  let index = 0;\n  for (const msg of messages) {\n    let loopModel, noteModel, msgModel;\n    switch (msg.type) {\n      case diagObj.db.LINETYPE.NOTE:\n        bounds.resetVerticalPos();\n        noteModel = msg.noteModel;\n        await drawNote(diagram2, noteModel);\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_START:\n        bounds.newActivation(msg, diagram2, actors);\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_END:\n        activeEnd(msg, bounds.getVerticalPos());\n        break;\n      case diagObj.db.LINETYPE.LOOP_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.LOOP_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"loop\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.RECT_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin,\n          (message) => bounds.newLoop(void 0, message.message)\n        );\n        break;\n      case diagObj.db.LINETYPE.RECT_END:\n        loopModel = bounds.endLoop();\n        backgrounds.push(loopModel);\n        bounds.models.addLoop(loopModel);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        break;\n      case diagObj.db.LINETYPE.OPT_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.OPT_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"opt\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.ALT_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.ALT_ELSE:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin + conf.boxTextMargin,\n          conf.boxMargin,\n          (message) => bounds.addSectionToLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.ALT_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"alt\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.PAR_START:\n      case diagObj.db.LINETYPE.PAR_OVER_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        bounds.saveVerticalPos();\n        break;\n      case diagObj.db.LINETYPE.PAR_AND:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin + conf.boxTextMargin,\n          conf.boxMargin,\n          (message) => bounds.addSectionToLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.PAR_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"par\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.AUTONUMBER:\n        sequenceIndex = msg.message.start || sequenceIndex;\n        sequenceIndexStep = msg.message.step || sequenceIndexStep;\n        if (msg.message.visible) {\n          diagObj.db.enableSequenceNumbers();\n        } else {\n          diagObj.db.disableSequenceNumbers();\n        }\n        break;\n      case diagObj.db.LINETYPE.CRITICAL_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.CRITICAL_OPTION:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin + conf.boxTextMargin,\n          conf.boxMargin,\n          (message) => bounds.addSectionToLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.CRITICAL_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"critical\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      case diagObj.db.LINETYPE.BREAK_START:\n        adjustLoopHeightForWrap(\n          loopWidths,\n          msg,\n          conf.boxMargin,\n          conf.boxMargin + conf.boxTextMargin,\n          (message) => bounds.newLoop(message)\n        );\n        break;\n      case diagObj.db.LINETYPE.BREAK_END:\n        loopModel = bounds.endLoop();\n        await svgDraw_default.drawLoop(diagram2, loopModel, \"break\", conf);\n        bounds.bumpVerticalPos(loopModel.stopy - bounds.getVerticalPos());\n        bounds.models.addLoop(loopModel);\n        break;\n      default:\n        try {\n          msgModel = msg.msgModel;\n          msgModel.starty = bounds.getVerticalPos();\n          msgModel.sequenceIndex = sequenceIndex;\n          msgModel.sequenceVisible = diagObj.db.showSequenceNumbers();\n          const lineStartY = await boundMessage(diagram2, msgModel);\n          adjustCreatedDestroyedData(\n            msg,\n            msgModel,\n            lineStartY,\n            index,\n            actors,\n            createdActors,\n            destroyedActors\n          );\n          messagesToDraw.push({ messageModel: msgModel, lineStartY });\n          bounds.models.addMessage(msgModel);\n        } catch (e) {\n          _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.error(\"error while drawing message\", e);\n        }\n    }\n    if ([\n      diagObj.db.LINETYPE.SOLID_OPEN,\n      diagObj.db.LINETYPE.DOTTED_OPEN,\n      diagObj.db.LINETYPE.SOLID,\n      diagObj.db.LINETYPE.DOTTED,\n      diagObj.db.LINETYPE.SOLID_CROSS,\n      diagObj.db.LINETYPE.DOTTED_CROSS,\n      diagObj.db.LINETYPE.SOLID_POINT,\n      diagObj.db.LINETYPE.DOTTED_POINT,\n      diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID,\n      diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED\n    ].includes(msg.type)) {\n      sequenceIndex = sequenceIndex + sequenceIndexStep;\n    }\n    index++;\n  }\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.debug(\"createdActors\", createdActors);\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.debug(\"destroyedActors\", destroyedActors);\n  await drawActors(diagram2, actors, actorKeys, false);\n  for (const e of messagesToDraw) {\n    await drawMessage(diagram2, e.messageModel, e.lineStartY, diagObj);\n  }\n  if (conf.mirrorActors) {\n    await drawActors(diagram2, actors, actorKeys, true);\n  }\n  backgrounds.forEach((e) => svgDraw_default.drawBackgroundRect(diagram2, e));\n  fixLifeLineHeights(diagram2, actors, actorKeys, conf);\n  for (const box2 of bounds.models.boxes) {\n    box2.height = bounds.getVerticalPos() - box2.y;\n    bounds.insert(box2.x, box2.y, box2.x + box2.width, box2.height);\n    box2.startx = box2.x;\n    box2.starty = box2.y;\n    box2.stopx = box2.startx + box2.width;\n    box2.stopy = box2.starty + box2.height;\n    box2.stroke = \"rgb(0,0,0, 0.5)\";\n    svgDraw_default.drawBox(diagram2, box2, conf);\n  }\n  if (hasBoxes) {\n    bounds.bumpVerticalPos(conf.boxMargin);\n  }\n  const requiredBoxSize = drawActorsPopup(diagram2, actors, actorKeys, doc);\n  const { bounds: box } = bounds.getBounds();\n  if (box.startx === void 0) {\n    box.startx = 0;\n  }\n  if (box.starty === void 0) {\n    box.starty = 0;\n  }\n  if (box.stopx === void 0) {\n    box.stopx = 0;\n  }\n  if (box.stopy === void 0) {\n    box.stopy = 0;\n  }\n  let boxHeight = box.stopy - box.starty;\n  if (boxHeight < requiredBoxSize.maxHeight) {\n    boxHeight = requiredBoxSize.maxHeight;\n  }\n  let height = boxHeight + 2 * conf.diagramMarginY;\n  if (conf.mirrorActors) {\n    height = height - conf.boxMargin + conf.bottomMarginAdj;\n  }\n  let boxWidth = box.stopx - box.startx;\n  if (boxWidth < requiredBoxSize.maxWidth) {\n    boxWidth = requiredBoxSize.maxWidth;\n  }\n  const width = boxWidth + 2 * conf.diagramMarginX;\n  if (title) {\n    diagram2.append(\"text\").text(title).attr(\"x\", (box.stopx - box.startx) / 2 - 2 * conf.diagramMarginX).attr(\"y\", -25);\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.configureSvgSize)(diagram2, height, width, conf.useMaxWidth);\n  const extraVertForTitle = title ? 40 : 0;\n  diagram2.attr(\n    \"viewBox\",\n    box.startx - conf.diagramMarginX + \" -\" + (conf.diagramMarginY + extraVertForTitle) + \" \" + width + \" \" + (height + extraVertForTitle)\n  );\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.debug(`models:`, bounds.models);\n}, \"draw\");\nasync function getMaxMessageWidthPerActor(actors, messages, diagObj) {\n  const maxMessageWidthPerActor = {};\n  for (const msg of messages) {\n    if (actors.get(msg.to) && actors.get(msg.from)) {\n      const actor = actors.get(msg.to);\n      if (msg.placement === diagObj.db.PLACEMENT.LEFTOF && !actor.prevActor) {\n        continue;\n      }\n      if (msg.placement === diagObj.db.PLACEMENT.RIGHTOF && !actor.nextActor) {\n        continue;\n      }\n      const isNote = msg.placement !== void 0;\n      const isMessage = !isNote;\n      const textFont = isNote ? noteFont(conf) : messageFont(conf);\n      const wrappedMessage = msg.wrap ? _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.wrapLabel(msg.message, conf.width - 2 * conf.wrapPadding, textFont) : msg.message;\n      const messageDimensions = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.hasKatex)(wrappedMessage) ? await (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.calculateMathMLDimensions)(msg.message, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)()) : _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.calculateTextDimensions(wrappedMessage, textFont);\n      const messageWidth = messageDimensions.width + 2 * conf.wrapPadding;\n      if (isMessage && msg.from === actor.nextActor) {\n        maxMessageWidthPerActor[msg.to] = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(\n          maxMessageWidthPerActor[msg.to] || 0,\n          messageWidth\n        );\n      } else if (isMessage && msg.from === actor.prevActor) {\n        maxMessageWidthPerActor[msg.from] = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(\n          maxMessageWidthPerActor[msg.from] || 0,\n          messageWidth\n        );\n      } else if (isMessage && msg.from === msg.to) {\n        maxMessageWidthPerActor[msg.from] = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(\n          maxMessageWidthPerActor[msg.from] || 0,\n          messageWidth / 2\n        );\n        maxMessageWidthPerActor[msg.to] = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(\n          maxMessageWidthPerActor[msg.to] || 0,\n          messageWidth / 2\n        );\n      } else if (msg.placement === diagObj.db.PLACEMENT.RIGHTOF) {\n        maxMessageWidthPerActor[msg.from] = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(\n          maxMessageWidthPerActor[msg.from] || 0,\n          messageWidth\n        );\n      } else if (msg.placement === diagObj.db.PLACEMENT.LEFTOF) {\n        maxMessageWidthPerActor[actor.prevActor] = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(\n          maxMessageWidthPerActor[actor.prevActor] || 0,\n          messageWidth\n        );\n      } else if (msg.placement === diagObj.db.PLACEMENT.OVER) {\n        if (actor.prevActor) {\n          maxMessageWidthPerActor[actor.prevActor] = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(\n            maxMessageWidthPerActor[actor.prevActor] || 0,\n            messageWidth / 2\n          );\n        }\n        if (actor.nextActor) {\n          maxMessageWidthPerActor[msg.from] = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(\n            maxMessageWidthPerActor[msg.from] || 0,\n            messageWidth / 2\n          );\n        }\n      }\n    }\n  }\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.debug(\"maxMessageWidthPerActor:\", maxMessageWidthPerActor);\n  return maxMessageWidthPerActor;\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(getMaxMessageWidthPerActor, \"getMaxMessageWidthPerActor\");\nvar getRequiredPopupWidth = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(actor) {\n  let requiredPopupWidth = 0;\n  const textFont = actorFont(conf);\n  for (const key in actor.links) {\n    const labelDimensions = _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.calculateTextDimensions(key, textFont);\n    const labelWidth = labelDimensions.width + 2 * conf.wrapPadding + 2 * conf.boxMargin;\n    if (requiredPopupWidth < labelWidth) {\n      requiredPopupWidth = labelWidth;\n    }\n  }\n  return requiredPopupWidth;\n}, \"getRequiredPopupWidth\");\nasync function calculateActorMargins(actors, actorToMessageWidth, boxes) {\n  let maxHeight = 0;\n  for (const prop of actors.keys()) {\n    const actor = actors.get(prop);\n    if (actor.wrap) {\n      actor.description = _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.wrapLabel(\n        actor.description,\n        conf.width - 2 * conf.wrapPadding,\n        actorFont(conf)\n      );\n    }\n    const actDims = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.hasKatex)(actor.description) ? await (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.calculateMathMLDimensions)(actor.description, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)()) : _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.calculateTextDimensions(actor.description, actorFont(conf));\n    actor.width = actor.wrap ? conf.width : _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(conf.width, actDims.width + 2 * conf.wrapPadding);\n    actor.height = actor.wrap ? _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(actDims.height, conf.height) : conf.height;\n    maxHeight = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(maxHeight, actor.height);\n  }\n  for (const actorKey in actorToMessageWidth) {\n    const actor = actors.get(actorKey);\n    if (!actor) {\n      continue;\n    }\n    const nextActor = actors.get(actor.nextActor);\n    if (!nextActor) {\n      const messageWidth2 = actorToMessageWidth[actorKey];\n      const actorWidth2 = messageWidth2 + conf.actorMargin - actor.width / 2;\n      actor.margin = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(actorWidth2, conf.actorMargin);\n      continue;\n    }\n    const messageWidth = actorToMessageWidth[actorKey];\n    const actorWidth = messageWidth + conf.actorMargin - actor.width / 2 - nextActor.width / 2;\n    actor.margin = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(actorWidth, conf.actorMargin);\n  }\n  let maxBoxHeight = 0;\n  boxes.forEach((box) => {\n    const textFont = messageFont(conf);\n    let totalWidth = box.actorKeys.reduce((total, aKey) => {\n      return total += actors.get(aKey).width + (actors.get(aKey).margin || 0);\n    }, 0);\n    totalWidth -= 2 * conf.boxTextMargin;\n    if (box.wrap) {\n      box.name = _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.wrapLabel(box.name, totalWidth - 2 * conf.wrapPadding, textFont);\n    }\n    const boxMsgDimensions = _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.calculateTextDimensions(box.name, textFont);\n    maxBoxHeight = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(boxMsgDimensions.height, maxBoxHeight);\n    const minWidth = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(totalWidth, boxMsgDimensions.width + 2 * conf.wrapPadding);\n    box.margin = conf.boxTextMargin;\n    if (totalWidth < minWidth) {\n      const missing = (minWidth - totalWidth) / 2;\n      box.margin += missing;\n    }\n  });\n  boxes.forEach((box) => box.textMaxHeight = maxBoxHeight);\n  return _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(maxHeight, conf.height);\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(calculateActorMargins, \"calculateActorMargins\");\nvar buildNoteModel = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(async function(msg, actors, diagObj) {\n  const fromActor = actors.get(msg.from);\n  const toActor = actors.get(msg.to);\n  const startx = fromActor.x;\n  const stopx = toActor.x;\n  const shouldWrap = msg.wrap && msg.message;\n  let textDimensions = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.hasKatex)(msg.message) ? await (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.calculateMathMLDimensions)(msg.message, (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.getConfig2)()) : _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.calculateTextDimensions(\n    shouldWrap ? _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.wrapLabel(msg.message, conf.width, noteFont(conf)) : msg.message,\n    noteFont(conf)\n  );\n  const noteModel = {\n    width: shouldWrap ? conf.width : _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(conf.width, textDimensions.width + 2 * conf.noteMargin),\n    height: 0,\n    startx: fromActor.x,\n    stopx: 0,\n    starty: 0,\n    stopy: 0,\n    message: msg.message\n  };\n  if (msg.placement === diagObj.db.PLACEMENT.RIGHTOF) {\n    noteModel.width = shouldWrap ? _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(conf.width, textDimensions.width) : _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(\n      fromActor.width / 2 + toActor.width / 2,\n      textDimensions.width + 2 * conf.noteMargin\n    );\n    noteModel.startx = startx + (fromActor.width + conf.actorMargin) / 2;\n  } else if (msg.placement === diagObj.db.PLACEMENT.LEFTOF) {\n    noteModel.width = shouldWrap ? _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(conf.width, textDimensions.width + 2 * conf.noteMargin) : _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(\n      fromActor.width / 2 + toActor.width / 2,\n      textDimensions.width + 2 * conf.noteMargin\n    );\n    noteModel.startx = startx - noteModel.width + (fromActor.width - conf.actorMargin) / 2;\n  } else if (msg.to === msg.from) {\n    textDimensions = _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.calculateTextDimensions(\n      shouldWrap ? _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.wrapLabel(msg.message, _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(conf.width, fromActor.width), noteFont(conf)) : msg.message,\n      noteFont(conf)\n    );\n    noteModel.width = shouldWrap ? _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(conf.width, fromActor.width) : _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(fromActor.width, conf.width, textDimensions.width + 2 * conf.noteMargin);\n    noteModel.startx = startx + (fromActor.width - noteModel.width) / 2;\n  } else {\n    noteModel.width = Math.abs(startx + fromActor.width / 2 - (stopx + toActor.width / 2)) + conf.actorMargin;\n    noteModel.startx = startx < stopx ? startx + fromActor.width / 2 - conf.actorMargin / 2 : stopx + toActor.width / 2 - conf.actorMargin / 2;\n  }\n  if (shouldWrap) {\n    noteModel.message = _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.wrapLabel(\n      msg.message,\n      noteModel.width - 2 * conf.wrapPadding,\n      noteFont(conf)\n    );\n  }\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.debug(\n    `NM:[${noteModel.startx},${noteModel.stopx},${noteModel.starty},${noteModel.stopy}:${noteModel.width},${noteModel.height}=${msg.message}]`\n  );\n  return noteModel;\n}, \"buildNoteModel\");\nvar buildMessageModel = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(function(msg, actors, diagObj) {\n  if (![\n    diagObj.db.LINETYPE.SOLID_OPEN,\n    diagObj.db.LINETYPE.DOTTED_OPEN,\n    diagObj.db.LINETYPE.SOLID,\n    diagObj.db.LINETYPE.DOTTED,\n    diagObj.db.LINETYPE.SOLID_CROSS,\n    diagObj.db.LINETYPE.DOTTED_CROSS,\n    diagObj.db.LINETYPE.SOLID_POINT,\n    diagObj.db.LINETYPE.DOTTED_POINT,\n    diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID,\n    diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED\n  ].includes(msg.type)) {\n    return {};\n  }\n  const [fromLeft, fromRight] = activationBounds(msg.from, actors);\n  const [toLeft, toRight] = activationBounds(msg.to, actors);\n  const isArrowToRight = fromLeft <= toLeft;\n  let startx = isArrowToRight ? fromRight : fromLeft;\n  let stopx = isArrowToRight ? toLeft : toRight;\n  const isArrowToActivation = Math.abs(toLeft - toRight) > 2;\n  const adjustValue = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((value) => {\n    return isArrowToRight ? -value : value;\n  }, \"adjustValue\");\n  if (msg.from === msg.to) {\n    stopx = startx;\n  } else {\n    if (msg.activate && !isArrowToActivation) {\n      stopx += adjustValue(conf.activationWidth / 2 - 1);\n    }\n    if (![diagObj.db.LINETYPE.SOLID_OPEN, diagObj.db.LINETYPE.DOTTED_OPEN].includes(msg.type)) {\n      stopx += adjustValue(3);\n    }\n    if ([diagObj.db.LINETYPE.BIDIRECTIONAL_SOLID, diagObj.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(\n      msg.type\n    )) {\n      startx -= adjustValue(3);\n    }\n  }\n  const allBounds = [fromLeft, fromRight, toLeft, toRight];\n  const boundedWidth = Math.abs(startx - stopx);\n  if (msg.wrap && msg.message) {\n    msg.message = _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.wrapLabel(\n      msg.message,\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(boundedWidth + 2 * conf.wrapPadding, conf.width),\n      messageFont(conf)\n    );\n  }\n  const msgDims = _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_2__.utils_default.calculateTextDimensions(msg.message, messageFont(conf));\n  return {\n    width: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(\n      msg.wrap ? 0 : msgDims.width + 2 * conf.wrapPadding,\n      boundedWidth + 2 * conf.wrapPadding,\n      conf.width\n    ),\n    height: 0,\n    startx,\n    stopx,\n    starty: 0,\n    stopy: 0,\n    message: msg.message,\n    type: msg.type,\n    wrap: msg.wrap,\n    fromBounds: Math.min.apply(null, allBounds),\n    toBounds: Math.max.apply(null, allBounds)\n  };\n}, \"buildMessageModel\");\nvar calculateLoopBounds = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)(async function(messages, actors, _maxWidthPerActor, diagObj) {\n  const loops = {};\n  const stack = [];\n  let current, noteModel, msgModel;\n  for (const msg of messages) {\n    switch (msg.type) {\n      case diagObj.db.LINETYPE.LOOP_START:\n      case diagObj.db.LINETYPE.ALT_START:\n      case diagObj.db.LINETYPE.OPT_START:\n      case diagObj.db.LINETYPE.PAR_START:\n      case diagObj.db.LINETYPE.PAR_OVER_START:\n      case diagObj.db.LINETYPE.CRITICAL_START:\n      case diagObj.db.LINETYPE.BREAK_START:\n        stack.push({\n          id: msg.id,\n          msg: msg.message,\n          from: Number.MAX_SAFE_INTEGER,\n          to: Number.MIN_SAFE_INTEGER,\n          width: 0\n        });\n        break;\n      case diagObj.db.LINETYPE.ALT_ELSE:\n      case diagObj.db.LINETYPE.PAR_AND:\n      case diagObj.db.LINETYPE.CRITICAL_OPTION:\n        if (msg.message) {\n          current = stack.pop();\n          loops[current.id] = current;\n          loops[msg.id] = current;\n          stack.push(current);\n        }\n        break;\n      case diagObj.db.LINETYPE.LOOP_END:\n      case diagObj.db.LINETYPE.ALT_END:\n      case diagObj.db.LINETYPE.OPT_END:\n      case diagObj.db.LINETYPE.PAR_END:\n      case diagObj.db.LINETYPE.CRITICAL_END:\n      case diagObj.db.LINETYPE.BREAK_END:\n        current = stack.pop();\n        loops[current.id] = current;\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_START:\n        {\n          const actorRect = actors.get(msg.from ? msg.from : msg.to.actor);\n          const stackedSize = actorActivations(msg.from ? msg.from : msg.to.actor).length;\n          const x = actorRect.x + actorRect.width / 2 + (stackedSize - 1) * conf.activationWidth / 2;\n          const toAdd = {\n            startx: x,\n            stopx: x + conf.activationWidth,\n            actor: msg.from,\n            enabled: true\n          };\n          bounds.activations.push(toAdd);\n        }\n        break;\n      case diagObj.db.LINETYPE.ACTIVE_END:\n        {\n          const lastActorActivationIdx = bounds.activations.map((a) => a.actor).lastIndexOf(msg.from);\n          bounds.activations.splice(lastActorActivationIdx, 1).splice(0, 1);\n        }\n        break;\n    }\n    const isNote = msg.placement !== void 0;\n    if (isNote) {\n      noteModel = await buildNoteModel(msg, actors, diagObj);\n      msg.noteModel = noteModel;\n      stack.forEach((stk) => {\n        current = stk;\n        current.from = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMin(current.from, noteModel.startx);\n        current.to = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(current.to, noteModel.startx + noteModel.width);\n        current.width = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(current.width, Math.abs(current.from - current.to)) - conf.labelBoxWidth;\n      });\n    } else {\n      msgModel = buildMessageModel(msg, actors, diagObj);\n      msg.msgModel = msgModel;\n      if (msgModel.startx && msgModel.stopx && stack.length > 0) {\n        stack.forEach((stk) => {\n          current = stk;\n          if (msgModel.startx === msgModel.stopx) {\n            const from = actors.get(msg.from);\n            const to = actors.get(msg.to);\n            current.from = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMin(\n              from.x - msgModel.width / 2,\n              from.x - from.width / 2,\n              current.from\n            );\n            current.to = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(\n              to.x + msgModel.width / 2,\n              to.x + from.width / 2,\n              current.to\n            );\n            current.width = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(current.width, Math.abs(current.to - current.from)) - conf.labelBoxWidth;\n          } else {\n            current.from = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMin(msgModel.startx, current.from);\n            current.to = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(msgModel.stopx, current.to);\n            current.width = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.common_default.getMax(current.width, msgModel.width) - conf.labelBoxWidth;\n          }\n        });\n      }\n    }\n  }\n  bounds.activations = [];\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.log.debug(\"Loop type widths:\", loops);\n  return loops;\n}, \"calculateLoopBounds\");\nvar sequenceRenderer_default = {\n  bounds,\n  drawActors,\n  drawActorsPopup,\n  setConf,\n  draw\n};\n\n// src/diagrams/sequence/sequenceDiagram.ts\nvar diagram = {\n  parser: sequenceDiagram_default,\n  get db() {\n    return new SequenceDB();\n  },\n  renderer: sequenceRenderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.__name)((cnf) => {\n    if (!cnf.sequence) {\n      cnf.sequence = {};\n    }\n    if (cnf.wrap) {\n      cnf.sequence.wrap = cnf.wrap;\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_3__.setConfig2)({ sequence: { wrap: cnf.wrap } });\n    }\n  }, \"init\")\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/sequenceDiagram-X6HHIX6F.mjs\n"));

/***/ })

}]);