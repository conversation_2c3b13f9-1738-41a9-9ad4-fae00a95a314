/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_chunks_mermaid_core_ganttDiagram-APWFNJXF_mjs"],{

/***/ "(app-pages-browser)/./node_modules/dayjs/plugin/advancedFormat.js":
/*!*****************************************************!*\
  !*** ./node_modules/dayjs/plugin/advancedFormat.js ***!
  \*****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t():0}(this,(function(){\"use strict\";return function(e,t){var r=t.prototype,n=r.format;r.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return n.bind(this)(e);var s=this.$utils(),a=(e||\"YYYY-MM-DDTHH:mm:ssZ\").replace(/\\[([^\\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,(function(e){switch(e){case\"Q\":return Math.ceil((t.$M+1)/3);case\"Do\":return r.ordinal(t.$D);case\"gggg\":return t.weekYear();case\"GGGG\":return t.isoWeekYear();case\"wo\":return r.ordinal(t.week(),\"W\");case\"w\":case\"ww\":return s.s(t.week(),\"w\"===e?1:2,\"0\");case\"W\":case\"WW\":return s.s(t.isoWeek(),\"W\"===e?1:2,\"0\");case\"k\":case\"kk\":return s.s(String(0===t.$H?24:t.$H),\"k\"===e?1:2,\"0\");case\"X\":return Math.floor(t.$d.getTime()/1e3);case\"x\":return t.$d.getTime();case\"z\":return\"[\"+t.offsetName()+\"]\";case\"zzz\":return\"[\"+t.offsetName(\"long\")+\"]\";default:return e}}));return n.bind(this)(a)}}}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/dayjs/plugin/advancedFormat.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/dayjs/plugin/customParseFormat.js":
/*!********************************************************!*\
  !*** ./node_modules/dayjs/plugin/customParseFormat.js ***!
  \********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t():0}(this,(function(){\"use strict\";var e={LTS:\"h:mm:ss A\",LT:\"h:mm A\",L:\"MM/DD/YYYY\",LL:\"MMMM D, YYYY\",LLL:\"MMMM D, YYYY h:mm A\",LLLL:\"dddd, MMMM D, YYYY h:mm A\"},t=/(\\[[^[]*\\])|([-_:/.,()\\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,n=/\\d/,r=/\\d\\d/,i=/\\d\\d?/,o=/\\d*[^-_:/,()\\s\\d]+/,s={},a=function(e){return(e=+e)+(e>68?1900:2e3)};var f=function(e){return function(t){this[e]=+t}},h=[/[+-]\\d\\d:?(\\d\\d)?|Z/,function(e){(this.zone||(this.zone={})).offset=function(e){if(!e)return 0;if(\"Z\"===e)return 0;var t=e.match(/([+-]|\\d\\d)/g),n=60*t[1]+(+t[2]||0);return 0===n?0:\"+\"===t[0]?-n:n}(e)}],u=function(e){var t=s[e];return t&&(t.indexOf?t:t.s.concat(t.f))},d=function(e,t){var n,r=s.meridiem;if(r){for(var i=1;i<=24;i+=1)if(e.indexOf(r(i,0,t))>-1){n=i>12;break}}else n=e===(t?\"pm\":\"PM\");return n},c={A:[o,function(e){this.afternoon=d(e,!1)}],a:[o,function(e){this.afternoon=d(e,!0)}],Q:[n,function(e){this.month=3*(e-1)+1}],S:[n,function(e){this.milliseconds=100*+e}],SS:[r,function(e){this.milliseconds=10*+e}],SSS:[/\\d{3}/,function(e){this.milliseconds=+e}],s:[i,f(\"seconds\")],ss:[i,f(\"seconds\")],m:[i,f(\"minutes\")],mm:[i,f(\"minutes\")],H:[i,f(\"hours\")],h:[i,f(\"hours\")],HH:[i,f(\"hours\")],hh:[i,f(\"hours\")],D:[i,f(\"day\")],DD:[r,f(\"day\")],Do:[o,function(e){var t=s.ordinal,n=e.match(/\\d+/);if(this.day=n[0],t)for(var r=1;r<=31;r+=1)t(r).replace(/\\[|\\]/g,\"\")===e&&(this.day=r)}],w:[i,f(\"week\")],ww:[r,f(\"week\")],M:[i,f(\"month\")],MM:[r,f(\"month\")],MMM:[o,function(e){var t=u(\"months\"),n=(u(\"monthsShort\")||t.map((function(e){return e.slice(0,3)}))).indexOf(e)+1;if(n<1)throw new Error;this.month=n%12||n}],MMMM:[o,function(e){var t=u(\"months\").indexOf(e)+1;if(t<1)throw new Error;this.month=t%12||t}],Y:[/[+-]?\\d+/,f(\"year\")],YY:[r,function(e){this.year=a(e)}],YYYY:[/\\d{4}/,f(\"year\")],Z:h,ZZ:h};function l(n){var r,i;r=n,i=s&&s.formats;for(var o=(n=r.replace(/(\\[[^\\]]+])|(LTS?|l{1,4}|L{1,4})/g,(function(t,n,r){var o=r&&r.toUpperCase();return n||i[r]||e[r]||i[o].replace(/(\\[[^\\]]+])|(MMMM|MM|DD|dddd)/g,(function(e,t,n){return t||n.slice(1)}))}))).match(t),a=o.length,f=0;f<a;f+=1){var h=o[f],u=c[h],d=u&&u[0],l=u&&u[1];o[f]=l?{regex:d,parser:l}:h.replace(/^\\[|\\]$/g,\"\")}return function(e){for(var t={},n=0,r=0;n<a;n+=1){var i=o[n];if(\"string\"==typeof i)r+=i.length;else{var s=i.regex,f=i.parser,h=e.slice(r),u=s.exec(h)[0];f.call(t,u),e=e.replace(u,\"\")}}return function(e){var t=e.afternoon;if(void 0!==t){var n=e.hours;t?n<12&&(e.hours+=12):12===n&&(e.hours=0),delete e.afternoon}}(t),t}}return function(e,t,n){n.p.customParseFormat=!0,e&&e.parseTwoDigitYear&&(a=e.parseTwoDigitYear);var r=t.prototype,i=r.parse;r.parse=function(e){var t=e.date,r=e.utc,o=e.args;this.$u=r;var a=o[1];if(\"string\"==typeof a){var f=!0===o[2],h=!0===o[3],u=f||h,d=o[2];h&&(d=o[2]),s=this.$locale(),!f&&d&&(s=n.Ls[d]),this.$d=function(e,t,n,r){try{if([\"x\",\"X\"].indexOf(t)>-1)return new Date((\"X\"===t?1e3:1)*e);var i=l(t)(e),o=i.year,s=i.month,a=i.day,f=i.hours,h=i.minutes,u=i.seconds,d=i.milliseconds,c=i.zone,m=i.week,M=new Date,Y=a||(o||s?1:M.getDate()),p=o||M.getFullYear(),v=0;o&&!s||(v=s>0?s-1:M.getMonth());var D,w=f||0,g=h||0,y=u||0,L=d||0;return c?new Date(Date.UTC(p,v,Y,w,g,y,L+60*c.offset*1e3)):n?new Date(Date.UTC(p,v,Y,w,g,y,L)):(D=new Date(p,v,Y,w,g,y,L),m&&(D=r(D).week(m).toDate()),D)}catch(e){return new Date(\"\")}}(t,a,r,n),this.init(),d&&!0!==d&&(this.$L=this.locale(d).$L),u&&t!=this.format(a)&&(this.$d=new Date(\"\")),s={}}else if(a instanceof Array)for(var c=a.length,m=1;m<=c;m+=1){o[1]=a[m-1];var M=n.apply(this,o);if(M.isValid()){this.$d=M.$d,this.$L=M.$L,this.init();break}m===c&&(this.$d=new Date(\"\"))}else i.call(this,e)}}}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/dayjs/plugin/customParseFormat.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/dayjs/plugin/isoWeek.js":
/*!**********************************************!*\
  !*** ./node_modules/dayjs/plugin/isoWeek.js ***!
  \**********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t():0}(this,(function(){\"use strict\";var e=\"day\";return function(t,i,s){var a=function(t){return t.add(4-t.isoWeekday(),e)},d=i.prototype;d.isoWeekYear=function(){return a(this).year()},d.isoWeek=function(t){if(!this.$utils().u(t))return this.add(7*(t-this.isoWeek()),e);var i,d,n,o,r=a(this),u=(i=this.isoWeekYear(),d=this.$u,n=(d?s.utc:s)().year(i).startOf(\"year\"),o=4-n.isoWeekday(),n.isoWeekday()>4&&(o+=7),n.add(o,e));return r.diff(u,\"week\")+1},d.isoWeekday=function(e){return this.$utils().u(e)?this.day()||7:this.day(this.day()%7?e:e-7)};var n=d.startOf;d.startOf=function(e,t){var i=this.$utils(),s=!!i.u(t)||t;return\"isoweek\"===i.p(e)?s?this.date(this.date()-(this.isoWeekday()-1)).startOf(\"day\"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf(\"day\"):n.bind(this)(e,t)}}}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXlqcy9wbHVnaW4vaXNvV2Vlay5qcyIsIm1hcHBpbmdzIjoiQUFBQSxlQUFlLEtBQW9ELG9CQUFvQixDQUE4SCxDQUFDLGtCQUFrQixhQUFhLFlBQVksdUJBQXVCLGtCQUFrQixpQ0FBaUMsZUFBZSx5QkFBeUIsc0JBQXNCLHVCQUF1QiwrREFBK0Qsd0pBQXdKLDBCQUEwQiwwQkFBMEIsc0VBQXNFLGdCQUFnQix3QkFBd0Isa0NBQWtDLHlLQUF5SyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkYXlqc1xccGx1Z2luXFxpc29XZWVrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIiFmdW5jdGlvbihlLHQpe1wib2JqZWN0XCI9PXR5cGVvZiBleHBvcnRzJiZcInVuZGVmaW5lZFwiIT10eXBlb2YgbW9kdWxlP21vZHVsZS5leHBvcnRzPXQoKTpcImZ1bmN0aW9uXCI9PXR5cGVvZiBkZWZpbmUmJmRlZmluZS5hbWQ/ZGVmaW5lKHQpOihlPVwidW5kZWZpbmVkXCIhPXR5cGVvZiBnbG9iYWxUaGlzP2dsb2JhbFRoaXM6ZXx8c2VsZikuZGF5anNfcGx1Z2luX2lzb1dlZWs9dCgpfSh0aGlzLChmdW5jdGlvbigpe1widXNlIHN0cmljdFwiO3ZhciBlPVwiZGF5XCI7cmV0dXJuIGZ1bmN0aW9uKHQsaSxzKXt2YXIgYT1mdW5jdGlvbih0KXtyZXR1cm4gdC5hZGQoNC10Lmlzb1dlZWtkYXkoKSxlKX0sZD1pLnByb3RvdHlwZTtkLmlzb1dlZWtZZWFyPWZ1bmN0aW9uKCl7cmV0dXJuIGEodGhpcykueWVhcigpfSxkLmlzb1dlZWs9ZnVuY3Rpb24odCl7aWYoIXRoaXMuJHV0aWxzKCkudSh0KSlyZXR1cm4gdGhpcy5hZGQoNyoodC10aGlzLmlzb1dlZWsoKSksZSk7dmFyIGksZCxuLG8scj1hKHRoaXMpLHU9KGk9dGhpcy5pc29XZWVrWWVhcigpLGQ9dGhpcy4kdSxuPShkP3MudXRjOnMpKCkueWVhcihpKS5zdGFydE9mKFwieWVhclwiKSxvPTQtbi5pc29XZWVrZGF5KCksbi5pc29XZWVrZGF5KCk+NCYmKG8rPTcpLG4uYWRkKG8sZSkpO3JldHVybiByLmRpZmYodSxcIndlZWtcIikrMX0sZC5pc29XZWVrZGF5PWZ1bmN0aW9uKGUpe3JldHVybiB0aGlzLiR1dGlscygpLnUoZSk/dGhpcy5kYXkoKXx8Nzp0aGlzLmRheSh0aGlzLmRheSgpJTc/ZTplLTcpfTt2YXIgbj1kLnN0YXJ0T2Y7ZC5zdGFydE9mPWZ1bmN0aW9uKGUsdCl7dmFyIGk9dGhpcy4kdXRpbHMoKSxzPSEhaS51KHQpfHx0O3JldHVyblwiaXNvd2Vla1wiPT09aS5wKGUpP3M/dGhpcy5kYXRlKHRoaXMuZGF0ZSgpLSh0aGlzLmlzb1dlZWtkYXkoKS0xKSkuc3RhcnRPZihcImRheVwiKTp0aGlzLmRhdGUodGhpcy5kYXRlKCktMS0odGhpcy5pc29XZWVrZGF5KCktMSkrNykuZW5kT2YoXCJkYXlcIik6bi5iaW5kKHRoaXMpKGUsdCl9fX0pKTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/dayjs/plugin/isoWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/ganttDiagram-APWFNJXF.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/ganttDiagram-APWFNJXF.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: () => (/* binding */ diagram)\n/* harmony export */ });\n/* harmony import */ var _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-O4NI6UNU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-O4NI6UNU.mjs\");\n/* harmony import */ var _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-YTJNT7DU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-YTJNT7DU.mjs\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs_plugin_isoWeek_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs/plugin/isoWeek.js */ \"(app-pages-browser)/./node_modules/dayjs/plugin/isoWeek.js\");\n/* harmony import */ var dayjs_plugin_customParseFormat_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs/plugin/customParseFormat.js */ \"(app-pages-browser)/./node_modules/dayjs/plugin/customParseFormat.js\");\n/* harmony import */ var dayjs_plugin_advancedFormat_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! dayjs/plugin/advancedFormat.js */ \"(app-pages-browser)/./node_modules/dayjs/plugin/advancedFormat.js\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n\n\n\n// src/diagrams/gantt/parser/gantt.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [6, 8, 10, 12, 13, 14, 15, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 35, 36, 38, 40], $V1 = [1, 26], $V2 = [1, 27], $V3 = [1, 28], $V4 = [1, 29], $V5 = [1, 30], $V6 = [1, 31], $V7 = [1, 32], $V8 = [1, 33], $V9 = [1, 34], $Va = [1, 9], $Vb = [1, 10], $Vc = [1, 11], $Vd = [1, 12], $Ve = [1, 13], $Vf = [1, 14], $Vg = [1, 15], $Vh = [1, 16], $Vi = [1, 19], $Vj = [1, 20], $Vk = [1, 21], $Vl = [1, 22], $Vm = [1, 23], $Vn = [1, 25], $Vo = [1, 35];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"gantt\": 4, \"document\": 5, \"EOF\": 6, \"line\": 7, \"SPACE\": 8, \"statement\": 9, \"NL\": 10, \"weekday\": 11, \"weekday_monday\": 12, \"weekday_tuesday\": 13, \"weekday_wednesday\": 14, \"weekday_thursday\": 15, \"weekday_friday\": 16, \"weekday_saturday\": 17, \"weekday_sunday\": 18, \"weekend\": 19, \"weekend_friday\": 20, \"weekend_saturday\": 21, \"dateFormat\": 22, \"inclusiveEndDates\": 23, \"topAxis\": 24, \"axisFormat\": 25, \"tickInterval\": 26, \"excludes\": 27, \"includes\": 28, \"todayMarker\": 29, \"title\": 30, \"acc_title\": 31, \"acc_title_value\": 32, \"acc_descr\": 33, \"acc_descr_value\": 34, \"acc_descr_multiline_value\": 35, \"section\": 36, \"clickStatement\": 37, \"taskTxt\": 38, \"taskData\": 39, \"click\": 40, \"callbackname\": 41, \"callbackargs\": 42, \"href\": 43, \"clickStatementDebug\": 44, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"gantt\", 6: \"EOF\", 8: \"SPACE\", 10: \"NL\", 12: \"weekday_monday\", 13: \"weekday_tuesday\", 14: \"weekday_wednesday\", 15: \"weekday_thursday\", 16: \"weekday_friday\", 17: \"weekday_saturday\", 18: \"weekday_sunday\", 20: \"weekend_friday\", 21: \"weekend_saturday\", 22: \"dateFormat\", 23: \"inclusiveEndDates\", 24: \"topAxis\", 25: \"axisFormat\", 26: \"tickInterval\", 27: \"excludes\", 28: \"includes\", 29: \"todayMarker\", 30: \"title\", 31: \"acc_title\", 32: \"acc_title_value\", 33: \"acc_descr\", 34: \"acc_descr_value\", 35: \"acc_descr_multiline_value\", 36: \"section\", 38: \"taskTxt\", 39: \"taskData\", 40: \"click\", 41: \"callbackname\", 42: \"callbackargs\", 43: \"href\" },\n    productions_: [0, [3, 3], [5, 0], [5, 2], [7, 2], [7, 1], [7, 1], [7, 1], [11, 1], [11, 1], [11, 1], [11, 1], [11, 1], [11, 1], [11, 1], [19, 1], [19, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 1], [9, 1], [9, 2], [37, 2], [37, 3], [37, 3], [37, 4], [37, 3], [37, 4], [37, 2], [44, 2], [44, 3], [44, 3], [44, 4], [44, 3], [44, 4], [44, 2]],\n    performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 1:\n          return $$[$0 - 1];\n          break;\n        case 2:\n          this.$ = [];\n          break;\n        case 3:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 4:\n        case 5:\n          this.$ = $$[$0];\n          break;\n        case 6:\n        case 7:\n          this.$ = [];\n          break;\n        case 8:\n          yy.setWeekday(\"monday\");\n          break;\n        case 9:\n          yy.setWeekday(\"tuesday\");\n          break;\n        case 10:\n          yy.setWeekday(\"wednesday\");\n          break;\n        case 11:\n          yy.setWeekday(\"thursday\");\n          break;\n        case 12:\n          yy.setWeekday(\"friday\");\n          break;\n        case 13:\n          yy.setWeekday(\"saturday\");\n          break;\n        case 14:\n          yy.setWeekday(\"sunday\");\n          break;\n        case 15:\n          yy.setWeekend(\"friday\");\n          break;\n        case 16:\n          yy.setWeekend(\"saturday\");\n          break;\n        case 17:\n          yy.setDateFormat($$[$0].substr(11));\n          this.$ = $$[$0].substr(11);\n          break;\n        case 18:\n          yy.enableInclusiveEndDates();\n          this.$ = $$[$0].substr(18);\n          break;\n        case 19:\n          yy.TopAxis();\n          this.$ = $$[$0].substr(8);\n          break;\n        case 20:\n          yy.setAxisFormat($$[$0].substr(11));\n          this.$ = $$[$0].substr(11);\n          break;\n        case 21:\n          yy.setTickInterval($$[$0].substr(13));\n          this.$ = $$[$0].substr(13);\n          break;\n        case 22:\n          yy.setExcludes($$[$0].substr(9));\n          this.$ = $$[$0].substr(9);\n          break;\n        case 23:\n          yy.setIncludes($$[$0].substr(9));\n          this.$ = $$[$0].substr(9);\n          break;\n        case 24:\n          yy.setTodayMarker($$[$0].substr(12));\n          this.$ = $$[$0].substr(12);\n          break;\n        case 27:\n          yy.setDiagramTitle($$[$0].substr(6));\n          this.$ = $$[$0].substr(6);\n          break;\n        case 28:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 29:\n        case 30:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 31:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 33:\n          yy.addTask($$[$0 - 1], $$[$0]);\n          this.$ = \"task\";\n          break;\n        case 34:\n          this.$ = $$[$0 - 1];\n          yy.setClickEvent($$[$0 - 1], $$[$0], null);\n          break;\n        case 35:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 2], $$[$0 - 1], $$[$0]);\n          break;\n        case 36:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 2], $$[$0 - 1], null);\n          yy.setLink($$[$0 - 2], $$[$0]);\n          break;\n        case 37:\n          this.$ = $$[$0 - 3];\n          yy.setClickEvent($$[$0 - 3], $$[$0 - 2], $$[$0 - 1]);\n          yy.setLink($$[$0 - 3], $$[$0]);\n          break;\n        case 38:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 2], $$[$0], null);\n          yy.setLink($$[$0 - 2], $$[$0 - 1]);\n          break;\n        case 39:\n          this.$ = $$[$0 - 3];\n          yy.setClickEvent($$[$0 - 3], $$[$0 - 1], $$[$0]);\n          yy.setLink($$[$0 - 3], $$[$0 - 2]);\n          break;\n        case 40:\n          this.$ = $$[$0 - 1];\n          yy.setLink($$[$0 - 1], $$[$0]);\n          break;\n        case 41:\n        case 47:\n          this.$ = $$[$0 - 1] + \" \" + $$[$0];\n          break;\n        case 42:\n        case 43:\n        case 45:\n          this.$ = $$[$0 - 2] + \" \" + $$[$0 - 1] + \" \" + $$[$0];\n          break;\n        case 44:\n        case 46:\n          this.$ = $$[$0 - 3] + \" \" + $$[$0 - 2] + \" \" + $$[$0 - 1] + \" \" + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, o($V0, [2, 2], { 5: 3 }), { 6: [1, 4], 7: 5, 8: [1, 6], 9: 7, 10: [1, 8], 11: 17, 12: $V1, 13: $V2, 14: $V3, 15: $V4, 16: $V5, 17: $V6, 18: $V7, 19: 18, 20: $V8, 21: $V9, 22: $Va, 23: $Vb, 24: $Vc, 25: $Vd, 26: $Ve, 27: $Vf, 28: $Vg, 29: $Vh, 30: $Vi, 31: $Vj, 33: $Vk, 35: $Vl, 36: $Vm, 37: 24, 38: $Vn, 40: $Vo }, o($V0, [2, 7], { 1: [2, 1] }), o($V0, [2, 3]), { 9: 36, 11: 17, 12: $V1, 13: $V2, 14: $V3, 15: $V4, 16: $V5, 17: $V6, 18: $V7, 19: 18, 20: $V8, 21: $V9, 22: $Va, 23: $Vb, 24: $Vc, 25: $Vd, 26: $Ve, 27: $Vf, 28: $Vg, 29: $Vh, 30: $Vi, 31: $Vj, 33: $Vk, 35: $Vl, 36: $Vm, 37: 24, 38: $Vn, 40: $Vo }, o($V0, [2, 5]), o($V0, [2, 6]), o($V0, [2, 17]), o($V0, [2, 18]), o($V0, [2, 19]), o($V0, [2, 20]), o($V0, [2, 21]), o($V0, [2, 22]), o($V0, [2, 23]), o($V0, [2, 24]), o($V0, [2, 25]), o($V0, [2, 26]), o($V0, [2, 27]), { 32: [1, 37] }, { 34: [1, 38] }, o($V0, [2, 30]), o($V0, [2, 31]), o($V0, [2, 32]), { 39: [1, 39] }, o($V0, [2, 8]), o($V0, [2, 9]), o($V0, [2, 10]), o($V0, [2, 11]), o($V0, [2, 12]), o($V0, [2, 13]), o($V0, [2, 14]), o($V0, [2, 15]), o($V0, [2, 16]), { 41: [1, 40], 43: [1, 41] }, o($V0, [2, 4]), o($V0, [2, 28]), o($V0, [2, 29]), o($V0, [2, 33]), o($V0, [2, 34], { 42: [1, 42], 43: [1, 43] }), o($V0, [2, 40], { 41: [1, 44] }), o($V0, [2, 35], { 43: [1, 45] }), o($V0, [2, 36]), o($V0, [2, 38], { 42: [1, 46] }), o($V0, [2, 37]), o($V0, [2, 39])],\n    defaultActions: {},\n    parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.begin(\"open_directive\");\n            return \"open_directive\";\n            break;\n          case 1:\n            this.begin(\"acc_title\");\n            return 31;\n            break;\n          case 2:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 3:\n            this.begin(\"acc_descr\");\n            return 33;\n            break;\n          case 4:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 5:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 6:\n            this.popState();\n            break;\n          case 7:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 8:\n            break;\n          case 9:\n            break;\n          case 10:\n            break;\n          case 11:\n            return 10;\n            break;\n          case 12:\n            break;\n          case 13:\n            break;\n          case 14:\n            this.begin(\"href\");\n            break;\n          case 15:\n            this.popState();\n            break;\n          case 16:\n            return 43;\n            break;\n          case 17:\n            this.begin(\"callbackname\");\n            break;\n          case 18:\n            this.popState();\n            break;\n          case 19:\n            this.popState();\n            this.begin(\"callbackargs\");\n            break;\n          case 20:\n            return 41;\n            break;\n          case 21:\n            this.popState();\n            break;\n          case 22:\n            return 42;\n            break;\n          case 23:\n            this.begin(\"click\");\n            break;\n          case 24:\n            this.popState();\n            break;\n          case 25:\n            return 40;\n            break;\n          case 26:\n            return 4;\n            break;\n          case 27:\n            return 22;\n            break;\n          case 28:\n            return 23;\n            break;\n          case 29:\n            return 24;\n            break;\n          case 30:\n            return 25;\n            break;\n          case 31:\n            return 26;\n            break;\n          case 32:\n            return 28;\n            break;\n          case 33:\n            return 27;\n            break;\n          case 34:\n            return 29;\n            break;\n          case 35:\n            return 12;\n            break;\n          case 36:\n            return 13;\n            break;\n          case 37:\n            return 14;\n            break;\n          case 38:\n            return 15;\n            break;\n          case 39:\n            return 16;\n            break;\n          case 40:\n            return 17;\n            break;\n          case 41:\n            return 18;\n            break;\n          case 42:\n            return 20;\n            break;\n          case 43:\n            return 21;\n            break;\n          case 44:\n            return \"date\";\n            break;\n          case 45:\n            return 30;\n            break;\n          case 46:\n            return \"accDescription\";\n            break;\n          case 47:\n            return 36;\n            break;\n          case 48:\n            return 38;\n            break;\n          case 49:\n            return 39;\n            break;\n          case 50:\n            return \":\";\n            break;\n          case 51:\n            return 6;\n            break;\n          case 52:\n            return \"INVALID\";\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%%\\{)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:%%(?!\\{)*[^\\n]*)/i, /^(?:[^\\}]%%*[^\\n]*)/i, /^(?:%%*[^\\n]*[\\n]*)/i, /^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:%[^\\n]*)/i, /^(?:href[\\s]+[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:call[\\s]+)/i, /^(?:\\([\\s]*\\))/i, /^(?:\\()/i, /^(?:[^(]*)/i, /^(?:\\))/i, /^(?:[^)]*)/i, /^(?:click[\\s]+)/i, /^(?:[\\s\\n])/i, /^(?:[^\\s\\n]*)/i, /^(?:gantt\\b)/i, /^(?:dateFormat\\s[^#\\n;]+)/i, /^(?:inclusiveEndDates\\b)/i, /^(?:topAxis\\b)/i, /^(?:axisFormat\\s[^#\\n;]+)/i, /^(?:tickInterval\\s[^#\\n;]+)/i, /^(?:includes\\s[^#\\n;]+)/i, /^(?:excludes\\s[^#\\n;]+)/i, /^(?:todayMarker\\s[^\\n;]+)/i, /^(?:weekday\\s+monday\\b)/i, /^(?:weekday\\s+tuesday\\b)/i, /^(?:weekday\\s+wednesday\\b)/i, /^(?:weekday\\s+thursday\\b)/i, /^(?:weekday\\s+friday\\b)/i, /^(?:weekday\\s+saturday\\b)/i, /^(?:weekday\\s+sunday\\b)/i, /^(?:weekend\\s+friday\\b)/i, /^(?:weekend\\s+saturday\\b)/i, /^(?:\\d\\d\\d\\d-\\d\\d-\\d\\d\\b)/i, /^(?:title\\s[^\\n]+)/i, /^(?:accDescription\\s[^#\\n;]+)/i, /^(?:section\\s[^\\n]+)/i, /^(?:[^:\\n]+)/i, /^(?::[^#\\n;]+)/i, /^(?::)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [6, 7], \"inclusive\": false }, \"acc_descr\": { \"rules\": [4], \"inclusive\": false }, \"acc_title\": { \"rules\": [2], \"inclusive\": false }, \"callbackargs\": { \"rules\": [21, 22], \"inclusive\": false }, \"callbackname\": { \"rules\": [18, 19, 20], \"inclusive\": false }, \"href\": { \"rules\": [15, 16], \"inclusive\": false }, \"click\": { \"rules\": [24, 25], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 3, 5, 8, 9, 10, 11, 12, 13, 14, 17, 23, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar gantt_default = parser;\n\n// src/diagrams/gantt/ganttDb.js\n\n\n\n\n\ndayjs__WEBPACK_IMPORTED_MODULE_3__.extend(dayjs_plugin_isoWeek_js__WEBPACK_IMPORTED_MODULE_4__);\ndayjs__WEBPACK_IMPORTED_MODULE_3__.extend(dayjs_plugin_customParseFormat_js__WEBPACK_IMPORTED_MODULE_5__);\ndayjs__WEBPACK_IMPORTED_MODULE_3__.extend(dayjs_plugin_advancedFormat_js__WEBPACK_IMPORTED_MODULE_6__);\nvar WEEKEND_START_DAY = { friday: 5, saturday: 6 };\nvar dateFormat = \"\";\nvar axisFormat = \"\";\nvar tickInterval = void 0;\nvar todayMarker = \"\";\nvar includes = [];\nvar excludes = [];\nvar links = /* @__PURE__ */ new Map();\nvar sections = [];\nvar tasks = [];\nvar currentSection = \"\";\nvar displayMode = \"\";\nvar tags = [\"active\", \"done\", \"crit\", \"milestone\"];\nvar funs = [];\nvar inclusiveEndDates = false;\nvar topAxis = false;\nvar weekday = \"sunday\";\nvar weekend = \"saturday\";\nvar lastOrder = 0;\nvar clear2 = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  sections = [];\n  tasks = [];\n  currentSection = \"\";\n  funs = [];\n  taskCnt = 0;\n  lastTask = void 0;\n  lastTaskID = void 0;\n  rawTasks = [];\n  dateFormat = \"\";\n  axisFormat = \"\";\n  displayMode = \"\";\n  tickInterval = void 0;\n  todayMarker = \"\";\n  includes = [];\n  excludes = [];\n  inclusiveEndDates = false;\n  topAxis = false;\n  lastOrder = 0;\n  links = /* @__PURE__ */ new Map();\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.clear)();\n  weekday = \"sunday\";\n  weekend = \"saturday\";\n}, \"clear\");\nvar setAxisFormat = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  axisFormat = txt;\n}, \"setAxisFormat\");\nvar getAxisFormat = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return axisFormat;\n}, \"getAxisFormat\");\nvar setTickInterval = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  tickInterval = txt;\n}, \"setTickInterval\");\nvar getTickInterval = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return tickInterval;\n}, \"getTickInterval\");\nvar setTodayMarker = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  todayMarker = txt;\n}, \"setTodayMarker\");\nvar getTodayMarker = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return todayMarker;\n}, \"getTodayMarker\");\nvar setDateFormat = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  dateFormat = txt;\n}, \"setDateFormat\");\nvar enableInclusiveEndDates = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  inclusiveEndDates = true;\n}, \"enableInclusiveEndDates\");\nvar endDatesAreInclusive = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return inclusiveEndDates;\n}, \"endDatesAreInclusive\");\nvar enableTopAxis = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  topAxis = true;\n}, \"enableTopAxis\");\nvar topAxisEnabled = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return topAxis;\n}, \"topAxisEnabled\");\nvar setDisplayMode = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  displayMode = txt;\n}, \"setDisplayMode\");\nvar getDisplayMode = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return displayMode;\n}, \"getDisplayMode\");\nvar getDateFormat = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return dateFormat;\n}, \"getDateFormat\");\nvar setIncludes = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  includes = txt.toLowerCase().split(/[\\s,]+/);\n}, \"setIncludes\");\nvar getIncludes = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return includes;\n}, \"getIncludes\");\nvar setExcludes = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  excludes = txt.toLowerCase().split(/[\\s,]+/);\n}, \"setExcludes\");\nvar getExcludes = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return excludes;\n}, \"getExcludes\");\nvar getLinks = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return links;\n}, \"getLinks\");\nvar addSection = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  currentSection = txt;\n  sections.push(txt);\n}, \"addSection\");\nvar getSections = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return sections;\n}, \"getSections\");\nvar getTasks = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 10;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n  tasks = rawTasks;\n  return tasks;\n}, \"getTasks\");\nvar isInvalidDate = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(date, dateFormat2, excludes2, includes2) {\n  if (includes2.includes(date.format(dateFormat2.trim()))) {\n    return false;\n  }\n  if (excludes2.includes(\"weekends\") && (date.isoWeekday() === WEEKEND_START_DAY[weekend] || date.isoWeekday() === WEEKEND_START_DAY[weekend] + 1)) {\n    return true;\n  }\n  if (excludes2.includes(date.format(\"dddd\").toLowerCase())) {\n    return true;\n  }\n  return excludes2.includes(date.format(dateFormat2.trim()));\n}, \"isInvalidDate\");\nvar setWeekday = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(txt) {\n  weekday = txt;\n}, \"setWeekday\");\nvar getWeekday = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  return weekday;\n}, \"getWeekday\");\nvar setWeekend = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(startDay) {\n  weekend = startDay;\n}, \"setWeekend\");\nvar checkTaskDates = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(task, dateFormat2, excludes2, includes2) {\n  if (!excludes2.length || task.manualEndTime) {\n    return;\n  }\n  let startTime;\n  if (task.startTime instanceof Date) {\n    startTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(task.startTime);\n  } else {\n    startTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(task.startTime, dateFormat2, true);\n  }\n  startTime = startTime.add(1, \"d\");\n  let originalEndTime;\n  if (task.endTime instanceof Date) {\n    originalEndTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(task.endTime);\n  } else {\n    originalEndTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(task.endTime, dateFormat2, true);\n  }\n  const [fixedEndTime, renderEndTime] = fixTaskDates(\n    startTime,\n    originalEndTime,\n    dateFormat2,\n    excludes2,\n    includes2\n  );\n  task.endTime = fixedEndTime.toDate();\n  task.renderEndTime = renderEndTime;\n}, \"checkTaskDates\");\nvar fixTaskDates = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(startTime, endTime, dateFormat2, excludes2, includes2) {\n  let invalid = false;\n  let renderEndTime = null;\n  while (startTime <= endTime) {\n    if (!invalid) {\n      renderEndTime = endTime.toDate();\n    }\n    invalid = isInvalidDate(startTime, dateFormat2, excludes2, includes2);\n    if (invalid) {\n      endTime = endTime.add(1, \"d\");\n    }\n    startTime = startTime.add(1, \"d\");\n  }\n  return [endTime, renderEndTime];\n}, \"fixTaskDates\");\nvar getStartDate = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(prevTime, dateFormat2, str) {\n  str = str.trim();\n  const afterRePattern = /^after\\s+(?<ids>[\\d\\w- ]+)/;\n  const afterStatement = afterRePattern.exec(str);\n  if (afterStatement !== null) {\n    let latestTask = null;\n    for (const id of afterStatement.groups.ids.split(\" \")) {\n      let task = findTaskById(id);\n      if (task !== void 0 && (!latestTask || task.endTime > latestTask.endTime)) {\n        latestTask = task;\n      }\n    }\n    if (latestTask) {\n      return latestTask.endTime;\n    }\n    const today = /* @__PURE__ */ new Date();\n    today.setHours(0, 0, 0, 0);\n    return today;\n  }\n  let mDate = dayjs__WEBPACK_IMPORTED_MODULE_3__(str, dateFormat2.trim(), true);\n  if (mDate.isValid()) {\n    return mDate.toDate();\n  } else {\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.log.debug(\"Invalid date:\" + str);\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.log.debug(\"With date format:\" + dateFormat2.trim());\n    const d = new Date(str);\n    if (d === void 0 || isNaN(d.getTime()) || // WebKit browsers can mis-parse invalid dates to be ridiculously\n    // huge numbers, e.g. new Date('202304') gets parsed as January 1, 202304.\n    // This can cause virtually infinite loops while rendering, so for the\n    // purposes of Gantt charts we'll just treat any date beyond 10,000 AD/BC as\n    // invalid.\n    d.getFullYear() < -1e4 || d.getFullYear() > 1e4) {\n      throw new Error(\"Invalid date:\" + str);\n    }\n    return d;\n  }\n}, \"getStartDate\");\nvar parseDuration = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(str) {\n  const statement = /^(\\d+(?:\\.\\d+)?)([Mdhmswy]|ms)$/.exec(str.trim());\n  if (statement !== null) {\n    return [Number.parseFloat(statement[1]), statement[2]];\n  }\n  return [NaN, \"ms\"];\n}, \"parseDuration\");\nvar getEndDate = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(prevTime, dateFormat2, str, inclusive = false) {\n  str = str.trim();\n  const untilRePattern = /^until\\s+(?<ids>[\\d\\w- ]+)/;\n  const untilStatement = untilRePattern.exec(str);\n  if (untilStatement !== null) {\n    let earliestTask = null;\n    for (const id of untilStatement.groups.ids.split(\" \")) {\n      let task = findTaskById(id);\n      if (task !== void 0 && (!earliestTask || task.startTime < earliestTask.startTime)) {\n        earliestTask = task;\n      }\n    }\n    if (earliestTask) {\n      return earliestTask.startTime;\n    }\n    const today = /* @__PURE__ */ new Date();\n    today.setHours(0, 0, 0, 0);\n    return today;\n  }\n  let parsedDate = dayjs__WEBPACK_IMPORTED_MODULE_3__(str, dateFormat2.trim(), true);\n  if (parsedDate.isValid()) {\n    if (inclusive) {\n      parsedDate = parsedDate.add(1, \"d\");\n    }\n    return parsedDate.toDate();\n  }\n  let endTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(prevTime);\n  const [durationValue, durationUnit] = parseDuration(str);\n  if (!Number.isNaN(durationValue)) {\n    const newEndTime = endTime.add(durationValue, durationUnit);\n    if (newEndTime.isValid()) {\n      endTime = newEndTime;\n    }\n  }\n  return endTime.toDate();\n}, \"getEndDate\");\nvar taskCnt = 0;\nvar parseId = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(idStr) {\n  if (idStr === void 0) {\n    taskCnt = taskCnt + 1;\n    return \"task\" + taskCnt;\n  }\n  return idStr;\n}, \"parseId\");\nvar compileData = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(prevTask, dataStr) {\n  let ds;\n  if (dataStr.substr(0, 1) === \":\") {\n    ds = dataStr.substr(1, dataStr.length);\n  } else {\n    ds = dataStr;\n  }\n  const data = ds.split(\",\");\n  const task = {};\n  getTaskTags(data, task, tags);\n  for (let i = 0; i < data.length; i++) {\n    data[i] = data[i].trim();\n  }\n  let endTimeData = \"\";\n  switch (data.length) {\n    case 1:\n      task.id = parseId();\n      task.startTime = prevTask.endTime;\n      endTimeData = data[0];\n      break;\n    case 2:\n      task.id = parseId();\n      task.startTime = getStartDate(void 0, dateFormat, data[0]);\n      endTimeData = data[1];\n      break;\n    case 3:\n      task.id = parseId(data[0]);\n      task.startTime = getStartDate(void 0, dateFormat, data[1]);\n      endTimeData = data[2];\n      break;\n    default:\n  }\n  if (endTimeData) {\n    task.endTime = getEndDate(task.startTime, dateFormat, endTimeData, inclusiveEndDates);\n    task.manualEndTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(endTimeData, \"YYYY-MM-DD\", true).isValid();\n    checkTaskDates(task, dateFormat, excludes, includes);\n  }\n  return task;\n}, \"compileData\");\nvar parseData = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(prevTaskId, dataStr) {\n  let ds;\n  if (dataStr.substr(0, 1) === \":\") {\n    ds = dataStr.substr(1, dataStr.length);\n  } else {\n    ds = dataStr;\n  }\n  const data = ds.split(\",\");\n  const task = {};\n  getTaskTags(data, task, tags);\n  for (let i = 0; i < data.length; i++) {\n    data[i] = data[i].trim();\n  }\n  switch (data.length) {\n    case 1:\n      task.id = parseId();\n      task.startTime = {\n        type: \"prevTaskEnd\",\n        id: prevTaskId\n      };\n      task.endTime = {\n        data: data[0]\n      };\n      break;\n    case 2:\n      task.id = parseId();\n      task.startTime = {\n        type: \"getStartDate\",\n        startData: data[0]\n      };\n      task.endTime = {\n        data: data[1]\n      };\n      break;\n    case 3:\n      task.id = parseId(data[0]);\n      task.startTime = {\n        type: \"getStartDate\",\n        startData: data[1]\n      };\n      task.endTime = {\n        data: data[2]\n      };\n      break;\n    default:\n  }\n  return task;\n}, \"parseData\");\nvar lastTask;\nvar lastTaskID;\nvar rawTasks = [];\nvar taskDb = {};\nvar addTask = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(descr, data) {\n  const rawTask = {\n    section: currentSection,\n    type: currentSection,\n    processed: false,\n    manualEndTime: false,\n    renderEndTime: null,\n    raw: { data },\n    task: descr,\n    classes: []\n  };\n  const taskInfo = parseData(lastTaskID, data);\n  rawTask.raw.startTime = taskInfo.startTime;\n  rawTask.raw.endTime = taskInfo.endTime;\n  rawTask.id = taskInfo.id;\n  rawTask.prevTaskId = lastTaskID;\n  rawTask.active = taskInfo.active;\n  rawTask.done = taskInfo.done;\n  rawTask.crit = taskInfo.crit;\n  rawTask.milestone = taskInfo.milestone;\n  rawTask.order = lastOrder;\n  lastOrder++;\n  const pos = rawTasks.push(rawTask);\n  lastTaskID = rawTask.id;\n  taskDb[rawTask.id] = pos - 1;\n}, \"addTask\");\nvar findTaskById = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(id) {\n  const pos = taskDb[id];\n  return rawTasks[pos];\n}, \"findTaskById\");\nvar addTaskOrg = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(descr, data) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: []\n  };\n  const taskInfo = compileData(lastTask, data);\n  newTask.startTime = taskInfo.startTime;\n  newTask.endTime = taskInfo.endTime;\n  newTask.id = taskInfo.id;\n  newTask.active = taskInfo.active;\n  newTask.done = taskInfo.done;\n  newTask.crit = taskInfo.crit;\n  newTask.milestone = taskInfo.milestone;\n  lastTask = newTask;\n  tasks.push(newTask);\n}, \"addTaskOrg\");\nvar compileTasks = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  const compileTask = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(pos) {\n    const task = rawTasks[pos];\n    let startTime = \"\";\n    switch (rawTasks[pos].raw.startTime.type) {\n      case \"prevTaskEnd\": {\n        const prevTask = findTaskById(task.prevTaskId);\n        task.startTime = prevTask.endTime;\n        break;\n      }\n      case \"getStartDate\":\n        startTime = getStartDate(void 0, dateFormat, rawTasks[pos].raw.startTime.startData);\n        if (startTime) {\n          rawTasks[pos].startTime = startTime;\n        }\n        break;\n    }\n    if (rawTasks[pos].startTime) {\n      rawTasks[pos].endTime = getEndDate(\n        rawTasks[pos].startTime,\n        dateFormat,\n        rawTasks[pos].raw.endTime.data,\n        inclusiveEndDates\n      );\n      if (rawTasks[pos].endTime) {\n        rawTasks[pos].processed = true;\n        rawTasks[pos].manualEndTime = dayjs__WEBPACK_IMPORTED_MODULE_3__(\n          rawTasks[pos].raw.endTime.data,\n          \"YYYY-MM-DD\",\n          true\n        ).isValid();\n        checkTaskDates(rawTasks[pos], dateFormat, excludes, includes);\n      }\n    }\n    return rawTasks[pos].processed;\n  }, \"compileTask\");\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n}, \"compileTasks\");\nvar setLink = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(ids, _linkStr) {\n  let linkStr = _linkStr;\n  if ((0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().securityLevel !== \"loose\") {\n    linkStr = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_2__.sanitizeUrl)(_linkStr);\n  }\n  ids.split(\",\").forEach(function(id) {\n    let rawTask = findTaskById(id);\n    if (rawTask !== void 0) {\n      pushFun(id, () => {\n        window.open(linkStr, \"_self\");\n      });\n      links.set(id, linkStr);\n    }\n  });\n  setClass(ids, \"clickable\");\n}, \"setLink\");\nvar setClass = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(ids, className) {\n  ids.split(\",\").forEach(function(id) {\n    let rawTask = findTaskById(id);\n    if (rawTask !== void 0) {\n      rawTask.classes.push(className);\n    }\n  });\n}, \"setClass\");\nvar setClickFun = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(id, functionName, functionArgs) {\n  if ((0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().securityLevel !== \"loose\") {\n    return;\n  }\n  if (functionName === void 0) {\n    return;\n  }\n  let argList = [];\n  if (typeof functionArgs === \"string\") {\n    argList = functionArgs.split(/,(?=(?:(?:[^\"]*\"){2})*[^\"]*$)/);\n    for (let i = 0; i < argList.length; i++) {\n      let item = argList[i].trim();\n      if (item.startsWith('\"') && item.endsWith('\"')) {\n        item = item.substr(1, item.length - 2);\n      }\n      argList[i] = item;\n    }\n  }\n  if (argList.length === 0) {\n    argList.push(id);\n  }\n  let rawTask = findTaskById(id);\n  if (rawTask !== void 0) {\n    pushFun(id, () => {\n      _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_0__.utils_default.runFunc(functionName, ...argList);\n    });\n  }\n}, \"setClickFun\");\nvar pushFun = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(id, callbackFunction) {\n  funs.push(\n    function() {\n      const elem = document.querySelector(`[id=\"${id}\"]`);\n      if (elem !== null) {\n        elem.addEventListener(\"click\", function() {\n          callbackFunction();\n        });\n      }\n    },\n    function() {\n      const elem = document.querySelector(`[id=\"${id}-text\"]`);\n      if (elem !== null) {\n        elem.addEventListener(\"click\", function() {\n          callbackFunction();\n        });\n      }\n    }\n  );\n}, \"pushFun\");\nvar setClickEvent = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(ids, functionName, functionArgs) {\n  ids.split(\",\").forEach(function(id) {\n    setClickFun(id, functionName, functionArgs);\n  });\n  setClass(ids, \"clickable\");\n}, \"setClickEvent\");\nvar bindFunctions = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(element) {\n  funs.forEach(function(fun) {\n    fun(element);\n  });\n}, \"bindFunctions\");\nvar ganttDb_default = {\n  getConfig: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(() => (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().gantt, \"getConfig\"),\n  clear: clear2,\n  setDateFormat,\n  getDateFormat,\n  enableInclusiveEndDates,\n  endDatesAreInclusive,\n  enableTopAxis,\n  topAxisEnabled,\n  setAxisFormat,\n  getAxisFormat,\n  setTickInterval,\n  getTickInterval,\n  setTodayMarker,\n  getTodayMarker,\n  setAccTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.setAccTitle,\n  getAccTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getAccTitle,\n  setDiagramTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.setDiagramTitle,\n  getDiagramTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getDiagramTitle,\n  setDisplayMode,\n  getDisplayMode,\n  setAccDescription: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.setAccDescription,\n  getAccDescription: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getAccDescription,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  findTaskById,\n  addTaskOrg,\n  setIncludes,\n  getIncludes,\n  setExcludes,\n  getExcludes,\n  setClickEvent,\n  setLink,\n  getLinks,\n  bindFunctions,\n  parseDuration,\n  isInvalidDate,\n  setWeekday,\n  getWeekday,\n  setWeekend\n};\nfunction getTaskTags(data, task, tags2) {\n  let matchFound = true;\n  while (matchFound) {\n    matchFound = false;\n    tags2.forEach(function(t) {\n      const pattern = \"^\\\\s*\" + t + \"\\\\s*$\";\n      const regex = new RegExp(pattern);\n      if (data[0].match(regex)) {\n        task[t] = true;\n        data.shift(1);\n        matchFound = true;\n      }\n    });\n  }\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(getTaskTags, \"getTaskTags\");\n\n// src/diagrams/gantt/ganttRenderer.js\n\n\nvar setConf = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function() {\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.log.debug(\"Something is calling, setConf, remove the call\");\n}, \"setConf\");\nvar mapWeekdayToTimeFunction = {\n  monday: d3__WEBPACK_IMPORTED_MODULE_7__.timeMonday,\n  tuesday: d3__WEBPACK_IMPORTED_MODULE_7__.timeTuesday,\n  wednesday: d3__WEBPACK_IMPORTED_MODULE_7__.timeWednesday,\n  thursday: d3__WEBPACK_IMPORTED_MODULE_7__.timeThursday,\n  friday: d3__WEBPACK_IMPORTED_MODULE_7__.timeFriday,\n  saturday: d3__WEBPACK_IMPORTED_MODULE_7__.timeSaturday,\n  sunday: d3__WEBPACK_IMPORTED_MODULE_7__.timeSunday\n};\nvar getMaxIntersections = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)((tasks2, orderOffset) => {\n  let timeline = [...tasks2].map(() => -Infinity);\n  let sorted = [...tasks2].sort((a, b) => a.startTime - b.startTime || a.order - b.order);\n  let maxIntersections = 0;\n  for (const element of sorted) {\n    for (let j = 0; j < timeline.length; j++) {\n      if (element.startTime >= timeline[j]) {\n        timeline[j] = element.endTime;\n        element.order = j + orderOffset;\n        if (j > maxIntersections) {\n          maxIntersections = j;\n        }\n        break;\n      }\n    }\n  }\n  return maxIntersections;\n}, \"getMaxIntersections\");\nvar w;\nvar draw = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(function(text, id, version, diagObj) {\n  const conf = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().gantt;\n  const securityLevel = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_7__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_7__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_7__.select)(\"body\");\n  const doc = securityLevel === \"sandbox\" ? sandboxElement.nodes()[0].contentDocument : document;\n  const elem = doc.getElementById(id);\n  w = elem.parentElement.offsetWidth;\n  if (w === void 0) {\n    w = 1200;\n  }\n  if (conf.useWidth !== void 0) {\n    w = conf.useWidth;\n  }\n  const taskArray = diagObj.db.getTasks();\n  let categories = [];\n  for (const element of taskArray) {\n    categories.push(element.type);\n  }\n  categories = checkUnique(categories);\n  const categoryHeights = {};\n  let h = 2 * conf.topPadding;\n  if (diagObj.db.getDisplayMode() === \"compact\" || conf.displayMode === \"compact\") {\n    const categoryElements = {};\n    for (const element of taskArray) {\n      if (categoryElements[element.section] === void 0) {\n        categoryElements[element.section] = [element];\n      } else {\n        categoryElements[element.section].push(element);\n      }\n    }\n    let intersections = 0;\n    for (const category of Object.keys(categoryElements)) {\n      const categoryHeight = getMaxIntersections(categoryElements[category], intersections) + 1;\n      intersections += categoryHeight;\n      h += categoryHeight * (conf.barHeight + conf.barGap);\n      categoryHeights[category] = categoryHeight;\n    }\n  } else {\n    h += taskArray.length * (conf.barHeight + conf.barGap);\n    for (const category of categories) {\n      categoryHeights[category] = taskArray.filter((task) => task.type === category).length;\n    }\n  }\n  elem.setAttribute(\"viewBox\", \"0 0 \" + w + \" \" + h);\n  const svg = root.select(`[id=\"${id}\"]`);\n  const timeScale = (0,d3__WEBPACK_IMPORTED_MODULE_7__.scaleTime)().domain([\n    (0,d3__WEBPACK_IMPORTED_MODULE_7__.min)(taskArray, function(d) {\n      return d.startTime;\n    }),\n    (0,d3__WEBPACK_IMPORTED_MODULE_7__.max)(taskArray, function(d) {\n      return d.endTime;\n    })\n  ]).rangeRound([0, w - conf.leftPadding - conf.rightPadding]);\n  function taskCompare(a, b) {\n    const taskA = a.startTime;\n    const taskB = b.startTime;\n    let result = 0;\n    if (taskA > taskB) {\n      result = 1;\n    } else if (taskA < taskB) {\n      result = -1;\n    }\n    return result;\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(taskCompare, \"taskCompare\");\n  taskArray.sort(taskCompare);\n  makeGantt(taskArray, w, h);\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.configureSvgSize)(svg, h, w, conf.useMaxWidth);\n  svg.append(\"text\").text(diagObj.db.getDiagramTitle()).attr(\"x\", w / 2).attr(\"y\", conf.titleTopMargin).attr(\"class\", \"titleText\");\n  function makeGantt(tasks2, pageWidth, pageHeight) {\n    const barHeight = conf.barHeight;\n    const gap = barHeight + conf.barGap;\n    const topPadding = conf.topPadding;\n    const leftPadding = conf.leftPadding;\n    const colorScale = (0,d3__WEBPACK_IMPORTED_MODULE_7__.scaleLinear)().domain([0, categories.length]).range([\"#00B9FA\", \"#F95002\"]).interpolate(d3__WEBPACK_IMPORTED_MODULE_7__.interpolateHcl);\n    drawExcludeDays(\n      gap,\n      topPadding,\n      leftPadding,\n      pageWidth,\n      pageHeight,\n      tasks2,\n      diagObj.db.getExcludes(),\n      diagObj.db.getIncludes()\n    );\n    makeGrid(leftPadding, topPadding, pageWidth, pageHeight);\n    drawRects(tasks2, gap, topPadding, leftPadding, barHeight, colorScale, pageWidth, pageHeight);\n    vertLabels(gap, topPadding, leftPadding, barHeight, colorScale);\n    drawToday(leftPadding, topPadding, pageWidth, pageHeight);\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(makeGantt, \"makeGantt\");\n  function drawRects(theArray, theGap, theTopPad, theSidePad, theBarHeight, theColorScale, w2) {\n    const uniqueTaskOrderIds = [...new Set(theArray.map((item) => item.order))];\n    const uniqueTasks = uniqueTaskOrderIds.map((id2) => theArray.find((item) => item.order === id2));\n    svg.append(\"g\").selectAll(\"rect\").data(uniqueTasks).enter().append(\"rect\").attr(\"x\", 0).attr(\"y\", function(d, i) {\n      i = d.order;\n      return i * theGap + theTopPad - 2;\n    }).attr(\"width\", function() {\n      return w2 - conf.rightPadding / 2;\n    }).attr(\"height\", theGap).attr(\"class\", function(d) {\n      for (const [i, category] of categories.entries()) {\n        if (d.type === category) {\n          return \"section section\" + i % conf.numberSectionStyles;\n        }\n      }\n      return \"section section0\";\n    });\n    const rectangles = svg.append(\"g\").selectAll(\"rect\").data(theArray).enter();\n    const links2 = diagObj.db.getLinks();\n    rectangles.append(\"rect\").attr(\"id\", function(d) {\n      return d.id;\n    }).attr(\"rx\", 3).attr(\"ry\", 3).attr(\"x\", function(d) {\n      if (d.milestone) {\n        return timeScale(d.startTime) + theSidePad + 0.5 * (timeScale(d.endTime) - timeScale(d.startTime)) - 0.5 * theBarHeight;\n      }\n      return timeScale(d.startTime) + theSidePad;\n    }).attr(\"y\", function(d, i) {\n      i = d.order;\n      return i * theGap + theTopPad;\n    }).attr(\"width\", function(d) {\n      if (d.milestone) {\n        return theBarHeight;\n      }\n      return timeScale(d.renderEndTime || d.endTime) - timeScale(d.startTime);\n    }).attr(\"height\", theBarHeight).attr(\"transform-origin\", function(d, i) {\n      i = d.order;\n      return (timeScale(d.startTime) + theSidePad + 0.5 * (timeScale(d.endTime) - timeScale(d.startTime))).toString() + \"px \" + (i * theGap + theTopPad + 0.5 * theBarHeight).toString() + \"px\";\n    }).attr(\"class\", function(d) {\n      const res = \"task\";\n      let classStr = \"\";\n      if (d.classes.length > 0) {\n        classStr = d.classes.join(\" \");\n      }\n      let secNum = 0;\n      for (const [i, category] of categories.entries()) {\n        if (d.type === category) {\n          secNum = i % conf.numberSectionStyles;\n        }\n      }\n      let taskClass = \"\";\n      if (d.active) {\n        if (d.crit) {\n          taskClass += \" activeCrit\";\n        } else {\n          taskClass = \" active\";\n        }\n      } else if (d.done) {\n        if (d.crit) {\n          taskClass = \" doneCrit\";\n        } else {\n          taskClass = \" done\";\n        }\n      } else {\n        if (d.crit) {\n          taskClass += \" crit\";\n        }\n      }\n      if (taskClass.length === 0) {\n        taskClass = \" task\";\n      }\n      if (d.milestone) {\n        taskClass = \" milestone \" + taskClass;\n      }\n      taskClass += secNum;\n      taskClass += \" \" + classStr;\n      return res + taskClass;\n    });\n    rectangles.append(\"text\").attr(\"id\", function(d) {\n      return d.id + \"-text\";\n    }).text(function(d) {\n      return d.task;\n    }).attr(\"font-size\", conf.fontSize).attr(\"x\", function(d) {\n      let startX = timeScale(d.startTime);\n      let endX = timeScale(d.renderEndTime || d.endTime);\n      if (d.milestone) {\n        startX += 0.5 * (timeScale(d.endTime) - timeScale(d.startTime)) - 0.5 * theBarHeight;\n      }\n      if (d.milestone) {\n        endX = startX + theBarHeight;\n      }\n      const textWidth = this.getBBox().width;\n      if (textWidth > endX - startX) {\n        if (endX + textWidth + 1.5 * conf.leftPadding > w2) {\n          return startX + theSidePad - 5;\n        } else {\n          return endX + theSidePad + 5;\n        }\n      } else {\n        return (endX - startX) / 2 + startX + theSidePad;\n      }\n    }).attr(\"y\", function(d, i) {\n      i = d.order;\n      return i * theGap + conf.barHeight / 2 + (conf.fontSize / 2 - 2) + theTopPad;\n    }).attr(\"text-height\", theBarHeight).attr(\"class\", function(d) {\n      const startX = timeScale(d.startTime);\n      let endX = timeScale(d.endTime);\n      if (d.milestone) {\n        endX = startX + theBarHeight;\n      }\n      const textWidth = this.getBBox().width;\n      let classStr = \"\";\n      if (d.classes.length > 0) {\n        classStr = d.classes.join(\" \");\n      }\n      let secNum = 0;\n      for (const [i, category] of categories.entries()) {\n        if (d.type === category) {\n          secNum = i % conf.numberSectionStyles;\n        }\n      }\n      let taskType = \"\";\n      if (d.active) {\n        if (d.crit) {\n          taskType = \"activeCritText\" + secNum;\n        } else {\n          taskType = \"activeText\" + secNum;\n        }\n      }\n      if (d.done) {\n        if (d.crit) {\n          taskType = taskType + \" doneCritText\" + secNum;\n        } else {\n          taskType = taskType + \" doneText\" + secNum;\n        }\n      } else {\n        if (d.crit) {\n          taskType = taskType + \" critText\" + secNum;\n        }\n      }\n      if (d.milestone) {\n        taskType += \" milestoneText\";\n      }\n      if (textWidth > endX - startX) {\n        if (endX + textWidth + 1.5 * conf.leftPadding > w2) {\n          return classStr + \" taskTextOutsideLeft taskTextOutside\" + secNum + \" \" + taskType;\n        } else {\n          return classStr + \" taskTextOutsideRight taskTextOutside\" + secNum + \" \" + taskType + \" width-\" + textWidth;\n        }\n      } else {\n        return classStr + \" taskText taskText\" + secNum + \" \" + taskType + \" width-\" + textWidth;\n      }\n    });\n    const securityLevel2 = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.getConfig2)().securityLevel;\n    if (securityLevel2 === \"sandbox\") {\n      let sandboxElement2;\n      sandboxElement2 = (0,d3__WEBPACK_IMPORTED_MODULE_7__.select)(\"#i\" + id);\n      const doc2 = sandboxElement2.nodes()[0].contentDocument;\n      rectangles.filter(function(d) {\n        return links2.has(d.id);\n      }).each(function(o) {\n        var taskRect = doc2.querySelector(\"#\" + o.id);\n        var taskText = doc2.querySelector(\"#\" + o.id + \"-text\");\n        const oldParent = taskRect.parentNode;\n        var Link = doc2.createElement(\"a\");\n        Link.setAttribute(\"xlink:href\", links2.get(o.id));\n        Link.setAttribute(\"target\", \"_top\");\n        oldParent.appendChild(Link);\n        Link.appendChild(taskRect);\n        Link.appendChild(taskText);\n      });\n    }\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(drawRects, \"drawRects\");\n  function drawExcludeDays(theGap, theTopPad, theSidePad, w2, h2, tasks2, excludes2, includes2) {\n    if (excludes2.length === 0 && includes2.length === 0) {\n      return;\n    }\n    let minTime;\n    let maxTime;\n    for (const { startTime, endTime } of tasks2) {\n      if (minTime === void 0 || startTime < minTime) {\n        minTime = startTime;\n      }\n      if (maxTime === void 0 || endTime > maxTime) {\n        maxTime = endTime;\n      }\n    }\n    if (!minTime || !maxTime) {\n      return;\n    }\n    if (dayjs__WEBPACK_IMPORTED_MODULE_3__(maxTime).diff(dayjs__WEBPACK_IMPORTED_MODULE_3__(minTime), \"year\") > 5) {\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.log.warn(\n        \"The difference between the min and max time is more than 5 years. This will cause performance issues. Skipping drawing exclude days.\"\n      );\n      return;\n    }\n    const dateFormat2 = diagObj.db.getDateFormat();\n    const excludeRanges = [];\n    let range = null;\n    let d = dayjs__WEBPACK_IMPORTED_MODULE_3__(minTime);\n    while (d.valueOf() <= maxTime) {\n      if (diagObj.db.isInvalidDate(d, dateFormat2, excludes2, includes2)) {\n        if (!range) {\n          range = {\n            start: d,\n            end: d\n          };\n        } else {\n          range.end = d;\n        }\n      } else {\n        if (range) {\n          excludeRanges.push(range);\n          range = null;\n        }\n      }\n      d = d.add(1, \"d\");\n    }\n    const rectangles = svg.append(\"g\").selectAll(\"rect\").data(excludeRanges).enter();\n    rectangles.append(\"rect\").attr(\"id\", function(d2) {\n      return \"exclude-\" + d2.start.format(\"YYYY-MM-DD\");\n    }).attr(\"x\", function(d2) {\n      return timeScale(d2.start) + theSidePad;\n    }).attr(\"y\", conf.gridLineStartPadding).attr(\"width\", function(d2) {\n      const renderEnd = d2.end.add(1, \"day\");\n      return timeScale(renderEnd) - timeScale(d2.start);\n    }).attr(\"height\", h2 - theTopPad - conf.gridLineStartPadding).attr(\"transform-origin\", function(d2, i) {\n      return (timeScale(d2.start) + theSidePad + 0.5 * (timeScale(d2.end) - timeScale(d2.start))).toString() + \"px \" + (i * theGap + 0.5 * h2).toString() + \"px\";\n    }).attr(\"class\", \"exclude-range\");\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(drawExcludeDays, \"drawExcludeDays\");\n  function makeGrid(theSidePad, theTopPad, w2, h2) {\n    let bottomXAxis = (0,d3__WEBPACK_IMPORTED_MODULE_7__.axisBottom)(timeScale).tickSize(-h2 + theTopPad + conf.gridLineStartPadding).tickFormat((0,d3__WEBPACK_IMPORTED_MODULE_7__.timeFormat)(diagObj.db.getAxisFormat() || conf.axisFormat || \"%Y-%m-%d\"));\n    const reTickInterval = /^([1-9]\\d*)(millisecond|second|minute|hour|day|week|month)$/;\n    const resultTickInterval = reTickInterval.exec(\n      diagObj.db.getTickInterval() || conf.tickInterval\n    );\n    if (resultTickInterval !== null) {\n      const every = resultTickInterval[1];\n      const interval = resultTickInterval[2];\n      const weekday2 = diagObj.db.getWeekday() || conf.weekday;\n      switch (interval) {\n        case \"millisecond\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMillisecond.every(every));\n          break;\n        case \"second\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeSecond.every(every));\n          break;\n        case \"minute\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMinute.every(every));\n          break;\n        case \"hour\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeHour.every(every));\n          break;\n        case \"day\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeDay.every(every));\n          break;\n        case \"week\":\n          bottomXAxis.ticks(mapWeekdayToTimeFunction[weekday2].every(every));\n          break;\n        case \"month\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMonth.every(every));\n          break;\n      }\n    }\n    svg.append(\"g\").attr(\"class\", \"grid\").attr(\"transform\", \"translate(\" + theSidePad + \", \" + (h2 - 50) + \")\").call(bottomXAxis).selectAll(\"text\").style(\"text-anchor\", \"middle\").attr(\"fill\", \"#000\").attr(\"stroke\", \"none\").attr(\"font-size\", 10).attr(\"dy\", \"1em\");\n    if (diagObj.db.topAxisEnabled() || conf.topAxis) {\n      let topXAxis = (0,d3__WEBPACK_IMPORTED_MODULE_7__.axisTop)(timeScale).tickSize(-h2 + theTopPad + conf.gridLineStartPadding).tickFormat((0,d3__WEBPACK_IMPORTED_MODULE_7__.timeFormat)(diagObj.db.getAxisFormat() || conf.axisFormat || \"%Y-%m-%d\"));\n      if (resultTickInterval !== null) {\n        const every = resultTickInterval[1];\n        const interval = resultTickInterval[2];\n        const weekday2 = diagObj.db.getWeekday() || conf.weekday;\n        switch (interval) {\n          case \"millisecond\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMillisecond.every(every));\n            break;\n          case \"second\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeSecond.every(every));\n            break;\n          case \"minute\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMinute.every(every));\n            break;\n          case \"hour\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeHour.every(every));\n            break;\n          case \"day\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeDay.every(every));\n            break;\n          case \"week\":\n            topXAxis.ticks(mapWeekdayToTimeFunction[weekday2].every(every));\n            break;\n          case \"month\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_7__.timeMonth.every(every));\n            break;\n        }\n      }\n      svg.append(\"g\").attr(\"class\", \"grid\").attr(\"transform\", \"translate(\" + theSidePad + \", \" + theTopPad + \")\").call(topXAxis).selectAll(\"text\").style(\"text-anchor\", \"middle\").attr(\"fill\", \"#000\").attr(\"stroke\", \"none\").attr(\"font-size\", 10);\n    }\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(makeGrid, \"makeGrid\");\n  function vertLabels(theGap, theTopPad) {\n    let prevGap = 0;\n    const numOccurrences = Object.keys(categoryHeights).map((d) => [d, categoryHeights[d]]);\n    svg.append(\"g\").selectAll(\"text\").data(numOccurrences).enter().append(function(d) {\n      const rows = d[0].split(_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.common_default.lineBreakRegex);\n      const dy = -(rows.length - 1) / 2;\n      const svgLabel = doc.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n      svgLabel.setAttribute(\"dy\", dy + \"em\");\n      for (const [j, row] of rows.entries()) {\n        const tspan = doc.createElementNS(\"http://www.w3.org/2000/svg\", \"tspan\");\n        tspan.setAttribute(\"alignment-baseline\", \"central\");\n        tspan.setAttribute(\"x\", \"10\");\n        if (j > 0) {\n          tspan.setAttribute(\"dy\", \"1em\");\n        }\n        tspan.textContent = row;\n        svgLabel.appendChild(tspan);\n      }\n      return svgLabel;\n    }).attr(\"x\", 10).attr(\"y\", function(d, i) {\n      if (i > 0) {\n        for (let j = 0; j < i; j++) {\n          prevGap += numOccurrences[i - 1][1];\n          return d[1] * theGap / 2 + prevGap * theGap + theTopPad;\n        }\n      } else {\n        return d[1] * theGap / 2 + theTopPad;\n      }\n    }).attr(\"font-size\", conf.sectionFontSize).attr(\"class\", function(d) {\n      for (const [i, category] of categories.entries()) {\n        if (d[0] === category) {\n          return \"sectionTitle sectionTitle\" + i % conf.numberSectionStyles;\n        }\n      }\n      return \"sectionTitle\";\n    });\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(vertLabels, \"vertLabels\");\n  function drawToday(theSidePad, theTopPad, w2, h2) {\n    const todayMarker2 = diagObj.db.getTodayMarker();\n    if (todayMarker2 === \"off\") {\n      return;\n    }\n    const todayG = svg.append(\"g\").attr(\"class\", \"today\");\n    const today = /* @__PURE__ */ new Date();\n    const todayLine = todayG.append(\"line\");\n    todayLine.attr(\"x1\", timeScale(today) + theSidePad).attr(\"x2\", timeScale(today) + theSidePad).attr(\"y1\", conf.titleTopMargin).attr(\"y2\", h2 - conf.titleTopMargin).attr(\"class\", \"today\");\n    if (todayMarker2 !== \"\") {\n      todayLine.attr(\"style\", todayMarker2.replace(/,/g, \";\"));\n    }\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(drawToday, \"drawToday\");\n  function checkUnique(arr) {\n    const hash = {};\n    const result = [];\n    for (let i = 0, l = arr.length; i < l; ++i) {\n      if (!Object.prototype.hasOwnProperty.call(hash, arr[i])) {\n        hash[arr[i]] = true;\n        result.push(arr[i]);\n      }\n    }\n    return result;\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)(checkUnique, \"checkUnique\");\n}, \"draw\");\nvar ganttRenderer_default = {\n  setConf,\n  draw\n};\n\n// src/diagrams/gantt/styles.js\nvar getStyles = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_1__.__name)((options) => `\n  .mermaid-main-font {\n        font-family: ${options.fontFamily};\n  }\n\n  .exclude-range {\n    fill: ${options.excludeBkgColor};\n  }\n\n  .section {\n    stroke: none;\n    opacity: 0.2;\n  }\n\n  .section0 {\n    fill: ${options.sectionBkgColor};\n  }\n\n  .section2 {\n    fill: ${options.sectionBkgColor2};\n  }\n\n  .section1,\n  .section3 {\n    fill: ${options.altSectionBkgColor};\n    opacity: 0.2;\n  }\n\n  .sectionTitle0 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle1 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle2 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle3 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle {\n    text-anchor: start;\n    font-family: ${options.fontFamily};\n  }\n\n\n  /* Grid and axis */\n\n  .grid .tick {\n    stroke: ${options.gridColor};\n    opacity: 0.8;\n    shape-rendering: crispEdges;\n  }\n\n  .grid .tick text {\n    font-family: ${options.fontFamily};\n    fill: ${options.textColor};\n  }\n\n  .grid path {\n    stroke-width: 0;\n  }\n\n\n  /* Today line */\n\n  .today {\n    fill: none;\n    stroke: ${options.todayLineColor};\n    stroke-width: 2px;\n  }\n\n\n  /* Task styling */\n\n  /* Default task */\n\n  .task {\n    stroke-width: 2;\n  }\n\n  .taskText {\n    text-anchor: middle;\n    font-family: ${options.fontFamily};\n  }\n\n  .taskTextOutsideRight {\n    fill: ${options.taskTextDarkColor};\n    text-anchor: start;\n    font-family: ${options.fontFamily};\n  }\n\n  .taskTextOutsideLeft {\n    fill: ${options.taskTextDarkColor};\n    text-anchor: end;\n  }\n\n\n  /* Special case clickable */\n\n  .task.clickable {\n    cursor: pointer;\n  }\n\n  .taskText.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideLeft.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideRight.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n\n  /* Specific task settings for the sections*/\n\n  .taskText0,\n  .taskText1,\n  .taskText2,\n  .taskText3 {\n    fill: ${options.taskTextColor};\n  }\n\n  .task0,\n  .task1,\n  .task2,\n  .task3 {\n    fill: ${options.taskBkgColor};\n    stroke: ${options.taskBorderColor};\n  }\n\n  .taskTextOutside0,\n  .taskTextOutside2\n  {\n    fill: ${options.taskTextOutsideColor};\n  }\n\n  .taskTextOutside1,\n  .taskTextOutside3 {\n    fill: ${options.taskTextOutsideColor};\n  }\n\n\n  /* Active task */\n\n  .active0,\n  .active1,\n  .active2,\n  .active3 {\n    fill: ${options.activeTaskBkgColor};\n    stroke: ${options.activeTaskBorderColor};\n  }\n\n  .activeText0,\n  .activeText1,\n  .activeText2,\n  .activeText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n\n  /* Completed task */\n\n  .done0,\n  .done1,\n  .done2,\n  .done3 {\n    stroke: ${options.doneTaskBorderColor};\n    fill: ${options.doneTaskBkgColor};\n    stroke-width: 2;\n  }\n\n  .doneText0,\n  .doneText1,\n  .doneText2,\n  .doneText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n\n  /* Tasks on the critical line */\n\n  .crit0,\n  .crit1,\n  .crit2,\n  .crit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.critBkgColor};\n    stroke-width: 2;\n  }\n\n  .activeCrit0,\n  .activeCrit1,\n  .activeCrit2,\n  .activeCrit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.activeTaskBkgColor};\n    stroke-width: 2;\n  }\n\n  .doneCrit0,\n  .doneCrit1,\n  .doneCrit2,\n  .doneCrit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.doneTaskBkgColor};\n    stroke-width: 2;\n    cursor: pointer;\n    shape-rendering: crispEdges;\n  }\n\n  .milestone {\n    transform: rotate(45deg) scale(0.8,0.8);\n  }\n\n  .milestoneText {\n    font-style: italic;\n  }\n  .doneCritText0,\n  .doneCritText1,\n  .doneCritText2,\n  .doneCritText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n  .activeCritText0,\n  .activeCritText1,\n  .activeCritText2,\n  .activeCritText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n  .titleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options.titleColor || options.textColor};\n    font-family: ${options.fontFamily};\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/gantt/ganttDiagram.ts\nvar diagram = {\n  parser: gantt_default,\n  db: ganttDb_default,\n  renderer: ganttRenderer_default,\n  styles: styles_default\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/ganttDiagram-APWFNJXF.mjs\n"));

/***/ })

}]);