from django.contrib import admin
from django.urls import path, include
from documents.api import (
    get_or_generate_flowchart, get_or_generate_flashcards, generate_quiz, generate_chat, process_blueprint_direct
)

urlpatterns = [
    path('admin/', admin.site.urls),
    # Original routes without /api prefix (keeping for backward compatibility)
    path('users/', include('users.urls')),
    path('chat/', include('chat.urls')),
    path('documents/', include('documents.urls')),
    # Routes with /api prefix for frontend
    path('api/users/', include('users.urls')),
    path('api/chat/', include('chat.urls')),
    path('api/documents/', include('documents.urls')),
    path('api/payments/', include('payments.urls')),
    path('api/flashcards/<int:document_id>/', get_or_generate_flashcards, name='get-or-generate-flashcards'),
    path('api/flowchart/<int:document_id>/', get_or_generate_flowchart, name='get-or-generate-flowchart'),
    path('api/quizzes/<int:document_id>/', generate_quiz, name='generate-quiz'),
    path('api/chats/<int:document_id>/', generate_chat, name='generate-chat'),
    path('api/process-blueprint/<int:document_id>/', process_blueprint_direct, name='process-blueprint'),
]