"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_chunks_mermaid_core_requirementDiagram-KVF5MWMF_mjs"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDiagramElement: () => (/* binding */ getDiagramElement),\n/* harmony export */   setupViewPortForSVG: () => (/* binding */ setupViewPortForSVG)\n/* harmony export */ });\n/* harmony import */ var _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-YTJNT7DU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-YTJNT7DU.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n\n\n// src/rendering-util/insertElementsForSize.js\n\nvar getDiagramElement = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((id, securityLevel) => {\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  return svg;\n}, \"getDiagramElement\");\n\n// src/rendering-util/setupViewPortForSVG.ts\nvar setupViewPortForSVG = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((svg, padding, cssDiagram, useMaxWidth) => {\n  svg.attr(\"class\", cssDiagram);\n  const { width, height, x, y } = calculateDimensionsWithPadding(svg, padding);\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.configureSvgSize)(svg, height, width, useMaxWidth);\n  const viewBox = createViewBox(x, y, width, height, padding);\n  svg.attr(\"viewBox\", viewBox);\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(`viewBox configured: ${viewBox} with padding: ${padding}`);\n}, \"setupViewPortForSVG\");\nvar calculateDimensionsWithPadding = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((svg, padding) => {\n  const bounds = svg.node()?.getBBox() || { width: 0, height: 0, x: 0, y: 0 };\n  return {\n    width: bounds.width + padding * 2,\n    height: bounds.height + padding * 2,\n    x: bounds.x,\n    y: bounds.y\n  };\n}, \"calculateDimensionsWithPadding\");\nvar createViewBox = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((x, y, width, height, padding) => {\n  return `${x - padding} ${y - padding} ${width} ${height}`;\n}, \"createViewBox\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZXJtYWlkL2Rpc3QvY2h1bmtzL21lcm1haWQuY29yZS9jaHVuay1SWjVCT1pFMi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUk4Qjs7QUFFOUI7QUFDNEI7QUFDNUIsd0NBQXdDLDJEQUFNO0FBQzlDO0FBQ0E7QUFDQSxxQkFBcUIsMENBQU07QUFDM0I7QUFDQSw2Q0FBNkMsMENBQU0sbURBQW1ELDBDQUFNO0FBQzVHLGtDQUFrQyxHQUFHO0FBQ3JDO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBLDBDQUEwQywyREFBTTtBQUNoRDtBQUNBLFVBQVUsc0JBQXNCO0FBQ2hDLEVBQUUscUVBQWdCO0FBQ2xCO0FBQ0E7QUFDQSxFQUFFLG9EQUFHLDhCQUE4QixTQUFTLGdCQUFnQixRQUFRO0FBQ3BFLENBQUM7QUFDRCxxREFBcUQsMkRBQU07QUFDM0QsNENBQTRDO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCxvQ0FBb0MsMkRBQU07QUFDMUMsWUFBWSxhQUFhLEVBQUUsYUFBYSxFQUFFLE9BQU8sRUFBRSxPQUFPO0FBQzFELENBQUM7O0FBS0MiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbWVybWFpZFxcZGlzdFxcY2h1bmtzXFxtZXJtYWlkLmNvcmVcXGNodW5rLVJaNUJPWkUyLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBfX25hbWUsXG4gIGNvbmZpZ3VyZVN2Z1NpemUsXG4gIGxvZ1xufSBmcm9tIFwiLi9jaHVuay1ZVEpOVDdEVS5tanNcIjtcblxuLy8gc3JjL3JlbmRlcmluZy11dGlsL2luc2VydEVsZW1lbnRzRm9yU2l6ZS5qc1xuaW1wb3J0IHsgc2VsZWN0IH0gZnJvbSBcImQzXCI7XG52YXIgZ2V0RGlhZ3JhbUVsZW1lbnQgPSAvKiBAX19QVVJFX18gKi8gX19uYW1lKChpZCwgc2VjdXJpdHlMZXZlbCkgPT4ge1xuICBsZXQgc2FuZGJveEVsZW1lbnQ7XG4gIGlmIChzZWN1cml0eUxldmVsID09PSBcInNhbmRib3hcIikge1xuICAgIHNhbmRib3hFbGVtZW50ID0gc2VsZWN0KFwiI2lcIiArIGlkKTtcbiAgfVxuICBjb25zdCByb290ID0gc2VjdXJpdHlMZXZlbCA9PT0gXCJzYW5kYm94XCIgPyBzZWxlY3Qoc2FuZGJveEVsZW1lbnQubm9kZXMoKVswXS5jb250ZW50RG9jdW1lbnQuYm9keSkgOiBzZWxlY3QoXCJib2R5XCIpO1xuICBjb25zdCBzdmcgPSByb290LnNlbGVjdChgW2lkPVwiJHtpZH1cIl1gKTtcbiAgcmV0dXJuIHN2Zztcbn0sIFwiZ2V0RGlhZ3JhbUVsZW1lbnRcIik7XG5cbi8vIHNyYy9yZW5kZXJpbmctdXRpbC9zZXR1cFZpZXdQb3J0Rm9yU1ZHLnRzXG52YXIgc2V0dXBWaWV3UG9ydEZvclNWRyA9IC8qIEBfX1BVUkVfXyAqLyBfX25hbWUoKHN2ZywgcGFkZGluZywgY3NzRGlhZ3JhbSwgdXNlTWF4V2lkdGgpID0+IHtcbiAgc3ZnLmF0dHIoXCJjbGFzc1wiLCBjc3NEaWFncmFtKTtcbiAgY29uc3QgeyB3aWR0aCwgaGVpZ2h0LCB4LCB5IH0gPSBjYWxjdWxhdGVEaW1lbnNpb25zV2l0aFBhZGRpbmcoc3ZnLCBwYWRkaW5nKTtcbiAgY29uZmlndXJlU3ZnU2l6ZShzdmcsIGhlaWdodCwgd2lkdGgsIHVzZU1heFdpZHRoKTtcbiAgY29uc3Qgdmlld0JveCA9IGNyZWF0ZVZpZXdCb3goeCwgeSwgd2lkdGgsIGhlaWdodCwgcGFkZGluZyk7XG4gIHN2Zy5hdHRyKFwidmlld0JveFwiLCB2aWV3Qm94KTtcbiAgbG9nLmRlYnVnKGB2aWV3Qm94IGNvbmZpZ3VyZWQ6ICR7dmlld0JveH0gd2l0aCBwYWRkaW5nOiAke3BhZGRpbmd9YCk7XG59LCBcInNldHVwVmlld1BvcnRGb3JTVkdcIik7XG52YXIgY2FsY3VsYXRlRGltZW5zaW9uc1dpdGhQYWRkaW5nID0gLyogQF9fUFVSRV9fICovIF9fbmFtZSgoc3ZnLCBwYWRkaW5nKSA9PiB7XG4gIGNvbnN0IGJvdW5kcyA9IHN2Zy5ub2RlKCk/LmdldEJCb3goKSB8fCB7IHdpZHRoOiAwLCBoZWlnaHQ6IDAsIHg6IDAsIHk6IDAgfTtcbiAgcmV0dXJuIHtcbiAgICB3aWR0aDogYm91bmRzLndpZHRoICsgcGFkZGluZyAqIDIsXG4gICAgaGVpZ2h0OiBib3VuZHMuaGVpZ2h0ICsgcGFkZGluZyAqIDIsXG4gICAgeDogYm91bmRzLngsXG4gICAgeTogYm91bmRzLnlcbiAgfTtcbn0sIFwiY2FsY3VsYXRlRGltZW5zaW9uc1dpdGhQYWRkaW5nXCIpO1xudmFyIGNyZWF0ZVZpZXdCb3ggPSAvKiBAX19QVVJFX18gKi8gX19uYW1lKCh4LCB5LCB3aWR0aCwgaGVpZ2h0LCBwYWRkaW5nKSA9PiB7XG4gIHJldHVybiBgJHt4IC0gcGFkZGluZ30gJHt5IC0gcGFkZGluZ30gJHt3aWR0aH0gJHtoZWlnaHR9YDtcbn0sIFwiY3JlYXRlVmlld0JveFwiKTtcblxuZXhwb3J0IHtcbiAgZ2V0RGlhZ3JhbUVsZW1lbnQsXG4gIHNldHVwVmlld1BvcnRGb3JTVkdcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/requirementDiagram-KVF5MWMF.mjs":
/*!***************************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/requirementDiagram-KVF5MWMF.mjs ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: () => (/* binding */ diagram)\n/* harmony export */ });\n/* harmony import */ var _chunk_RZ5BOZE2_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-RZ5BOZE2.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-RZ5BOZE2.mjs\");\n/* harmony import */ var _chunk_TYCBKAJE_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-TYCBKAJE.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-TYCBKAJE.mjs\");\n/* harmony import */ var _chunk_IIMUDSI4_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-IIMUDSI4.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-IIMUDSI4.mjs\");\n/* harmony import */ var _chunk_VV3M67IP_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-VV3M67IP.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-VV3M67IP.mjs\");\n/* harmony import */ var _chunk_HRU6DDCH_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-HRU6DDCH.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-HRU6DDCH.mjs\");\n/* harmony import */ var _chunk_K557N5IZ_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-K557N5IZ.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-K557N5IZ.mjs\");\n/* harmony import */ var _chunk_H2D2JQ3I_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./chunk-H2D2JQ3I.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-H2D2JQ3I.mjs\");\n/* harmony import */ var _chunk_C3MQ5ANM_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./chunk-C3MQ5ANM.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-C3MQ5ANM.mjs\");\n/* harmony import */ var _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./chunk-O4NI6UNU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-O4NI6UNU.mjs\");\n/* harmony import */ var _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./chunk-YTJNT7DU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-YTJNT7DU.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n// src/diagrams/requirement/parser/requirementDiagram.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 3], $V1 = [1, 4], $V2 = [1, 5], $V3 = [1, 6], $V4 = [5, 6, 8, 9, 11, 13, 21, 22, 23, 24, 41, 42, 43, 44, 45, 46, 54, 72, 74, 77, 89, 90], $V5 = [1, 22], $V6 = [2, 7], $V7 = [1, 26], $V8 = [1, 27], $V9 = [1, 28], $Va = [1, 29], $Vb = [1, 33], $Vc = [1, 34], $Vd = [1, 35], $Ve = [1, 36], $Vf = [1, 37], $Vg = [1, 38], $Vh = [1, 24], $Vi = [1, 31], $Vj = [1, 32], $Vk = [1, 30], $Vl = [1, 39], $Vm = [1, 40], $Vn = [5, 8, 9, 11, 13, 21, 22, 23, 24, 41, 42, 43, 44, 45, 46, 54, 72, 74, 77, 89, 90], $Vo = [1, 61], $Vp = [89, 90], $Vq = [5, 8, 9, 11, 13, 21, 22, 23, 24, 27, 29, 41, 42, 43, 44, 45, 46, 54, 61, 63, 72, 74, 75, 76, 77, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90], $Vr = [27, 29], $Vs = [1, 70], $Vt = [1, 71], $Vu = [1, 72], $Vv = [1, 73], $Vw = [1, 74], $Vx = [1, 75], $Vy = [1, 76], $Vz = [1, 83], $VA = [1, 80], $VB = [1, 84], $VC = [1, 85], $VD = [1, 86], $VE = [1, 87], $VF = [1, 88], $VG = [1, 89], $VH = [1, 90], $VI = [1, 91], $VJ = [1, 92], $VK = [5, 8, 9, 11, 13, 21, 22, 23, 24, 27, 41, 42, 43, 44, 45, 46, 54, 72, 74, 75, 76, 77, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90], $VL = [63, 64], $VM = [1, 101], $VN = [5, 8, 9, 11, 13, 21, 22, 23, 24, 41, 42, 43, 44, 45, 46, 54, 72, 74, 76, 77, 89, 90], $VO = [5, 8, 9, 11, 13, 21, 22, 23, 24, 41, 42, 43, 44, 45, 46, 54, 72, 74, 75, 76, 77, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90], $VP = [1, 110], $VQ = [1, 106], $VR = [1, 107], $VS = [1, 108], $VT = [1, 109], $VU = [1, 111], $VV = [1, 116], $VW = [1, 117], $VX = [1, 114], $VY = [1, 115];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"directive\": 4, \"NEWLINE\": 5, \"RD\": 6, \"diagram\": 7, \"EOF\": 8, \"acc_title\": 9, \"acc_title_value\": 10, \"acc_descr\": 11, \"acc_descr_value\": 12, \"acc_descr_multiline_value\": 13, \"requirementDef\": 14, \"elementDef\": 15, \"relationshipDef\": 16, \"direction\": 17, \"styleStatement\": 18, \"classDefStatement\": 19, \"classStatement\": 20, \"direction_tb\": 21, \"direction_bt\": 22, \"direction_rl\": 23, \"direction_lr\": 24, \"requirementType\": 25, \"requirementName\": 26, \"STRUCT_START\": 27, \"requirementBody\": 28, \"STYLE_SEPARATOR\": 29, \"idList\": 30, \"ID\": 31, \"COLONSEP\": 32, \"id\": 33, \"TEXT\": 34, \"text\": 35, \"RISK\": 36, \"riskLevel\": 37, \"VERIFYMTHD\": 38, \"verifyType\": 39, \"STRUCT_STOP\": 40, \"REQUIREMENT\": 41, \"FUNCTIONAL_REQUIREMENT\": 42, \"INTERFACE_REQUIREMENT\": 43, \"PERFORMANCE_REQUIREMENT\": 44, \"PHYSICAL_REQUIREMENT\": 45, \"DESIGN_CONSTRAINT\": 46, \"LOW_RISK\": 47, \"MED_RISK\": 48, \"HIGH_RISK\": 49, \"VERIFY_ANALYSIS\": 50, \"VERIFY_DEMONSTRATION\": 51, \"VERIFY_INSPECTION\": 52, \"VERIFY_TEST\": 53, \"ELEMENT\": 54, \"elementName\": 55, \"elementBody\": 56, \"TYPE\": 57, \"type\": 58, \"DOCREF\": 59, \"ref\": 60, \"END_ARROW_L\": 61, \"relationship\": 62, \"LINE\": 63, \"END_ARROW_R\": 64, \"CONTAINS\": 65, \"COPIES\": 66, \"DERIVES\": 67, \"SATISFIES\": 68, \"VERIFIES\": 69, \"REFINES\": 70, \"TRACES\": 71, \"CLASSDEF\": 72, \"stylesOpt\": 73, \"CLASS\": 74, \"ALPHA\": 75, \"COMMA\": 76, \"STYLE\": 77, \"style\": 78, \"styleComponent\": 79, \"NUM\": 80, \"COLON\": 81, \"UNIT\": 82, \"SPACE\": 83, \"BRKT\": 84, \"PCT\": 85, \"MINUS\": 86, \"LABEL\": 87, \"SEMICOLON\": 88, \"unqString\": 89, \"qString\": 90, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 5: \"NEWLINE\", 6: \"RD\", 8: \"EOF\", 9: \"acc_title\", 10: \"acc_title_value\", 11: \"acc_descr\", 12: \"acc_descr_value\", 13: \"acc_descr_multiline_value\", 21: \"direction_tb\", 22: \"direction_bt\", 23: \"direction_rl\", 24: \"direction_lr\", 27: \"STRUCT_START\", 29: \"STYLE_SEPARATOR\", 31: \"ID\", 32: \"COLONSEP\", 34: \"TEXT\", 36: \"RISK\", 38: \"VERIFYMTHD\", 40: \"STRUCT_STOP\", 41: \"REQUIREMENT\", 42: \"FUNCTIONAL_REQUIREMENT\", 43: \"INTERFACE_REQUIREMENT\", 44: \"PERFORMANCE_REQUIREMENT\", 45: \"PHYSICAL_REQUIREMENT\", 46: \"DESIGN_CONSTRAINT\", 47: \"LOW_RISK\", 48: \"MED_RISK\", 49: \"HIGH_RISK\", 50: \"VERIFY_ANALYSIS\", 51: \"VERIFY_DEMONSTRATION\", 52: \"VERIFY_INSPECTION\", 53: \"VERIFY_TEST\", 54: \"ELEMENT\", 57: \"TYPE\", 59: \"DOCREF\", 61: \"END_ARROW_L\", 63: \"LINE\", 64: \"END_ARROW_R\", 65: \"CONTAINS\", 66: \"COPIES\", 67: \"DERIVES\", 68: \"SATISFIES\", 69: \"VERIFIES\", 70: \"REFINES\", 71: \"TRACES\", 72: \"CLASSDEF\", 74: \"CLASS\", 75: \"ALPHA\", 76: \"COMMA\", 77: \"STYLE\", 80: \"NUM\", 81: \"COLON\", 82: \"UNIT\", 83: \"SPACE\", 84: \"BRKT\", 85: \"PCT\", 86: \"MINUS\", 87: \"LABEL\", 88: \"SEMICOLON\", 89: \"unqString\", 90: \"qString\" },\n    productions_: [0, [3, 3], [3, 2], [3, 4], [4, 2], [4, 2], [4, 1], [7, 0], [7, 2], [7, 2], [7, 2], [7, 2], [7, 2], [7, 2], [7, 2], [7, 2], [7, 2], [17, 1], [17, 1], [17, 1], [17, 1], [14, 5], [14, 7], [28, 5], [28, 5], [28, 5], [28, 5], [28, 2], [28, 1], [25, 1], [25, 1], [25, 1], [25, 1], [25, 1], [25, 1], [37, 1], [37, 1], [37, 1], [39, 1], [39, 1], [39, 1], [39, 1], [15, 5], [15, 7], [56, 5], [56, 5], [56, 2], [56, 1], [16, 5], [16, 5], [62, 1], [62, 1], [62, 1], [62, 1], [62, 1], [62, 1], [62, 1], [19, 3], [20, 3], [20, 3], [30, 1], [30, 3], [30, 1], [30, 3], [18, 3], [73, 1], [73, 3], [78, 1], [78, 2], [79, 1], [79, 1], [79, 1], [79, 1], [79, 1], [79, 1], [79, 1], [79, 1], [79, 1], [79, 1], [26, 1], [26, 1], [33, 1], [33, 1], [35, 1], [35, 1], [55, 1], [55, 1], [58, 1], [58, 1], [60, 1], [60, 1]],\n    performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 4:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 5:\n        case 6:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 7:\n          this.$ = [];\n          break;\n        case 17:\n          yy.setDirection(\"TB\");\n          break;\n        case 18:\n          yy.setDirection(\"BT\");\n          break;\n        case 19:\n          yy.setDirection(\"RL\");\n          break;\n        case 20:\n          yy.setDirection(\"LR\");\n          break;\n        case 21:\n          yy.addRequirement($$[$0 - 3], $$[$0 - 4]);\n          break;\n        case 22:\n          yy.addRequirement($$[$0 - 5], $$[$0 - 6]);\n          yy.setClass([$$[$0 - 5]], $$[$0 - 3]);\n          break;\n        case 23:\n          yy.setNewReqId($$[$0 - 2]);\n          break;\n        case 24:\n          yy.setNewReqText($$[$0 - 2]);\n          break;\n        case 25:\n          yy.setNewReqRisk($$[$0 - 2]);\n          break;\n        case 26:\n          yy.setNewReqVerifyMethod($$[$0 - 2]);\n          break;\n        case 29:\n          this.$ = yy.RequirementType.REQUIREMENT;\n          break;\n        case 30:\n          this.$ = yy.RequirementType.FUNCTIONAL_REQUIREMENT;\n          break;\n        case 31:\n          this.$ = yy.RequirementType.INTERFACE_REQUIREMENT;\n          break;\n        case 32:\n          this.$ = yy.RequirementType.PERFORMANCE_REQUIREMENT;\n          break;\n        case 33:\n          this.$ = yy.RequirementType.PHYSICAL_REQUIREMENT;\n          break;\n        case 34:\n          this.$ = yy.RequirementType.DESIGN_CONSTRAINT;\n          break;\n        case 35:\n          this.$ = yy.RiskLevel.LOW_RISK;\n          break;\n        case 36:\n          this.$ = yy.RiskLevel.MED_RISK;\n          break;\n        case 37:\n          this.$ = yy.RiskLevel.HIGH_RISK;\n          break;\n        case 38:\n          this.$ = yy.VerifyType.VERIFY_ANALYSIS;\n          break;\n        case 39:\n          this.$ = yy.VerifyType.VERIFY_DEMONSTRATION;\n          break;\n        case 40:\n          this.$ = yy.VerifyType.VERIFY_INSPECTION;\n          break;\n        case 41:\n          this.$ = yy.VerifyType.VERIFY_TEST;\n          break;\n        case 42:\n          yy.addElement($$[$0 - 3]);\n          break;\n        case 43:\n          yy.addElement($$[$0 - 5]);\n          yy.setClass([$$[$0 - 5]], $$[$0 - 3]);\n          break;\n        case 44:\n          yy.setNewElementType($$[$0 - 2]);\n          break;\n        case 45:\n          yy.setNewElementDocRef($$[$0 - 2]);\n          break;\n        case 48:\n          yy.addRelationship($$[$0 - 2], $$[$0], $$[$0 - 4]);\n          break;\n        case 49:\n          yy.addRelationship($$[$0 - 2], $$[$0 - 4], $$[$0]);\n          break;\n        case 50:\n          this.$ = yy.Relationships.CONTAINS;\n          break;\n        case 51:\n          this.$ = yy.Relationships.COPIES;\n          break;\n        case 52:\n          this.$ = yy.Relationships.DERIVES;\n          break;\n        case 53:\n          this.$ = yy.Relationships.SATISFIES;\n          break;\n        case 54:\n          this.$ = yy.Relationships.VERIFIES;\n          break;\n        case 55:\n          this.$ = yy.Relationships.REFINES;\n          break;\n        case 56:\n          this.$ = yy.Relationships.TRACES;\n          break;\n        case 57:\n          this.$ = $$[$0 - 2];\n          yy.defineClass($$[$0 - 1], $$[$0]);\n          break;\n        case 58:\n          yy.setClass($$[$0 - 1], $$[$0]);\n          break;\n        case 59:\n          yy.setClass([$$[$0 - 2]], $$[$0]);\n          break;\n        case 60:\n        case 62:\n          this.$ = [$$[$0]];\n          break;\n        case 61:\n        case 63:\n          this.$ = $$[$0 - 2].concat([$$[$0]]);\n          break;\n        case 64:\n          this.$ = $$[$0 - 2];\n          yy.setCssStyle($$[$0 - 1], $$[$0]);\n          break;\n        case 65:\n          this.$ = [$$[$0]];\n          break;\n        case 66:\n          $$[$0 - 2].push($$[$0]);\n          this.$ = $$[$0 - 2];\n          break;\n        case 68:\n          this.$ = $$[$0 - 1] + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: 2, 6: $V0, 9: $V1, 11: $V2, 13: $V3 }, { 1: [3] }, { 3: 8, 4: 2, 5: [1, 7], 6: $V0, 9: $V1, 11: $V2, 13: $V3 }, { 5: [1, 9] }, { 10: [1, 10] }, { 12: [1, 11] }, o($V4, [2, 6]), { 3: 12, 4: 2, 6: $V0, 9: $V1, 11: $V2, 13: $V3 }, { 1: [2, 2] }, { 4: 17, 5: $V5, 7: 13, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, o($V4, [2, 4]), o($V4, [2, 5]), { 1: [2, 1] }, { 8: [1, 41] }, { 4: 17, 5: $V5, 7: 42, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 4: 17, 5: $V5, 7: 43, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 4: 17, 5: $V5, 7: 44, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 4: 17, 5: $V5, 7: 45, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 4: 17, 5: $V5, 7: 46, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 4: 17, 5: $V5, 7: 47, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 4: 17, 5: $V5, 7: 48, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 4: 17, 5: $V5, 7: 49, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 4: 17, 5: $V5, 7: 50, 8: $V6, 9: $V1, 11: $V2, 13: $V3, 14: 14, 15: 15, 16: 16, 17: 18, 18: 19, 19: 20, 20: 21, 21: $V7, 22: $V8, 23: $V9, 24: $Va, 25: 23, 33: 25, 41: $Vb, 42: $Vc, 43: $Vd, 44: $Ve, 45: $Vf, 46: $Vg, 54: $Vh, 72: $Vi, 74: $Vj, 77: $Vk, 89: $Vl, 90: $Vm }, { 26: 51, 89: [1, 52], 90: [1, 53] }, { 55: 54, 89: [1, 55], 90: [1, 56] }, { 29: [1, 59], 61: [1, 57], 63: [1, 58] }, o($Vn, [2, 17]), o($Vn, [2, 18]), o($Vn, [2, 19]), o($Vn, [2, 20]), { 30: 60, 33: 62, 75: $Vo, 89: $Vl, 90: $Vm }, { 30: 63, 33: 62, 75: $Vo, 89: $Vl, 90: $Vm }, { 30: 64, 33: 62, 75: $Vo, 89: $Vl, 90: $Vm }, o($Vp, [2, 29]), o($Vp, [2, 30]), o($Vp, [2, 31]), o($Vp, [2, 32]), o($Vp, [2, 33]), o($Vp, [2, 34]), o($Vq, [2, 81]), o($Vq, [2, 82]), { 1: [2, 3] }, { 8: [2, 8] }, { 8: [2, 9] }, { 8: [2, 10] }, { 8: [2, 11] }, { 8: [2, 12] }, { 8: [2, 13] }, { 8: [2, 14] }, { 8: [2, 15] }, { 8: [2, 16] }, { 27: [1, 65], 29: [1, 66] }, o($Vr, [2, 79]), o($Vr, [2, 80]), { 27: [1, 67], 29: [1, 68] }, o($Vr, [2, 85]), o($Vr, [2, 86]), { 62: 69, 65: $Vs, 66: $Vt, 67: $Vu, 68: $Vv, 69: $Vw, 70: $Vx, 71: $Vy }, { 62: 77, 65: $Vs, 66: $Vt, 67: $Vu, 68: $Vv, 69: $Vw, 70: $Vx, 71: $Vy }, { 30: 78, 33: 62, 75: $Vo, 89: $Vl, 90: $Vm }, { 73: 79, 75: $Vz, 76: $VA, 78: 81, 79: 82, 80: $VB, 81: $VC, 82: $VD, 83: $VE, 84: $VF, 85: $VG, 86: $VH, 87: $VI, 88: $VJ }, o($VK, [2, 60]), o($VK, [2, 62]), { 73: 93, 75: $Vz, 76: $VA, 78: 81, 79: 82, 80: $VB, 81: $VC, 82: $VD, 83: $VE, 84: $VF, 85: $VG, 86: $VH, 87: $VI, 88: $VJ }, { 30: 94, 33: 62, 75: $Vo, 76: $VA, 89: $Vl, 90: $Vm }, { 5: [1, 95] }, { 30: 96, 33: 62, 75: $Vo, 89: $Vl, 90: $Vm }, { 5: [1, 97] }, { 30: 98, 33: 62, 75: $Vo, 89: $Vl, 90: $Vm }, { 63: [1, 99] }, o($VL, [2, 50]), o($VL, [2, 51]), o($VL, [2, 52]), o($VL, [2, 53]), o($VL, [2, 54]), o($VL, [2, 55]), o($VL, [2, 56]), { 64: [1, 100] }, o($Vn, [2, 59], { 76: $VA }), o($Vn, [2, 64], { 76: $VM }), { 33: 103, 75: [1, 102], 89: $Vl, 90: $Vm }, o($VN, [2, 65], { 79: 104, 75: $Vz, 80: $VB, 81: $VC, 82: $VD, 83: $VE, 84: $VF, 85: $VG, 86: $VH, 87: $VI, 88: $VJ }), o($VO, [2, 67]), o($VO, [2, 69]), o($VO, [2, 70]), o($VO, [2, 71]), o($VO, [2, 72]), o($VO, [2, 73]), o($VO, [2, 74]), o($VO, [2, 75]), o($VO, [2, 76]), o($VO, [2, 77]), o($VO, [2, 78]), o($Vn, [2, 57], { 76: $VM }), o($Vn, [2, 58], { 76: $VA }), { 5: $VP, 28: 105, 31: $VQ, 34: $VR, 36: $VS, 38: $VT, 40: $VU }, { 27: [1, 112], 76: $VA }, { 5: $VV, 40: $VW, 56: 113, 57: $VX, 59: $VY }, { 27: [1, 118], 76: $VA }, { 33: 119, 89: $Vl, 90: $Vm }, { 33: 120, 89: $Vl, 90: $Vm }, { 75: $Vz, 78: 121, 79: 82, 80: $VB, 81: $VC, 82: $VD, 83: $VE, 84: $VF, 85: $VG, 86: $VH, 87: $VI, 88: $VJ }, o($VK, [2, 61]), o($VK, [2, 63]), o($VO, [2, 68]), o($Vn, [2, 21]), { 32: [1, 122] }, { 32: [1, 123] }, { 32: [1, 124] }, { 32: [1, 125] }, { 5: $VP, 28: 126, 31: $VQ, 34: $VR, 36: $VS, 38: $VT, 40: $VU }, o($Vn, [2, 28]), { 5: [1, 127] }, o($Vn, [2, 42]), { 32: [1, 128] }, { 32: [1, 129] }, { 5: $VV, 40: $VW, 56: 130, 57: $VX, 59: $VY }, o($Vn, [2, 47]), { 5: [1, 131] }, o($Vn, [2, 48]), o($Vn, [2, 49]), o($VN, [2, 66], { 79: 104, 75: $Vz, 80: $VB, 81: $VC, 82: $VD, 83: $VE, 84: $VF, 85: $VG, 86: $VH, 87: $VI, 88: $VJ }), { 33: 132, 89: $Vl, 90: $Vm }, { 35: 133, 89: [1, 134], 90: [1, 135] }, { 37: 136, 47: [1, 137], 48: [1, 138], 49: [1, 139] }, { 39: 140, 50: [1, 141], 51: [1, 142], 52: [1, 143], 53: [1, 144] }, o($Vn, [2, 27]), { 5: $VP, 28: 145, 31: $VQ, 34: $VR, 36: $VS, 38: $VT, 40: $VU }, { 58: 146, 89: [1, 147], 90: [1, 148] }, { 60: 149, 89: [1, 150], 90: [1, 151] }, o($Vn, [2, 46]), { 5: $VV, 40: $VW, 56: 152, 57: $VX, 59: $VY }, { 5: [1, 153] }, { 5: [1, 154] }, { 5: [2, 83] }, { 5: [2, 84] }, { 5: [1, 155] }, { 5: [2, 35] }, { 5: [2, 36] }, { 5: [2, 37] }, { 5: [1, 156] }, { 5: [2, 38] }, { 5: [2, 39] }, { 5: [2, 40] }, { 5: [2, 41] }, o($Vn, [2, 22]), { 5: [1, 157] }, { 5: [2, 87] }, { 5: [2, 88] }, { 5: [1, 158] }, { 5: [2, 89] }, { 5: [2, 90] }, o($Vn, [2, 43]), { 5: $VP, 28: 159, 31: $VQ, 34: $VR, 36: $VS, 38: $VT, 40: $VU }, { 5: $VP, 28: 160, 31: $VQ, 34: $VR, 36: $VS, 38: $VT, 40: $VU }, { 5: $VP, 28: 161, 31: $VQ, 34: $VR, 36: $VS, 38: $VT, 40: $VU }, { 5: $VP, 28: 162, 31: $VQ, 34: $VR, 36: $VS, 38: $VT, 40: $VU }, { 5: $VV, 40: $VW, 56: 163, 57: $VX, 59: $VY }, { 5: $VV, 40: $VW, 56: 164, 57: $VX, 59: $VY }, o($Vn, [2, 23]), o($Vn, [2, 24]), o($Vn, [2, 25]), o($Vn, [2, 26]), o($Vn, [2, 44]), o($Vn, [2, 45])],\n    defaultActions: { 8: [2, 2], 12: [2, 1], 41: [2, 3], 42: [2, 8], 43: [2, 9], 44: [2, 10], 45: [2, 11], 46: [2, 12], 47: [2, 13], 48: [2, 14], 49: [2, 15], 50: [2, 16], 134: [2, 83], 135: [2, 84], 137: [2, 35], 138: [2, 36], 139: [2, 37], 141: [2, 38], 142: [2, 39], 143: [2, 40], 144: [2, 41], 147: [2, 87], 148: [2, 88], 150: [2, 89], 151: [2, 90] },\n    parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return \"title\";\n            break;\n          case 1:\n            this.begin(\"acc_title\");\n            return 9;\n            break;\n          case 2:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 3:\n            this.begin(\"acc_descr\");\n            return 11;\n            break;\n          case 4:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 5:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 6:\n            this.popState();\n            break;\n          case 7:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 8:\n            return 21;\n            break;\n          case 9:\n            return 22;\n            break;\n          case 10:\n            return 23;\n            break;\n          case 11:\n            return 24;\n            break;\n          case 12:\n            return 5;\n            break;\n          case 13:\n            break;\n          case 14:\n            break;\n          case 15:\n            break;\n          case 16:\n            return 8;\n            break;\n          case 17:\n            return 6;\n            break;\n          case 18:\n            return 27;\n            break;\n          case 19:\n            return 40;\n            break;\n          case 20:\n            return 29;\n            break;\n          case 21:\n            return 32;\n            break;\n          case 22:\n            return 31;\n            break;\n          case 23:\n            return 34;\n            break;\n          case 24:\n            return 36;\n            break;\n          case 25:\n            return 38;\n            break;\n          case 26:\n            return 41;\n            break;\n          case 27:\n            return 42;\n            break;\n          case 28:\n            return 43;\n            break;\n          case 29:\n            return 44;\n            break;\n          case 30:\n            return 45;\n            break;\n          case 31:\n            return 46;\n            break;\n          case 32:\n            return 47;\n            break;\n          case 33:\n            return 48;\n            break;\n          case 34:\n            return 49;\n            break;\n          case 35:\n            return 50;\n            break;\n          case 36:\n            return 51;\n            break;\n          case 37:\n            return 52;\n            break;\n          case 38:\n            return 53;\n            break;\n          case 39:\n            return 54;\n            break;\n          case 40:\n            return 65;\n            break;\n          case 41:\n            return 66;\n            break;\n          case 42:\n            return 67;\n            break;\n          case 43:\n            return 68;\n            break;\n          case 44:\n            return 69;\n            break;\n          case 45:\n            return 70;\n            break;\n          case 46:\n            return 71;\n            break;\n          case 47:\n            return 57;\n            break;\n          case 48:\n            return 59;\n            break;\n          case 49:\n            this.begin(\"style\");\n            return 77;\n            break;\n          case 50:\n            return 75;\n            break;\n          case 51:\n            return 81;\n            break;\n          case 52:\n            return 88;\n            break;\n          case 53:\n            return \"PERCENT\";\n            break;\n          case 54:\n            return 86;\n            break;\n          case 55:\n            return 84;\n            break;\n          case 56:\n            break;\n          case 57:\n            this.begin(\"string\");\n            break;\n          case 58:\n            this.popState();\n            break;\n          case 59:\n            this.begin(\"style\");\n            return 72;\n            break;\n          case 60:\n            this.begin(\"style\");\n            return 74;\n            break;\n          case 61:\n            return 61;\n            break;\n          case 62:\n            return 64;\n            break;\n          case 63:\n            return 63;\n            break;\n          case 64:\n            this.begin(\"string\");\n            break;\n          case 65:\n            this.popState();\n            break;\n          case 66:\n            return \"qString\";\n            break;\n          case 67:\n            yy_.yytext = yy_.yytext.trim();\n            return 89;\n            break;\n          case 68:\n            return 75;\n            break;\n          case 69:\n            return 80;\n            break;\n          case 70:\n            return 76;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:title\\s[^#\\n;]+)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:.*direction\\s+TB[^\\n]*)/i, /^(?:.*direction\\s+BT[^\\n]*)/i, /^(?:.*direction\\s+RL[^\\n]*)/i, /^(?:.*direction\\s+LR[^\\n]*)/i, /^(?:(\\r?\\n)+)/i, /^(?:\\s+)/i, /^(?:#[^\\n]*)/i, /^(?:%[^\\n]*)/i, /^(?:$)/i, /^(?:requirementDiagram\\b)/i, /^(?:\\{)/i, /^(?:\\})/i, /^(?::{3})/i, /^(?::)/i, /^(?:id\\b)/i, /^(?:text\\b)/i, /^(?:risk\\b)/i, /^(?:verifyMethod\\b)/i, /^(?:requirement\\b)/i, /^(?:functionalRequirement\\b)/i, /^(?:interfaceRequirement\\b)/i, /^(?:performanceRequirement\\b)/i, /^(?:physicalRequirement\\b)/i, /^(?:designConstraint\\b)/i, /^(?:low\\b)/i, /^(?:medium\\b)/i, /^(?:high\\b)/i, /^(?:analysis\\b)/i, /^(?:demonstration\\b)/i, /^(?:inspection\\b)/i, /^(?:test\\b)/i, /^(?:element\\b)/i, /^(?:contains\\b)/i, /^(?:copies\\b)/i, /^(?:derives\\b)/i, /^(?:satisfies\\b)/i, /^(?:verifies\\b)/i, /^(?:refines\\b)/i, /^(?:traces\\b)/i, /^(?:type\\b)/i, /^(?:docref\\b)/i, /^(?:style\\b)/i, /^(?:\\w+)/i, /^(?::)/i, /^(?:;)/i, /^(?:%)/i, /^(?:-)/i, /^(?:#)/i, /^(?: )/i, /^(?:[\"])/i, /^(?:\\n)/i, /^(?:classDef\\b)/i, /^(?:class\\b)/i, /^(?:<-)/i, /^(?:->)/i, /^(?:-)/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:[\\w][^:,\\r\\n\\{\\<\\>\\-\\=]*)/i, /^(?:\\w+)/i, /^(?:[0-9]+)/i, /^(?:,)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [6, 7, 68, 69, 70], \"inclusive\": false }, \"acc_descr\": { \"rules\": [4, 68, 69, 70], \"inclusive\": false }, \"acc_title\": { \"rules\": [2, 68, 69, 70], \"inclusive\": false }, \"style\": { \"rules\": [50, 51, 52, 53, 54, 55, 56, 57, 58, 68, 69, 70], \"inclusive\": false }, \"unqString\": { \"rules\": [68, 69, 70], \"inclusive\": false }, \"token\": { \"rules\": [68, 69, 70], \"inclusive\": false }, \"string\": { \"rules\": [65, 66, 68, 69, 70], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 3, 5, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 59, 60, 61, 62, 63, 64, 67, 68, 69, 70], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar requirementDiagram_default = parser;\n\n// src/diagrams/requirement/requirementDb.ts\nvar RequirementDB = class {\n  constructor() {\n    this.relations = [];\n    this.latestRequirement = this.getInitialRequirement();\n    this.requirements = /* @__PURE__ */ new Map();\n    this.latestElement = this.getInitialElement();\n    this.elements = /* @__PURE__ */ new Map();\n    this.classes = /* @__PURE__ */ new Map();\n    this.direction = \"TB\";\n    this.RequirementType = {\n      REQUIREMENT: \"Requirement\",\n      FUNCTIONAL_REQUIREMENT: \"Functional Requirement\",\n      INTERFACE_REQUIREMENT: \"Interface Requirement\",\n      PERFORMANCE_REQUIREMENT: \"Performance Requirement\",\n      PHYSICAL_REQUIREMENT: \"Physical Requirement\",\n      DESIGN_CONSTRAINT: \"Design Constraint\"\n    };\n    this.RiskLevel = {\n      LOW_RISK: \"Low\",\n      MED_RISK: \"Medium\",\n      HIGH_RISK: \"High\"\n    };\n    this.VerifyType = {\n      VERIFY_ANALYSIS: \"Analysis\",\n      VERIFY_DEMONSTRATION: \"Demonstration\",\n      VERIFY_INSPECTION: \"Inspection\",\n      VERIFY_TEST: \"Test\"\n    };\n    this.Relationships = {\n      CONTAINS: \"contains\",\n      COPIES: \"copies\",\n      DERIVES: \"derives\",\n      SATISFIES: \"satisfies\",\n      VERIFIES: \"verifies\",\n      REFINES: \"refines\",\n      TRACES: \"traces\"\n    };\n    this.setAccTitle = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.setAccTitle;\n    this.getAccTitle = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.getAccTitle;\n    this.setAccDescription = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.setAccDescription;\n    this.getAccDescription = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.getAccDescription;\n    this.setDiagramTitle = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.setDiagramTitle;\n    this.getDiagramTitle = _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.getDiagramTitle;\n    this.getConfig = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(() => (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.getConfig2)().requirement, \"getConfig\");\n    this.clear();\n    this.setDirection = this.setDirection.bind(this);\n    this.addRequirement = this.addRequirement.bind(this);\n    this.setNewReqId = this.setNewReqId.bind(this);\n    this.setNewReqRisk = this.setNewReqRisk.bind(this);\n    this.setNewReqText = this.setNewReqText.bind(this);\n    this.setNewReqVerifyMethod = this.setNewReqVerifyMethod.bind(this);\n    this.addElement = this.addElement.bind(this);\n    this.setNewElementType = this.setNewElementType.bind(this);\n    this.setNewElementDocRef = this.setNewElementDocRef.bind(this);\n    this.addRelationship = this.addRelationship.bind(this);\n    this.setCssStyle = this.setCssStyle.bind(this);\n    this.setClass = this.setClass.bind(this);\n    this.defineClass = this.defineClass.bind(this);\n    this.setAccTitle = this.setAccTitle.bind(this);\n    this.setAccDescription = this.setAccDescription.bind(this);\n  }\n  static {\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(this, \"RequirementDB\");\n  }\n  getDirection() {\n    return this.direction;\n  }\n  setDirection(dir) {\n    this.direction = dir;\n  }\n  resetLatestRequirement() {\n    this.latestRequirement = this.getInitialRequirement();\n  }\n  resetLatestElement() {\n    this.latestElement = this.getInitialElement();\n  }\n  getInitialRequirement() {\n    return {\n      requirementId: \"\",\n      text: \"\",\n      risk: \"\",\n      verifyMethod: \"\",\n      name: \"\",\n      type: \"\",\n      cssStyles: [],\n      classes: [\"default\"]\n    };\n  }\n  getInitialElement() {\n    return {\n      name: \"\",\n      type: \"\",\n      docRef: \"\",\n      cssStyles: [],\n      classes: [\"default\"]\n    };\n  }\n  addRequirement(name, type) {\n    if (!this.requirements.has(name)) {\n      this.requirements.set(name, {\n        name,\n        type,\n        requirementId: this.latestRequirement.requirementId,\n        text: this.latestRequirement.text,\n        risk: this.latestRequirement.risk,\n        verifyMethod: this.latestRequirement.verifyMethod,\n        cssStyles: [],\n        classes: [\"default\"]\n      });\n    }\n    this.resetLatestRequirement();\n    return this.requirements.get(name);\n  }\n  getRequirements() {\n    return this.requirements;\n  }\n  setNewReqId(id) {\n    if (this.latestRequirement !== void 0) {\n      this.latestRequirement.requirementId = id;\n    }\n  }\n  setNewReqText(text) {\n    if (this.latestRequirement !== void 0) {\n      this.latestRequirement.text = text;\n    }\n  }\n  setNewReqRisk(risk) {\n    if (this.latestRequirement !== void 0) {\n      this.latestRequirement.risk = risk;\n    }\n  }\n  setNewReqVerifyMethod(verifyMethod) {\n    if (this.latestRequirement !== void 0) {\n      this.latestRequirement.verifyMethod = verifyMethod;\n    }\n  }\n  addElement(name) {\n    if (!this.elements.has(name)) {\n      this.elements.set(name, {\n        name,\n        type: this.latestElement.type,\n        docRef: this.latestElement.docRef,\n        cssStyles: [],\n        classes: [\"default\"]\n      });\n      _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.log.info(\"Added new element: \", name);\n    }\n    this.resetLatestElement();\n    return this.elements.get(name);\n  }\n  getElements() {\n    return this.elements;\n  }\n  setNewElementType(type) {\n    if (this.latestElement !== void 0) {\n      this.latestElement.type = type;\n    }\n  }\n  setNewElementDocRef(docRef) {\n    if (this.latestElement !== void 0) {\n      this.latestElement.docRef = docRef;\n    }\n  }\n  addRelationship(type, src, dst) {\n    this.relations.push({\n      type,\n      src,\n      dst\n    });\n  }\n  getRelationships() {\n    return this.relations;\n  }\n  clear() {\n    this.relations = [];\n    this.resetLatestRequirement();\n    this.requirements = /* @__PURE__ */ new Map();\n    this.resetLatestElement();\n    this.elements = /* @__PURE__ */ new Map();\n    this.classes = /* @__PURE__ */ new Map();\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.clear)();\n  }\n  setCssStyle(ids, styles) {\n    for (const id of ids) {\n      const node = this.requirements.get(id) ?? this.elements.get(id);\n      if (!styles || !node) {\n        return;\n      }\n      for (const s of styles) {\n        if (s.includes(\",\")) {\n          node.cssStyles.push(...s.split(\",\"));\n        } else {\n          node.cssStyles.push(s);\n        }\n      }\n    }\n  }\n  setClass(ids, classNames) {\n    for (const id of ids) {\n      const node = this.requirements.get(id) ?? this.elements.get(id);\n      if (node) {\n        for (const _class of classNames) {\n          node.classes.push(_class);\n          const styles = this.classes.get(_class)?.styles;\n          if (styles) {\n            node.cssStyles.push(...styles);\n          }\n        }\n      }\n    }\n  }\n  defineClass(ids, style) {\n    for (const id of ids) {\n      let styleClass = this.classes.get(id);\n      if (styleClass === void 0) {\n        styleClass = { id, styles: [], textStyles: [] };\n        this.classes.set(id, styleClass);\n      }\n      if (style) {\n        style.forEach(function(s) {\n          if (/color/.exec(s)) {\n            const newStyle = s.replace(\"fill\", \"bgFill\");\n            styleClass.textStyles.push(newStyle);\n          }\n          styleClass.styles.push(s);\n        });\n      }\n      this.requirements.forEach((value) => {\n        if (value.classes.includes(id)) {\n          value.cssStyles.push(...style.flatMap((s) => s.split(\",\")));\n        }\n      });\n      this.elements.forEach((value) => {\n        if (value.classes.includes(id)) {\n          value.cssStyles.push(...style.flatMap((s) => s.split(\",\")));\n        }\n      });\n    }\n  }\n  getClasses() {\n    return this.classes;\n  }\n  getData() {\n    const config = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.getConfig2)();\n    const nodes = [];\n    const edges = [];\n    for (const requirement of this.requirements.values()) {\n      const node = requirement;\n      node.id = requirement.name;\n      node.cssStyles = requirement.cssStyles;\n      node.cssClasses = requirement.classes.join(\" \");\n      node.shape = \"requirementBox\";\n      node.look = config.look;\n      nodes.push(node);\n    }\n    for (const element of this.elements.values()) {\n      const node = element;\n      node.shape = \"requirementBox\";\n      node.look = config.look;\n      node.id = element.name;\n      node.cssStyles = element.cssStyles;\n      node.cssClasses = element.classes.join(\" \");\n      nodes.push(node);\n    }\n    for (const relation of this.relations) {\n      let counter = 0;\n      const isContains = relation.type === this.Relationships.CONTAINS;\n      const edge = {\n        id: `${relation.src}-${relation.dst}-${counter}`,\n        start: this.requirements.get(relation.src)?.name ?? this.elements.get(relation.src)?.name,\n        end: this.requirements.get(relation.dst)?.name ?? this.elements.get(relation.dst)?.name,\n        label: `&lt;&lt;${relation.type}&gt;&gt;`,\n        classes: \"relationshipLine\",\n        style: [\"fill:none\", isContains ? \"\" : \"stroke-dasharray: 10,7\"],\n        labelpos: \"c\",\n        thickness: \"normal\",\n        type: \"normal\",\n        pattern: isContains ? \"normal\" : \"dashed\",\n        arrowTypeStart: isContains ? \"requirement_contains\" : \"\",\n        arrowTypeEnd: isContains ? \"\" : \"requirement_arrow\",\n        look: config.look\n      };\n      edges.push(edge);\n      counter++;\n    }\n    return { nodes, edges, other: {}, config, direction: this.getDirection() };\n  }\n};\n\n// src/diagrams/requirement/styles.js\nvar getStyles = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)((options) => `\n\n  marker {\n    fill: ${options.relationColor};\n    stroke: ${options.relationColor};\n  }\n\n  marker.cross {\n    stroke: ${options.lineColor};\n  }\n\n  svg {\n    font-family: ${options.fontFamily};\n    font-size: ${options.fontSize};\n  }\n\n  .reqBox {\n    fill: ${options.requirementBackground};\n    fill-opacity: 1.0;\n    stroke: ${options.requirementBorderColor};\n    stroke-width: ${options.requirementBorderSize};\n  }\n  \n  .reqTitle, .reqLabel{\n    fill:  ${options.requirementTextColor};\n  }\n  .reqLabelBox {\n    fill: ${options.relationLabelBackground};\n    fill-opacity: 1.0;\n  }\n\n  .req-title-line {\n    stroke: ${options.requirementBorderColor};\n    stroke-width: ${options.requirementBorderSize};\n  }\n  .relationshipLine {\n    stroke: ${options.relationColor};\n    stroke-width: 1;\n  }\n  .relationshipLabel {\n    fill: ${options.relationLabelColor};\n  }\n  .divider {\n    stroke: ${options.nodeBorder};\n    stroke-width: 1;\n  }\n  .label {\n    font-family: ${options.fontFamily};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n  .label text,span {\n    fill: ${options.nodeTextColor || options.textColor};\n    color: ${options.nodeTextColor || options.textColor};\n  }\n  .labelBkg {\n    background-color: ${options.edgeLabelBackground};\n  }\n\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/requirement/requirementRenderer.ts\nvar requirementRenderer_exports = {};\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__export)(requirementRenderer_exports, {\n  draw: () => draw\n});\nvar draw = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.__name)(async function(text, id, _version, diag) {\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.log.info(\"REF0:\");\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.log.info(\"Drawing requirement diagram (unified)\", id);\n  const { securityLevel, state: conf, layout } = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_9__.getConfig2)();\n  const data4Layout = diag.db.getData();\n  const svg = (0,_chunk_RZ5BOZE2_mjs__WEBPACK_IMPORTED_MODULE_0__.getDiagramElement)(id, securityLevel);\n  data4Layout.type = diag.type;\n  data4Layout.layoutAlgorithm = (0,_chunk_TYCBKAJE_mjs__WEBPACK_IMPORTED_MODULE_1__.getRegisteredLayoutAlgorithm)(layout);\n  data4Layout.nodeSpacing = conf?.nodeSpacing ?? 50;\n  data4Layout.rankSpacing = conf?.rankSpacing ?? 50;\n  data4Layout.markers = [\"requirement_contains\", \"requirement_arrow\"];\n  data4Layout.diagramId = id;\n  await (0,_chunk_TYCBKAJE_mjs__WEBPACK_IMPORTED_MODULE_1__.render)(data4Layout, svg);\n  const padding = 8;\n  _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_8__.utils_default.insertTitle(\n    svg,\n    \"requirementDiagramTitleText\",\n    conf?.titleTopMargin ?? 25,\n    diag.db.getDiagramTitle()\n  );\n  (0,_chunk_RZ5BOZE2_mjs__WEBPACK_IMPORTED_MODULE_0__.setupViewPortForSVG)(svg, padding, \"requirementDiagram\", conf?.useMaxWidth ?? true);\n}, \"draw\");\n\n// src/diagrams/requirement/requirementDiagram.ts\nvar diagram = {\n  parser: requirementDiagram_default,\n  get db() {\n    return new RequirementDB();\n  },\n  renderer: requirementRenderer_exports,\n  styles: styles_default\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/requirementDiagram-KVF5MWMF.mjs\n"));

/***/ })

}]);