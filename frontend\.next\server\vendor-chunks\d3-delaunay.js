"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-delaunay";
exports.ids = ["vendor-chunks/d3-delaunay"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-delaunay/src/delaunay.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-delaunay/src/delaunay.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Delaunay)\n/* harmony export */ });\n/* harmony import */ var delaunator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! delaunator */ \"(ssr)/./node_modules/delaunator/index.js\");\n/* harmony import */ var _path_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./path.js */ \"(ssr)/./node_modules/d3-delaunay/src/path.js\");\n/* harmony import */ var _polygon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./polygon.js */ \"(ssr)/./node_modules/d3-delaunay/src/polygon.js\");\n/* harmony import */ var _voronoi_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./voronoi.js */ \"(ssr)/./node_modules/d3-delaunay/src/voronoi.js\");\n\n\n\n\n\nconst tau = 2 * Math.PI, pow = Math.pow;\n\nfunction pointX(p) {\n  return p[0];\n}\n\nfunction pointY(p) {\n  return p[1];\n}\n\n// A triangulation is collinear if all its triangles have a non-null area\nfunction collinear(d) {\n  const {triangles, coords} = d;\n  for (let i = 0; i < triangles.length; i += 3) {\n    const a = 2 * triangles[i],\n          b = 2 * triangles[i + 1],\n          c = 2 * triangles[i + 2],\n          cross = (coords[c] - coords[a]) * (coords[b + 1] - coords[a + 1])\n                - (coords[b] - coords[a]) * (coords[c + 1] - coords[a + 1]);\n    if (cross > 1e-10) return false;\n  }\n  return true;\n}\n\nfunction jitter(x, y, r) {\n  return [x + Math.sin(x + y) * r, y + Math.cos(x - y) * r];\n}\n\nclass Delaunay {\n  static from(points, fx = pointX, fy = pointY, that) {\n    return new Delaunay(\"length\" in points\n        ? flatArray(points, fx, fy, that)\n        : Float64Array.from(flatIterable(points, fx, fy, that)));\n  }\n  constructor(points) {\n    this._delaunator = new delaunator__WEBPACK_IMPORTED_MODULE_0__[\"default\"](points);\n    this.inedges = new Int32Array(points.length / 2);\n    this._hullIndex = new Int32Array(points.length / 2);\n    this.points = this._delaunator.coords;\n    this._init();\n  }\n  update() {\n    this._delaunator.update();\n    this._init();\n    return this;\n  }\n  _init() {\n    const d = this._delaunator, points = this.points;\n\n    // check for collinear\n    if (d.hull && d.hull.length > 2 && collinear(d)) {\n      this.collinear = Int32Array.from({length: points.length/2}, (_,i) => i)\n        .sort((i, j) => points[2 * i] - points[2 * j] || points[2 * i + 1] - points[2 * j + 1]); // for exact neighbors\n      const e = this.collinear[0], f = this.collinear[this.collinear.length - 1],\n        bounds = [ points[2 * e], points[2 * e + 1], points[2 * f], points[2 * f + 1] ],\n        r = 1e-8 * Math.hypot(bounds[3] - bounds[1], bounds[2] - bounds[0]);\n      for (let i = 0, n = points.length / 2; i < n; ++i) {\n        const p = jitter(points[2 * i], points[2 * i + 1], r);\n        points[2 * i] = p[0];\n        points[2 * i + 1] = p[1];\n      }\n      this._delaunator = new delaunator__WEBPACK_IMPORTED_MODULE_0__[\"default\"](points);\n    } else {\n      delete this.collinear;\n    }\n\n    const halfedges = this.halfedges = this._delaunator.halfedges;\n    const hull = this.hull = this._delaunator.hull;\n    const triangles = this.triangles = this._delaunator.triangles;\n    const inedges = this.inedges.fill(-1);\n    const hullIndex = this._hullIndex.fill(-1);\n\n    // Compute an index from each point to an (arbitrary) incoming halfedge\n    // Used to give the first neighbor of each point; for this reason,\n    // on the hull we give priority to exterior halfedges\n    for (let e = 0, n = halfedges.length; e < n; ++e) {\n      const p = triangles[e % 3 === 2 ? e - 2 : e + 1];\n      if (halfedges[e] === -1 || inedges[p] === -1) inedges[p] = e;\n    }\n    for (let i = 0, n = hull.length; i < n; ++i) {\n      hullIndex[hull[i]] = i;\n    }\n\n    // degenerate case: 1 or 2 (distinct) points\n    if (hull.length <= 2 && hull.length > 0) {\n      this.triangles = new Int32Array(3).fill(-1);\n      this.halfedges = new Int32Array(3).fill(-1);\n      this.triangles[0] = hull[0];\n      inedges[hull[0]] = 1;\n      if (hull.length === 2) {\n        inedges[hull[1]] = 0;\n        this.triangles[1] = hull[1];\n        this.triangles[2] = hull[1];\n      }\n    }\n  }\n  voronoi(bounds) {\n    return new _voronoi_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, bounds);\n  }\n  *neighbors(i) {\n    const {inedges, hull, _hullIndex, halfedges, triangles, collinear} = this;\n\n    // degenerate case with several collinear points\n    if (collinear) {\n      const l = collinear.indexOf(i);\n      if (l > 0) yield collinear[l - 1];\n      if (l < collinear.length - 1) yield collinear[l + 1];\n      return;\n    }\n\n    const e0 = inedges[i];\n    if (e0 === -1) return; // coincident point\n    let e = e0, p0 = -1;\n    do {\n      yield p0 = triangles[e];\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) return; // bad triangulation\n      e = halfedges[e];\n      if (e === -1) {\n        const p = hull[(_hullIndex[i] + 1) % hull.length];\n        if (p !== p0) yield p;\n        return;\n      }\n    } while (e !== e0);\n  }\n  find(x, y, i = 0) {\n    if ((x = +x, x !== x) || (y = +y, y !== y)) return -1;\n    const i0 = i;\n    let c;\n    while ((c = this._step(i, x, y)) >= 0 && c !== i && c !== i0) i = c;\n    return c;\n  }\n  _step(i, x, y) {\n    const {inedges, hull, _hullIndex, halfedges, triangles, points} = this;\n    if (inedges[i] === -1 || !points.length) return (i + 1) % (points.length >> 1);\n    let c = i;\n    let dc = pow(x - points[i * 2], 2) + pow(y - points[i * 2 + 1], 2);\n    const e0 = inedges[i];\n    let e = e0;\n    do {\n      let t = triangles[e];\n      const dt = pow(x - points[t * 2], 2) + pow(y - points[t * 2 + 1], 2);\n      if (dt < dc) dc = dt, c = t;\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) break; // bad triangulation\n      e = halfedges[e];\n      if (e === -1) {\n        e = hull[(_hullIndex[i] + 1) % hull.length];\n        if (e !== t) {\n          if (pow(x - points[e * 2], 2) + pow(y - points[e * 2 + 1], 2) < dc) return e;\n        }\n        break;\n      }\n    } while (e !== e0);\n    return c;\n  }\n  render(context) {\n    const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"] : undefined;\n    const {points, halfedges, triangles} = this;\n    for (let i = 0, n = halfedges.length; i < n; ++i) {\n      const j = halfedges[i];\n      if (j < i) continue;\n      const ti = triangles[i] * 2;\n      const tj = triangles[j] * 2;\n      context.moveTo(points[ti], points[ti + 1]);\n      context.lineTo(points[tj], points[tj + 1]);\n    }\n    this.renderHull(context);\n    return buffer && buffer.value();\n  }\n  renderPoints(context, r) {\n    if (r === undefined && (!context || typeof context.moveTo !== \"function\")) r = context, context = null;\n    r = r == undefined ? 2 : +r;\n    const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"] : undefined;\n    const {points} = this;\n    for (let i = 0, n = points.length; i < n; i += 2) {\n      const x = points[i], y = points[i + 1];\n      context.moveTo(x + r, y);\n      context.arc(x, y, r, 0, tau);\n    }\n    return buffer && buffer.value();\n  }\n  renderHull(context) {\n    const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"] : undefined;\n    const {hull, points} = this;\n    const h = hull[0] * 2, n = hull.length;\n    context.moveTo(points[h], points[h + 1]);\n    for (let i = 1; i < n; ++i) {\n      const h = 2 * hull[i];\n      context.lineTo(points[h], points[h + 1]);\n    }\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  hullPolygon() {\n    const polygon = new _polygon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n    this.renderHull(polygon);\n    return polygon.value();\n  }\n  renderTriangle(i, context) {\n    const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"] : undefined;\n    const {points, triangles} = this;\n    const t0 = triangles[i *= 3] * 2;\n    const t1 = triangles[i + 1] * 2;\n    const t2 = triangles[i + 2] * 2;\n    context.moveTo(points[t0], points[t0 + 1]);\n    context.lineTo(points[t1], points[t1 + 1]);\n    context.lineTo(points[t2], points[t2 + 1]);\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  *trianglePolygons() {\n    const {triangles} = this;\n    for (let i = 0, n = triangles.length / 3; i < n; ++i) {\n      yield this.trianglePolygon(i);\n    }\n  }\n  trianglePolygon(i) {\n    const polygon = new _polygon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n    this.renderTriangle(i, polygon);\n    return polygon.value();\n  }\n}\n\nfunction flatArray(points, fx, fy, that) {\n  const n = points.length;\n  const array = new Float64Array(n * 2);\n  for (let i = 0; i < n; ++i) {\n    const p = points[i];\n    array[i * 2] = fx.call(that, p, i, points);\n    array[i * 2 + 1] = fy.call(that, p, i, points);\n  }\n  return array;\n}\n\nfunction* flatIterable(points, fx, fy, that) {\n  let i = 0;\n  for (const p of points) {\n    yield fx.call(that, p, i, points);\n    yield fy.call(that, p, i, points);\n    ++i;\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-delaunay/src/delaunay.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-delaunay/src/index.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-delaunay/src/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Delaunay: () => (/* reexport safe */ _delaunay_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Voronoi: () => (/* reexport safe */ _voronoi_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _delaunay_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./delaunay.js */ \"(ssr)/./node_modules/d3-delaunay/src/delaunay.js\");\n/* harmony import */ var _voronoi_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./voronoi.js */ \"(ssr)/./node_modules/d3-delaunay/src/voronoi.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZGVsYXVuYXkvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBa0Q7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1kZWxhdW5heVxcc3JjXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge2RlZmF1bHQgYXMgRGVsYXVuYXl9IGZyb20gXCIuL2RlbGF1bmF5LmpzXCI7XG5leHBvcnQge2RlZmF1bHQgYXMgVm9yb25vaX0gZnJvbSBcIi4vdm9yb25vaS5qc1wiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-delaunay/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-delaunay/src/path.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-delaunay/src/path.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Path)\n/* harmony export */ });\nconst epsilon = 1e-6;\n\nclass Path {\n  constructor() {\n    this._x0 = this._y0 = // start of current subpath\n    this._x1 = this._y1 = null; // end of current subpath\n    this._ = \"\";\n  }\n  moveTo(x, y) {\n    this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;\n  }\n  closePath() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  }\n  lineTo(x, y) {\n    this._ += `L${this._x1 = +x},${this._y1 = +y}`;\n  }\n  arc(x, y, r) {\n    x = +x, y = +y, r = +r;\n    const x0 = x + r;\n    const y0 = y;\n    if (r < 0) throw new Error(\"negative radius\");\n    if (this._x1 === null) this._ += `M${x0},${y0}`;\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) this._ += \"L\" + x0 + \",\" + y0;\n    if (!r) return;\n    this._ += `A${r},${r},0,1,1,${x - r},${y}A${r},${r},0,1,1,${this._x1 = x0},${this._y1 = y0}`;\n  }\n  rect(x, y, w, h) {\n    this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${+w}v${+h}h${-w}Z`;\n  }\n  value() {\n    return this._ || null;\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-delaunay/src/path.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-delaunay/src/polygon.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-delaunay/src/polygon.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Polygon)\n/* harmony export */ });\nclass Polygon {\n  constructor() {\n    this._ = [];\n  }\n  moveTo(x, y) {\n    this._.push([x, y]);\n  }\n  closePath() {\n    this._.push(this._[0].slice());\n  }\n  lineTo(x, y) {\n    this._.push([x, y]);\n  }\n  value() {\n    return this._.length ? this._ : null;\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZGVsYXVuYXkvc3JjL3BvbHlnb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtZGVsYXVuYXlcXHNyY1xccG9seWdvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBjbGFzcyBQb2x5Z29uIHtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5fID0gW107XG4gIH1cbiAgbW92ZVRvKHgsIHkpIHtcbiAgICB0aGlzLl8ucHVzaChbeCwgeV0pO1xuICB9XG4gIGNsb3NlUGF0aCgpIHtcbiAgICB0aGlzLl8ucHVzaCh0aGlzLl9bMF0uc2xpY2UoKSk7XG4gIH1cbiAgbGluZVRvKHgsIHkpIHtcbiAgICB0aGlzLl8ucHVzaChbeCwgeV0pO1xuICB9XG4gIHZhbHVlKCkge1xuICAgIHJldHVybiB0aGlzLl8ubGVuZ3RoID8gdGhpcy5fIDogbnVsbDtcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-delaunay/src/polygon.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-delaunay/src/voronoi.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-delaunay/src/voronoi.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Voronoi)\n/* harmony export */ });\n/* harmony import */ var _path_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./path.js */ \"(ssr)/./node_modules/d3-delaunay/src/path.js\");\n/* harmony import */ var _polygon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./polygon.js */ \"(ssr)/./node_modules/d3-delaunay/src/polygon.js\");\n\n\n\nclass Voronoi {\n  constructor(delaunay, [xmin, ymin, xmax, ymax] = [0, 0, 960, 500]) {\n    if (!((xmax = +xmax) >= (xmin = +xmin)) || !((ymax = +ymax) >= (ymin = +ymin))) throw new Error(\"invalid bounds\");\n    this.delaunay = delaunay;\n    this._circumcenters = new Float64Array(delaunay.points.length * 2);\n    this.vectors = new Float64Array(delaunay.points.length * 2);\n    this.xmax = xmax, this.xmin = xmin;\n    this.ymax = ymax, this.ymin = ymin;\n    this._init();\n  }\n  update() {\n    this.delaunay.update();\n    this._init();\n    return this;\n  }\n  _init() {\n    const {delaunay: {points, hull, triangles}, vectors} = this;\n    let bx, by; // lazily computed barycenter of the hull\n\n    // Compute circumcenters.\n    const circumcenters = this.circumcenters = this._circumcenters.subarray(0, triangles.length / 3 * 2);\n    for (let i = 0, j = 0, n = triangles.length, x, y; i < n; i += 3, j += 2) {\n      const t1 = triangles[i] * 2;\n      const t2 = triangles[i + 1] * 2;\n      const t3 = triangles[i + 2] * 2;\n      const x1 = points[t1];\n      const y1 = points[t1 + 1];\n      const x2 = points[t2];\n      const y2 = points[t2 + 1];\n      const x3 = points[t3];\n      const y3 = points[t3 + 1];\n\n      const dx = x2 - x1;\n      const dy = y2 - y1;\n      const ex = x3 - x1;\n      const ey = y3 - y1;\n      const ab = (dx * ey - dy * ex) * 2;\n\n      if (Math.abs(ab) < 1e-9) {\n        // For a degenerate triangle, the circumcenter is at the infinity, in a\n        // direction orthogonal to the halfedge and away from the “center” of\n        // the diagram <bx, by>, defined as the hull’s barycenter.\n        if (bx === undefined) {\n          bx = by = 0;\n          for (const i of hull) bx += points[i * 2], by += points[i * 2 + 1];\n          bx /= hull.length, by /= hull.length;\n        }\n        const a = 1e9 * Math.sign((bx - x1) * ey - (by - y1) * ex);\n        x = (x1 + x3) / 2 - a * ey;\n        y = (y1 + y3) / 2 + a * ex;\n      } else {\n        const d = 1 / ab;\n        const bl = dx * dx + dy * dy;\n        const cl = ex * ex + ey * ey;\n        x = x1 + (ey * bl - dy * cl) * d;\n        y = y1 + (dx * cl - ex * bl) * d;\n      }\n      circumcenters[j] = x;\n      circumcenters[j + 1] = y;\n    }\n\n    // Compute exterior cell rays.\n    let h = hull[hull.length - 1];\n    let p0, p1 = h * 4;\n    let x0, x1 = points[2 * h];\n    let y0, y1 = points[2 * h + 1];\n    vectors.fill(0);\n    for (let i = 0; i < hull.length; ++i) {\n      h = hull[i];\n      p0 = p1, x0 = x1, y0 = y1;\n      p1 = h * 4, x1 = points[2 * h], y1 = points[2 * h + 1];\n      vectors[p0 + 2] = vectors[p1] = y0 - y1;\n      vectors[p0 + 3] = vectors[p1 + 1] = x1 - x0;\n    }\n  }\n  render(context) {\n    const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : undefined;\n    const {delaunay: {halfedges, inedges, hull}, circumcenters, vectors} = this;\n    if (hull.length <= 1) return null;\n    for (let i = 0, n = halfedges.length; i < n; ++i) {\n      const j = halfedges[i];\n      if (j < i) continue;\n      const ti = Math.floor(i / 3) * 2;\n      const tj = Math.floor(j / 3) * 2;\n      const xi = circumcenters[ti];\n      const yi = circumcenters[ti + 1];\n      const xj = circumcenters[tj];\n      const yj = circumcenters[tj + 1];\n      this._renderSegment(xi, yi, xj, yj, context);\n    }\n    let h0, h1 = hull[hull.length - 1];\n    for (let i = 0; i < hull.length; ++i) {\n      h0 = h1, h1 = hull[i];\n      const t = Math.floor(inedges[h1] / 3) * 2;\n      const x = circumcenters[t];\n      const y = circumcenters[t + 1];\n      const v = h0 * 4;\n      const p = this._project(x, y, vectors[v + 2], vectors[v + 3]);\n      if (p) this._renderSegment(x, y, p[0], p[1], context);\n    }\n    return buffer && buffer.value();\n  }\n  renderBounds(context) {\n    const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : undefined;\n    context.rect(this.xmin, this.ymin, this.xmax - this.xmin, this.ymax - this.ymin);\n    return buffer && buffer.value();\n  }\n  renderCell(i, context) {\n    const buffer = context == null ? context = new _path_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : undefined;\n    const points = this._clip(i);\n    if (points === null || !points.length) return;\n    context.moveTo(points[0], points[1]);\n    let n = points.length;\n    while (points[0] === points[n-2] && points[1] === points[n-1] && n > 1) n -= 2;\n    for (let i = 2; i < n; i += 2) {\n      if (points[i] !== points[i-2] || points[i+1] !== points[i-1])\n        context.lineTo(points[i], points[i + 1]);\n    }\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  *cellPolygons() {\n    const {delaunay: {points}} = this;\n    for (let i = 0, n = points.length / 2; i < n; ++i) {\n      const cell = this.cellPolygon(i);\n      if (cell) cell.index = i, yield cell;\n    }\n  }\n  cellPolygon(i) {\n    const polygon = new _polygon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n    this.renderCell(i, polygon);\n    return polygon.value();\n  }\n  _renderSegment(x0, y0, x1, y1, context) {\n    let S;\n    const c0 = this._regioncode(x0, y0);\n    const c1 = this._regioncode(x1, y1);\n    if (c0 === 0 && c1 === 0) {\n      context.moveTo(x0, y0);\n      context.lineTo(x1, y1);\n    } else if (S = this._clipSegment(x0, y0, x1, y1, c0, c1)) {\n      context.moveTo(S[0], S[1]);\n      context.lineTo(S[2], S[3]);\n    }\n  }\n  contains(i, x, y) {\n    if ((x = +x, x !== x) || (y = +y, y !== y)) return false;\n    return this.delaunay._step(i, x, y) === i;\n  }\n  *neighbors(i) {\n    const ci = this._clip(i);\n    if (ci) for (const j of this.delaunay.neighbors(i)) {\n      const cj = this._clip(j);\n      // find the common edge\n      if (cj) loop: for (let ai = 0, li = ci.length; ai < li; ai += 2) {\n        for (let aj = 0, lj = cj.length; aj < lj; aj += 2) {\n          if (ci[ai] === cj[aj]\n              && ci[ai + 1] === cj[aj + 1]\n              && ci[(ai + 2) % li] === cj[(aj + lj - 2) % lj]\n              && ci[(ai + 3) % li] === cj[(aj + lj - 1) % lj]) {\n            yield j;\n            break loop;\n          }\n        }\n      }\n    }\n  }\n  _cell(i) {\n    const {circumcenters, delaunay: {inedges, halfedges, triangles}} = this;\n    const e0 = inedges[i];\n    if (e0 === -1) return null; // coincident point\n    const points = [];\n    let e = e0;\n    do {\n      const t = Math.floor(e / 3);\n      points.push(circumcenters[t * 2], circumcenters[t * 2 + 1]);\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) break; // bad triangulation\n      e = halfedges[e];\n    } while (e !== e0 && e !== -1);\n    return points;\n  }\n  _clip(i) {\n    // degenerate case (1 valid point: return the box)\n    if (i === 0 && this.delaunay.hull.length === 1) {\n      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];\n    }\n    const points = this._cell(i);\n    if (points === null) return null;\n    const {vectors: V} = this;\n    const v = i * 4;\n    return this._simplify(V[v] || V[v + 1]\n        ? this._clipInfinite(i, points, V[v], V[v + 1], V[v + 2], V[v + 3])\n        : this._clipFinite(i, points));\n  }\n  _clipFinite(i, points) {\n    const n = points.length;\n    let P = null;\n    let x0, y0, x1 = points[n - 2], y1 = points[n - 1];\n    let c0, c1 = this._regioncode(x1, y1);\n    let e0, e1 = 0;\n    for (let j = 0; j < n; j += 2) {\n      x0 = x1, y0 = y1, x1 = points[j], y1 = points[j + 1];\n      c0 = c1, c1 = this._regioncode(x1, y1);\n      if (c0 === 0 && c1 === 0) {\n        e0 = e1, e1 = 0;\n        if (P) P.push(x1, y1);\n        else P = [x1, y1];\n      } else {\n        let S, sx0, sy0, sx1, sy1;\n        if (c0 === 0) {\n          if ((S = this._clipSegment(x0, y0, x1, y1, c0, c1)) === null) continue;\n          [sx0, sy0, sx1, sy1] = S;\n        } else {\n          if ((S = this._clipSegment(x1, y1, x0, y0, c1, c0)) === null) continue;\n          [sx1, sy1, sx0, sy0] = S;\n          e0 = e1, e1 = this._edgecode(sx0, sy0);\n          if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n          if (P) P.push(sx0, sy0);\n          else P = [sx0, sy0];\n        }\n        e0 = e1, e1 = this._edgecode(sx1, sy1);\n        if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n        if (P) P.push(sx1, sy1);\n        else P = [sx1, sy1];\n      }\n    }\n    if (P) {\n      e0 = e1, e1 = this._edgecode(P[0], P[1]);\n      if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n    } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];\n    }\n    return P;\n  }\n  _clipSegment(x0, y0, x1, y1, c0, c1) {\n    // for more robustness, always consider the segment in the same order\n    const flip = c0 < c1;\n    if (flip) [x0, y0, x1, y1, c0, c1] = [x1, y1, x0, y0, c1, c0];\n    while (true) {\n      if (c0 === 0 && c1 === 0) return flip ? [x1, y1, x0, y0] : [x0, y0, x1, y1];\n      if (c0 & c1) return null;\n      let x, y, c = c0 || c1;\n      if (c & 0b1000) x = x0 + (x1 - x0) * (this.ymax - y0) / (y1 - y0), y = this.ymax;\n      else if (c & 0b0100) x = x0 + (x1 - x0) * (this.ymin - y0) / (y1 - y0), y = this.ymin;\n      else if (c & 0b0010) y = y0 + (y1 - y0) * (this.xmax - x0) / (x1 - x0), x = this.xmax;\n      else y = y0 + (y1 - y0) * (this.xmin - x0) / (x1 - x0), x = this.xmin;\n      if (c0) x0 = x, y0 = y, c0 = this._regioncode(x0, y0);\n      else x1 = x, y1 = y, c1 = this._regioncode(x1, y1);\n    }\n  }\n  _clipInfinite(i, points, vx0, vy0, vxn, vyn) {\n    let P = Array.from(points), p;\n    if (p = this._project(P[0], P[1], vx0, vy0)) P.unshift(p[0], p[1]);\n    if (p = this._project(P[P.length - 2], P[P.length - 1], vxn, vyn)) P.push(p[0], p[1]);\n    if (P = this._clipFinite(i, P)) {\n      for (let j = 0, n = P.length, c0, c1 = this._edgecode(P[n - 2], P[n - 1]); j < n; j += 2) {\n        c0 = c1, c1 = this._edgecode(P[j], P[j + 1]);\n        if (c0 && c1) j = this._edge(i, c0, c1, P, j), n = P.length;\n      }\n    } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n      P = [this.xmin, this.ymin, this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax];\n    }\n    return P;\n  }\n  _edge(i, e0, e1, P, j) {\n    while (e0 !== e1) {\n      let x, y;\n      switch (e0) {\n        case 0b0101: e0 = 0b0100; continue; // top-left\n        case 0b0100: e0 = 0b0110, x = this.xmax, y = this.ymin; break; // top\n        case 0b0110: e0 = 0b0010; continue; // top-right\n        case 0b0010: e0 = 0b1010, x = this.xmax, y = this.ymax; break; // right\n        case 0b1010: e0 = 0b1000; continue; // bottom-right\n        case 0b1000: e0 = 0b1001, x = this.xmin, y = this.ymax; break; // bottom\n        case 0b1001: e0 = 0b0001; continue; // bottom-left\n        case 0b0001: e0 = 0b0101, x = this.xmin, y = this.ymin; break; // left\n      }\n      // Note: this implicitly checks for out of bounds: if P[j] or P[j+1] are\n      // undefined, the conditional statement will be executed.\n      if ((P[j] !== x || P[j + 1] !== y) && this.contains(i, x, y)) {\n        P.splice(j, 0, x, y), j += 2;\n      }\n    }\n    return j;\n  }\n  _project(x0, y0, vx, vy) {\n    let t = Infinity, c, x, y;\n    if (vy < 0) { // top\n      if (y0 <= this.ymin) return null;\n      if ((c = (this.ymin - y0) / vy) < t) y = this.ymin, x = x0 + (t = c) * vx;\n    } else if (vy > 0) { // bottom\n      if (y0 >= this.ymax) return null;\n      if ((c = (this.ymax - y0) / vy) < t) y = this.ymax, x = x0 + (t = c) * vx;\n    }\n    if (vx > 0) { // right\n      if (x0 >= this.xmax) return null;\n      if ((c = (this.xmax - x0) / vx) < t) x = this.xmax, y = y0 + (t = c) * vy;\n    } else if (vx < 0) { // left\n      if (x0 <= this.xmin) return null;\n      if ((c = (this.xmin - x0) / vx) < t) x = this.xmin, y = y0 + (t = c) * vy;\n    }\n    return [x, y];\n  }\n  _edgecode(x, y) {\n    return (x === this.xmin ? 0b0001\n        : x === this.xmax ? 0b0010 : 0b0000)\n        | (y === this.ymin ? 0b0100\n        : y === this.ymax ? 0b1000 : 0b0000);\n  }\n  _regioncode(x, y) {\n    return (x < this.xmin ? 0b0001\n        : x > this.xmax ? 0b0010 : 0b0000)\n        | (y < this.ymin ? 0b0100\n        : y > this.ymax ? 0b1000 : 0b0000);\n  }\n  _simplify(P) {\n    if (P && P.length > 4) {\n      for (let i = 0; i < P.length; i+= 2) {\n        const j = (i + 2) % P.length, k = (i + 4) % P.length;\n        if (P[i] === P[j] && P[j] === P[k] || P[i + 1] === P[j + 1] && P[j + 1] === P[k + 1]) {\n          P.splice(j, 2), i -= 2;\n        }\n      }\n      if (!P.length) P = null;\n    }\n    return P;\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-delaunay/src/voronoi.js\n");

/***/ })

};
;