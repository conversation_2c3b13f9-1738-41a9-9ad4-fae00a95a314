"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_chunks_mermaid_core_kanban-definition-NDS4AKOZ_mjs"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/kanban-definition-NDS4AKOZ.mjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/kanban-definition-NDS4AKOZ.mjs ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: () => (/* binding */ diagram)\n/* harmony export */ });\n/* harmony import */ var _chunk_6JRP7KZX_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-6JRP7KZX.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-6JRP7KZX.mjs\");\n/* harmony import */ var _chunk_HRU6DDCH_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-HRU6DDCH.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-HRU6DDCH.mjs\");\n/* harmony import */ var _chunk_K557N5IZ_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-K557N5IZ.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-K557N5IZ.mjs\");\n/* harmony import */ var _chunk_H2D2JQ3I_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-H2D2JQ3I.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-H2D2JQ3I.mjs\");\n/* harmony import */ var _chunk_C3MQ5ANM_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-C3MQ5ANM.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-C3MQ5ANM.mjs\");\n/* harmony import */ var _chunk_O4NI6UNU_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-O4NI6UNU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-O4NI6UNU.mjs\");\n/* harmony import */ var _chunk_7B677QYD_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./chunk-7B677QYD.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-7B677QYD.mjs\");\n/* harmony import */ var _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./chunk-YTJNT7DU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-YTJNT7DU.mjs\");\n/* harmony import */ var khroma__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! khroma */ \"(app-pages-browser)/./node_modules/khroma/dist/methods/is_dark.js\");\n/* harmony import */ var khroma__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! khroma */ \"(app-pages-browser)/./node_modules/khroma/dist/methods/lighten.js\");\n/* harmony import */ var khroma__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! khroma */ \"(app-pages-browser)/./node_modules/khroma/dist/methods/darken.js\");\n\n\n\n\n\n\n\n\n\n// src/diagrams/kanban/parser/kanban.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 4], $V1 = [1, 13], $V2 = [1, 12], $V3 = [1, 15], $V4 = [1, 16], $V5 = [1, 20], $V6 = [1, 19], $V7 = [6, 7, 8], $V8 = [1, 26], $V9 = [1, 24], $Va = [1, 25], $Vb = [6, 7, 11], $Vc = [1, 31], $Vd = [6, 7, 11, 24], $Ve = [1, 6, 13, 16, 17, 20, 23], $Vf = [1, 35], $Vg = [1, 36], $Vh = [1, 6, 7, 11, 13, 16, 17, 20, 23], $Vi = [1, 38];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"mindMap\": 4, \"spaceLines\": 5, \"SPACELINE\": 6, \"NL\": 7, \"KANBAN\": 8, \"document\": 9, \"stop\": 10, \"EOF\": 11, \"statement\": 12, \"SPACELIST\": 13, \"node\": 14, \"shapeData\": 15, \"ICON\": 16, \"CLASS\": 17, \"nodeWithId\": 18, \"nodeWithoutId\": 19, \"NODE_DSTART\": 20, \"NODE_DESCR\": 21, \"NODE_DEND\": 22, \"NODE_ID\": 23, \"SHAPE_DATA\": 24, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 6: \"SPACELINE\", 7: \"NL\", 8: \"KANBAN\", 11: \"EOF\", 13: \"SPACELIST\", 16: \"ICON\", 17: \"CLASS\", 20: \"NODE_DSTART\", 21: \"NODE_DESCR\", 22: \"NODE_DEND\", 23: \"NODE_ID\", 24: \"SHAPE_DATA\" },\n    productions_: [0, [3, 1], [3, 2], [5, 1], [5, 2], [5, 2], [4, 2], [4, 3], [10, 1], [10, 1], [10, 1], [10, 2], [10, 2], [9, 3], [9, 2], [12, 3], [12, 2], [12, 2], [12, 2], [12, 1], [12, 2], [12, 1], [12, 1], [12, 1], [12, 1], [14, 1], [14, 1], [19, 3], [18, 1], [18, 4], [15, 2], [15, 1]],\n    performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 6:\n        case 7:\n          return yy;\n          break;\n        case 8:\n          yy.getLogger().trace(\"Stop NL \");\n          break;\n        case 9:\n          yy.getLogger().trace(\"Stop EOF \");\n          break;\n        case 11:\n          yy.getLogger().trace(\"Stop NL2 \");\n          break;\n        case 12:\n          yy.getLogger().trace(\"Stop EOF2 \");\n          break;\n        case 15:\n          yy.getLogger().info(\"Node: \", $$[$0 - 1].id);\n          yy.addNode($$[$0 - 2].length, $$[$0 - 1].id, $$[$0 - 1].descr, $$[$0 - 1].type, $$[$0]);\n          break;\n        case 16:\n          yy.getLogger().info(\"Node: \", $$[$0].id);\n          yy.addNode($$[$0 - 1].length, $$[$0].id, $$[$0].descr, $$[$0].type);\n          break;\n        case 17:\n          yy.getLogger().trace(\"Icon: \", $$[$0]);\n          yy.decorateNode({ icon: $$[$0] });\n          break;\n        case 18:\n        case 23:\n          yy.decorateNode({ class: $$[$0] });\n          break;\n        case 19:\n          yy.getLogger().trace(\"SPACELIST\");\n          break;\n        case 20:\n          yy.getLogger().trace(\"Node: \", $$[$0 - 1].id);\n          yy.addNode(0, $$[$0 - 1].id, $$[$0 - 1].descr, $$[$0 - 1].type, $$[$0]);\n          break;\n        case 21:\n          yy.getLogger().trace(\"Node: \", $$[$0].id);\n          yy.addNode(0, $$[$0].id, $$[$0].descr, $$[$0].type);\n          break;\n        case 22:\n          yy.decorateNode({ icon: $$[$0] });\n          break;\n        case 27:\n          yy.getLogger().trace(\"node found ..\", $$[$0 - 2]);\n          this.$ = { id: $$[$0 - 1], descr: $$[$0 - 1], type: yy.getType($$[$0 - 2], $$[$0]) };\n          break;\n        case 28:\n          this.$ = { id: $$[$0], descr: $$[$0], type: 0 };\n          break;\n        case 29:\n          yy.getLogger().trace(\"node found ..\", $$[$0 - 3]);\n          this.$ = { id: $$[$0 - 3], descr: $$[$0 - 1], type: yy.getType($$[$0 - 2], $$[$0]) };\n          break;\n        case 30:\n          this.$ = $$[$0 - 1] + $$[$0];\n          break;\n        case 31:\n          this.$ = $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: 2, 5: 3, 6: [1, 5], 8: $V0 }, { 1: [3] }, { 1: [2, 1] }, { 4: 6, 6: [1, 7], 7: [1, 8], 8: $V0 }, { 6: $V1, 7: [1, 10], 9: 9, 12: 11, 13: $V2, 14: 14, 16: $V3, 17: $V4, 18: 17, 19: 18, 20: $V5, 23: $V6 }, o($V7, [2, 3]), { 1: [2, 2] }, o($V7, [2, 4]), o($V7, [2, 5]), { 1: [2, 6], 6: $V1, 12: 21, 13: $V2, 14: 14, 16: $V3, 17: $V4, 18: 17, 19: 18, 20: $V5, 23: $V6 }, { 6: $V1, 9: 22, 12: 11, 13: $V2, 14: 14, 16: $V3, 17: $V4, 18: 17, 19: 18, 20: $V5, 23: $V6 }, { 6: $V8, 7: $V9, 10: 23, 11: $Va }, o($Vb, [2, 24], { 18: 17, 19: 18, 14: 27, 16: [1, 28], 17: [1, 29], 20: $V5, 23: $V6 }), o($Vb, [2, 19]), o($Vb, [2, 21], { 15: 30, 24: $Vc }), o($Vb, [2, 22]), o($Vb, [2, 23]), o($Vd, [2, 25]), o($Vd, [2, 26]), o($Vd, [2, 28], { 20: [1, 32] }), { 21: [1, 33] }, { 6: $V8, 7: $V9, 10: 34, 11: $Va }, { 1: [2, 7], 6: $V1, 12: 21, 13: $V2, 14: 14, 16: $V3, 17: $V4, 18: 17, 19: 18, 20: $V5, 23: $V6 }, o($Ve, [2, 14], { 7: $Vf, 11: $Vg }), o($Vh, [2, 8]), o($Vh, [2, 9]), o($Vh, [2, 10]), o($Vb, [2, 16], { 15: 37, 24: $Vc }), o($Vb, [2, 17]), o($Vb, [2, 18]), o($Vb, [2, 20], { 24: $Vi }), o($Vd, [2, 31]), { 21: [1, 39] }, { 22: [1, 40] }, o($Ve, [2, 13], { 7: $Vf, 11: $Vg }), o($Vh, [2, 11]), o($Vh, [2, 12]), o($Vb, [2, 15], { 24: $Vi }), o($Vd, [2, 30]), { 22: [1, 41] }, o($Vd, [2, 27]), o($Vd, [2, 29])],\n    defaultActions: { 2: [2, 1], 6: [2, 2] },\n    parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.pushState(\"shapeData\");\n            yy_.yytext = \"\";\n            return 24;\n            break;\n          case 1:\n            this.pushState(\"shapeDataStr\");\n            return 24;\n            break;\n          case 2:\n            this.popState();\n            return 24;\n            break;\n          case 3:\n            const re = /\\n\\s*/g;\n            yy_.yytext = yy_.yytext.replace(re, \"<br/>\");\n            return 24;\n            break;\n          case 4:\n            return 24;\n            break;\n          case 5:\n            this.popState();\n            break;\n          case 6:\n            yy.getLogger().trace(\"Found comment\", yy_.yytext);\n            return 6;\n            break;\n          case 7:\n            return 8;\n            break;\n          case 8:\n            this.begin(\"CLASS\");\n            break;\n          case 9:\n            this.popState();\n            return 17;\n            break;\n          case 10:\n            this.popState();\n            break;\n          case 11:\n            yy.getLogger().trace(\"Begin icon\");\n            this.begin(\"ICON\");\n            break;\n          case 12:\n            yy.getLogger().trace(\"SPACELINE\");\n            return 6;\n            break;\n          case 13:\n            return 7;\n            break;\n          case 14:\n            return 16;\n            break;\n          case 15:\n            yy.getLogger().trace(\"end icon\");\n            this.popState();\n            break;\n          case 16:\n            yy.getLogger().trace(\"Exploding node\");\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 17:\n            yy.getLogger().trace(\"Cloud\");\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 18:\n            yy.getLogger().trace(\"Explosion Bang\");\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 19:\n            yy.getLogger().trace(\"Cloud Bang\");\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 20:\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 21:\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 22:\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 23:\n            this.begin(\"NODE\");\n            return 20;\n            break;\n          case 24:\n            return 13;\n            break;\n          case 25:\n            return 23;\n            break;\n          case 26:\n            return 11;\n            break;\n          case 27:\n            this.begin(\"NSTR2\");\n            break;\n          case 28:\n            return \"NODE_DESCR\";\n            break;\n          case 29:\n            this.popState();\n            break;\n          case 30:\n            yy.getLogger().trace(\"Starting NSTR\");\n            this.begin(\"NSTR\");\n            break;\n          case 31:\n            yy.getLogger().trace(\"description:\", yy_.yytext);\n            return \"NODE_DESCR\";\n            break;\n          case 32:\n            this.popState();\n            break;\n          case 33:\n            this.popState();\n            yy.getLogger().trace(\"node end ))\");\n            return \"NODE_DEND\";\n            break;\n          case 34:\n            this.popState();\n            yy.getLogger().trace(\"node end )\");\n            return \"NODE_DEND\";\n            break;\n          case 35:\n            this.popState();\n            yy.getLogger().trace(\"node end ...\", yy_.yytext);\n            return \"NODE_DEND\";\n            break;\n          case 36:\n            this.popState();\n            yy.getLogger().trace(\"node end ((\");\n            return \"NODE_DEND\";\n            break;\n          case 37:\n            this.popState();\n            yy.getLogger().trace(\"node end (-\");\n            return \"NODE_DEND\";\n            break;\n          case 38:\n            this.popState();\n            yy.getLogger().trace(\"node end (-\");\n            return \"NODE_DEND\";\n            break;\n          case 39:\n            this.popState();\n            yy.getLogger().trace(\"node end ((\");\n            return \"NODE_DEND\";\n            break;\n          case 40:\n            this.popState();\n            yy.getLogger().trace(\"node end ((\");\n            return \"NODE_DEND\";\n            break;\n          case 41:\n            yy.getLogger().trace(\"Long description:\", yy_.yytext);\n            return 21;\n            break;\n          case 42:\n            yy.getLogger().trace(\"Long description:\", yy_.yytext);\n            return 21;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:@\\{)/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\\\"]+)/i, /^(?:[^}^\"]+)/i, /^(?:\\})/i, /^(?:\\s*%%.*)/i, /^(?:kanban\\b)/i, /^(?::::)/i, /^(?:.+)/i, /^(?:\\n)/i, /^(?:::icon\\()/i, /^(?:[\\s]+[\\n])/i, /^(?:[\\n]+)/i, /^(?:[^\\)]+)/i, /^(?:\\))/i, /^(?:-\\))/i, /^(?:\\(-)/i, /^(?:\\)\\))/i, /^(?:\\))/i, /^(?:\\(\\()/i, /^(?:\\{\\{)/i, /^(?:\\()/i, /^(?:\\[)/i, /^(?:[\\s]+)/i, /^(?:[^\\(\\[\\n\\)\\{\\}@]+)/i, /^(?:$)/i, /^(?:[\"][`])/i, /^(?:[^`\"]+)/i, /^(?:[`][\"])/i, /^(?:[\"])/i, /^(?:[^\"]+)/i, /^(?:[\"])/i, /^(?:[\\)]\\))/i, /^(?:[\\)])/i, /^(?:[\\]])/i, /^(?:\\}\\})/i, /^(?:\\(-)/i, /^(?:-\\))/i, /^(?:\\(\\()/i, /^(?:\\()/i, /^(?:[^\\)\\]\\(\\}]+)/i, /^(?:.+(?!\\(\\())/i],\n      conditions: { \"shapeDataEndBracket\": { \"rules\": [], \"inclusive\": false }, \"shapeDataStr\": { \"rules\": [2, 3], \"inclusive\": false }, \"shapeData\": { \"rules\": [1, 4, 5], \"inclusive\": false }, \"CLASS\": { \"rules\": [9, 10], \"inclusive\": false }, \"ICON\": { \"rules\": [14, 15], \"inclusive\": false }, \"NSTR2\": { \"rules\": [28, 29], \"inclusive\": false }, \"NSTR\": { \"rules\": [31, 32], \"inclusive\": false }, \"NODE\": { \"rules\": [27, 30, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 6, 7, 8, 11, 12, 13, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar kanban_default = parser;\n\n// src/diagrams/kanban/kanbanDb.ts\nvar nodes = [];\nvar sections = [];\nvar cnt = 0;\nvar elements = {};\nvar clear = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(() => {\n  nodes = [];\n  sections = [];\n  cnt = 0;\n  elements = {};\n}, \"clear\");\nvar getSection = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)((level) => {\n  if (nodes.length === 0) {\n    return null;\n  }\n  const sectionLevel = nodes[0].level;\n  let lastSection = null;\n  for (let i = nodes.length - 1; i >= 0; i--) {\n    if (nodes[i].level === sectionLevel && !lastSection) {\n      lastSection = nodes[i];\n    }\n    if (nodes[i].level < sectionLevel) {\n      throw new Error('Items without section detected, found section (\"' + nodes[i].label + '\")');\n    }\n  }\n  if (level === lastSection?.level) {\n    return null;\n  }\n  return lastSection;\n}, \"getSection\");\nvar getSections = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function() {\n  return sections;\n}, \"getSections\");\nvar getData = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(function() {\n  const edges = [];\n  const _nodes = [];\n  const sections2 = getSections();\n  const conf = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.getConfig2)();\n  for (const section of sections2) {\n    const node = {\n      id: section.id,\n      label: (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.sanitizeText)(section.label ?? \"\", conf),\n      isGroup: true,\n      ticket: section.ticket,\n      shape: \"kanbanSection\",\n      level: section.level,\n      look: conf.look\n    };\n    _nodes.push(node);\n    const children = nodes.filter((n) => n.parentId === section.id);\n    for (const item of children) {\n      const childNode = {\n        id: item.id,\n        parentId: section.id,\n        label: (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.sanitizeText)(item.label ?? \"\", conf),\n        isGroup: false,\n        ticket: item?.ticket,\n        priority: item?.priority,\n        assigned: item?.assigned,\n        icon: item?.icon,\n        shape: \"kanbanItem\",\n        level: item.level,\n        rx: 5,\n        ry: 5,\n        cssStyles: [\"text-align: left\"]\n      };\n      _nodes.push(childNode);\n    }\n  }\n  return { nodes: _nodes, edges, other: {}, config: (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.getConfig2)() };\n}, \"getData\");\nvar addNode = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)((level, id, descr, type, shapeData) => {\n  const conf = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.getConfig2)();\n  let padding = conf.mindmap?.padding ?? _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.defaultConfig_default.mindmap.padding;\n  switch (type) {\n    case nodeType.ROUNDED_RECT:\n    case nodeType.RECT:\n    case nodeType.HEXAGON:\n      padding *= 2;\n  }\n  const node = {\n    id: (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.sanitizeText)(id, conf) || \"kbn\" + cnt++,\n    level,\n    label: (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.sanitizeText)(descr, conf),\n    width: conf.mindmap?.maxNodeWidth ?? _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.defaultConfig_default.mindmap.maxNodeWidth,\n    padding,\n    isGroup: false\n  };\n  if (shapeData !== void 0) {\n    let yamlData;\n    if (!shapeData.includes(\"\\n\")) {\n      yamlData = \"{\\n\" + shapeData + \"\\n}\";\n    } else {\n      yamlData = shapeData + \"\\n\";\n    }\n    const doc = (0,_chunk_6JRP7KZX_mjs__WEBPACK_IMPORTED_MODULE_0__.load)(yamlData, { schema: _chunk_6JRP7KZX_mjs__WEBPACK_IMPORTED_MODULE_0__.JSON_SCHEMA });\n    if (doc.shape && (doc.shape !== doc.shape.toLowerCase() || doc.shape.includes(\"_\"))) {\n      throw new Error(`No such shape: ${doc.shape}. Shape names should be lowercase.`);\n    }\n    if (doc?.shape && doc.shape === \"kanbanItem\") {\n      node.shape = doc?.shape;\n    }\n    if (doc?.label) {\n      node.label = doc?.label;\n    }\n    if (doc?.icon) {\n      node.icon = doc?.icon.toString();\n    }\n    if (doc?.assigned) {\n      node.assigned = doc?.assigned.toString();\n    }\n    if (doc?.ticket) {\n      node.ticket = doc?.ticket.toString();\n    }\n    if (doc?.priority) {\n      node.priority = doc?.priority;\n    }\n  }\n  const section = getSection(level);\n  if (section) {\n    node.parentId = section.id || \"kbn\" + cnt++;\n  } else {\n    sections.push(node);\n  }\n  nodes.push(node);\n}, \"addNode\");\nvar nodeType = {\n  DEFAULT: 0,\n  NO_BORDER: 0,\n  ROUNDED_RECT: 1,\n  RECT: 2,\n  CIRCLE: 3,\n  CLOUD: 4,\n  BANG: 5,\n  HEXAGON: 6\n};\nvar getType = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)((startStr, endStr) => {\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.log.debug(\"In get type\", startStr, endStr);\n  switch (startStr) {\n    case \"[\":\n      return nodeType.RECT;\n    case \"(\":\n      return endStr === \")\" ? nodeType.ROUNDED_RECT : nodeType.CLOUD;\n    case \"((\":\n      return nodeType.CIRCLE;\n    case \")\":\n      return nodeType.CLOUD;\n    case \"))\":\n      return nodeType.BANG;\n    case \"{{\":\n      return nodeType.HEXAGON;\n    default:\n      return nodeType.DEFAULT;\n  }\n}, \"getType\");\nvar setElementForId = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)((id, element) => {\n  elements[id] = element;\n}, \"setElementForId\");\nvar decorateNode = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)((decoration) => {\n  if (!decoration) {\n    return;\n  }\n  const config = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.getConfig2)();\n  const node = nodes[nodes.length - 1];\n  if (decoration.icon) {\n    node.icon = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.sanitizeText)(decoration.icon, config);\n  }\n  if (decoration.class) {\n    node.cssClasses = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.sanitizeText)(decoration.class, config);\n  }\n}, \"decorateNode\");\nvar type2Str = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)((type) => {\n  switch (type) {\n    case nodeType.DEFAULT:\n      return \"no-border\";\n    case nodeType.RECT:\n      return \"rect\";\n    case nodeType.ROUNDED_RECT:\n      return \"rounded-rect\";\n    case nodeType.CIRCLE:\n      return \"circle\";\n    case nodeType.CLOUD:\n      return \"cloud\";\n    case nodeType.BANG:\n      return \"bang\";\n    case nodeType.HEXAGON:\n      return \"hexgon\";\n    // cspell: disable-line\n    default:\n      return \"no-border\";\n  }\n}, \"type2Str\");\nvar getLogger = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(() => _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.log, \"getLogger\");\nvar getElementById = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)((id) => elements[id], \"getElementById\");\nvar db = {\n  clear,\n  addNode,\n  getSections,\n  getData,\n  nodeType,\n  getType,\n  setElementForId,\n  decorateNode,\n  type2Str,\n  getLogger,\n  getElementById\n};\nvar kanbanDb_default = db;\n\n// src/diagrams/kanban/kanbanRenderer.ts\nvar draw = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)(async (text, id, _version, diagObj) => {\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.log.debug(\"Rendering kanban diagram\\n\" + text);\n  const db2 = diagObj.db;\n  const data4Layout = db2.getData();\n  const conf = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.getConfig2)();\n  conf.htmlLabels = false;\n  const svg = (0,_chunk_7B677QYD_mjs__WEBPACK_IMPORTED_MODULE_6__.selectSvgElement)(id);\n  const sectionsElem = svg.append(\"g\");\n  sectionsElem.attr(\"class\", \"sections\");\n  const nodesElem = svg.append(\"g\");\n  nodesElem.attr(\"class\", \"items\");\n  const sections2 = data4Layout.nodes.filter(\n    // TODO: TypeScript 5.5 will infer this predicate automatically\n    (node) => node.isGroup\n  );\n  let cnt2 = 0;\n  const padding = 10;\n  const sectionObjects = [];\n  let maxLabelHeight = 25;\n  for (const section of sections2) {\n    const WIDTH = conf?.kanban?.sectionWidth || 200;\n    cnt2 = cnt2 + 1;\n    section.x = WIDTH * cnt2 + (cnt2 - 1) * padding / 2;\n    section.width = WIDTH;\n    section.y = 0;\n    section.height = WIDTH * 3;\n    section.rx = 5;\n    section.ry = 5;\n    section.cssClasses = section.cssClasses + \" section-\" + cnt2;\n    const sectionObj = await (0,_chunk_HRU6DDCH_mjs__WEBPACK_IMPORTED_MODULE_1__.insertCluster)(sectionsElem, section);\n    maxLabelHeight = Math.max(maxLabelHeight, sectionObj?.labelBBox?.height);\n    sectionObjects.push(sectionObj);\n  }\n  let i = 0;\n  for (const section of sections2) {\n    const sectionObj = sectionObjects[i];\n    i = i + 1;\n    const WIDTH = conf?.kanban?.sectionWidth || 200;\n    const top = -WIDTH * 3 / 2 + maxLabelHeight;\n    let y = top;\n    const sectionItems = data4Layout.nodes.filter((node) => node.parentId === section.id);\n    for (const item of sectionItems) {\n      if (item.isGroup) {\n        throw new Error(\"Groups within groups are not allowed in Kanban diagrams\");\n      }\n      item.x = section.x;\n      item.width = WIDTH - 1.5 * padding;\n      const nodeEl = await (0,_chunk_HRU6DDCH_mjs__WEBPACK_IMPORTED_MODULE_1__.insertNode)(nodesElem, item, { config: conf });\n      const bbox = nodeEl.node().getBBox();\n      item.y = y + bbox.height / 2;\n      await (0,_chunk_HRU6DDCH_mjs__WEBPACK_IMPORTED_MODULE_1__.positionNode)(item);\n      y = item.y + bbox.height / 2 + padding / 2;\n    }\n    const rect = sectionObj.cluster.select(\"rect\");\n    const height = Math.max(y - top + 3 * padding, 50) + (maxLabelHeight - 25);\n    rect.attr(\"height\", height);\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.setupGraphViewbox)(\n    void 0,\n    svg,\n    conf.mindmap?.padding ?? _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.defaultConfig_default.kanban.padding,\n    conf.mindmap?.useMaxWidth ?? _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.defaultConfig_default.kanban.useMaxWidth\n  );\n}, \"draw\");\nvar kanbanRenderer_default = {\n  draw\n};\n\n// src/diagrams/kanban/styles.ts\n\nvar genSections = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)((options) => {\n  let sections2 = \"\";\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    options[\"lineColor\" + i] = options[\"lineColor\" + i] || options[\"cScaleInv\" + i];\n    if ((0,khroma__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(options[\"lineColor\" + i])) {\n      options[\"lineColor\" + i] = (0,khroma__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(options[\"lineColor\" + i], 20);\n    } else {\n      options[\"lineColor\" + i] = (0,khroma__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(options[\"lineColor\" + i], 20);\n    }\n  }\n  const adjuster = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)((color, level) => options.darkMode ? (0,khroma__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(color, level) : (0,khroma__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(color, level), \"adjuster\");\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    const sw = \"\" + (17 - 3 * i);\n    sections2 += `\n    .section-${i - 1} rect, .section-${i - 1} path, .section-${i - 1} circle, .section-${i - 1} polygon, .section-${i - 1} path  {\n      fill: ${adjuster(options[\"cScale\" + i], 10)};\n      stroke: ${adjuster(options[\"cScale\" + i], 10)};\n\n    }\n    .section-${i - 1} text {\n     fill: ${options[\"cScaleLabel\" + i]};\n    }\n    .node-icon-${i - 1} {\n      font-size: 40px;\n      color: ${options[\"cScaleLabel\" + i]};\n    }\n    .section-edge-${i - 1}{\n      stroke: ${options[\"cScale\" + i]};\n    }\n    .edge-depth-${i - 1}{\n      stroke-width: ${sw};\n    }\n    .section-${i - 1} line {\n      stroke: ${options[\"cScaleInv\" + i]} ;\n      stroke-width: 3;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${options.background};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n\n  .kanban-ticket-link {\n    fill: ${options.background};\n    stroke: ${options.nodeBorder};\n    text-decoration: underline;\n  }\n    `;\n  }\n  return sections2;\n}, \"genSections\");\nvar getStyles = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_7__.__name)((options) => `\n  .edge {\n    stroke-width: 3;\n  }\n  ${genSections(options)}\n  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {\n    fill: ${options.git0};\n  }\n  .section-root text {\n    fill: ${options.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .cluster-label, .label {\n    color: ${options.textColor};\n    fill: ${options.textColor};\n    }\n  .kanban-label {\n    dy: 1em;\n    alignment-baseline: middle;\n    text-anchor: middle;\n    dominant-baseline: middle;\n    text-align: center;\n  }\n`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/kanban/kanban-definition.ts\nvar diagram = {\n  db: kanbanDb_default,\n  renderer: kanbanRenderer_default,\n  parser: kanban_default,\n  styles: styles_default\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/kanban-definition-NDS4AKOZ.mjs\n"));

/***/ })

}]);