"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@braintree";
exports.ids = ["vendor-chunks/@braintree"];
exports.modules = {

/***/ "(ssr)/./node_modules/@braintree/sanitize-url/dist/constants.js":
/*!****************************************************************!*\
  !*** ./node_modules/@braintree/sanitize-url/dist/constants.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BLANK_URL = exports.relativeFirstCharacters = exports.whitespaceEscapeCharsRegex = exports.urlSchemeRegex = exports.ctrlCharactersRegex = exports.htmlCtrlEntityRegex = exports.htmlEntitiesRegex = exports.invalidProtocolRegex = void 0;\nexports.invalidProtocolRegex = /^([^\\w]*)(javascript|data|vbscript)/im;\nexports.htmlEntitiesRegex = /&#(\\w+)(^\\w|;)?/g;\nexports.htmlCtrlEntityRegex = /&(newline|tab);/gi;\nexports.ctrlCharactersRegex = /[\\u0000-\\u001F\\u007F-\\u009F\\u2000-\\u200D\\uFEFF]/gim;\nexports.urlSchemeRegex = /^.+(:|&colon;)/gim;\nexports.whitespaceEscapeCharsRegex = /(\\\\|%5[cC])((%(6[eE]|72|74))|[nrt])/g;\nexports.relativeFirstCharacters = [\".\", \"/\"];\nexports.BLANK_URL = \"about:blank\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGJyYWludHJlZS9zYW5pdGl6ZS11cmwvZGlzdC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsaUJBQWlCLEdBQUcsK0JBQStCLEdBQUcsa0NBQWtDLEdBQUcsc0JBQXNCLEdBQUcsMkJBQTJCLEdBQUcsMkJBQTJCLEdBQUcseUJBQXlCLEdBQUcsNEJBQTRCO0FBQ3hPLDRCQUE0QjtBQUM1Qix5QkFBeUIsaUJBQWlCO0FBQzFDLDJCQUEyQixtQkFBbUI7QUFDOUMsMkJBQTJCO0FBQzNCLHNCQUFzQixpQkFBaUI7QUFDdkMsa0NBQWtDO0FBQ2xDLCtCQUErQjtBQUMvQixpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQGJyYWludHJlZVxcc2FuaXRpemUtdXJsXFxkaXN0XFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkJMQU5LX1VSTCA9IGV4cG9ydHMucmVsYXRpdmVGaXJzdENoYXJhY3RlcnMgPSBleHBvcnRzLndoaXRlc3BhY2VFc2NhcGVDaGFyc1JlZ2V4ID0gZXhwb3J0cy51cmxTY2hlbWVSZWdleCA9IGV4cG9ydHMuY3RybENoYXJhY3RlcnNSZWdleCA9IGV4cG9ydHMuaHRtbEN0cmxFbnRpdHlSZWdleCA9IGV4cG9ydHMuaHRtbEVudGl0aWVzUmVnZXggPSBleHBvcnRzLmludmFsaWRQcm90b2NvbFJlZ2V4ID0gdm9pZCAwO1xuZXhwb3J0cy5pbnZhbGlkUHJvdG9jb2xSZWdleCA9IC9eKFteXFx3XSopKGphdmFzY3JpcHR8ZGF0YXx2YnNjcmlwdCkvaW07XG5leHBvcnRzLmh0bWxFbnRpdGllc1JlZ2V4ID0gLyYjKFxcdyspKF5cXHd8Oyk/L2c7XG5leHBvcnRzLmh0bWxDdHJsRW50aXR5UmVnZXggPSAvJihuZXdsaW5lfHRhYik7L2dpO1xuZXhwb3J0cy5jdHJsQ2hhcmFjdGVyc1JlZ2V4ID0gL1tcXHUwMDAwLVxcdTAwMUZcXHUwMDdGLVxcdTAwOUZcXHUyMDAwLVxcdTIwMERcXHVGRUZGXS9naW07XG5leHBvcnRzLnVybFNjaGVtZVJlZ2V4ID0gL14uKyg6fCZjb2xvbjspL2dpbTtcbmV4cG9ydHMud2hpdGVzcGFjZUVzY2FwZUNoYXJzUmVnZXggPSAvKFxcXFx8JTVbY0NdKSgoJSg2W2VFXXw3Mnw3NCkpfFtucnRdKS9nO1xuZXhwb3J0cy5yZWxhdGl2ZUZpcnN0Q2hhcmFjdGVycyA9IFtcIi5cIiwgXCIvXCJdO1xuZXhwb3J0cy5CTEFOS19VUkwgPSBcImFib3V0OmJsYW5rXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@braintree/sanitize-url/dist/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@braintree/sanitize-url/dist/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@braintree/sanitize-url/dist/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.sanitizeUrl = void 0;\nvar constants_1 = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/@braintree/sanitize-url/dist/constants.js\");\nfunction isRelativeUrlWithoutProtocol(url) {\n    return constants_1.relativeFirstCharacters.indexOf(url[0]) > -1;\n}\nfunction decodeHtmlCharacters(str) {\n    var removedNullByte = str.replace(constants_1.ctrlCharactersRegex, \"\");\n    return removedNullByte.replace(constants_1.htmlEntitiesRegex, function (match, dec) {\n        return String.fromCharCode(dec);\n    });\n}\nfunction isValidUrl(url) {\n    return URL.canParse(url);\n}\nfunction decodeURI(uri) {\n    try {\n        return decodeURIComponent(uri);\n    }\n    catch (e) {\n        // Ignoring error\n        // It is possible that the URI contains a `%` not associated\n        // with URI/URL-encoding.\n        return uri;\n    }\n}\nfunction sanitizeUrl(url) {\n    if (!url) {\n        return constants_1.BLANK_URL;\n    }\n    var charsToDecode;\n    var decodedUrl = decodeURI(url.trim());\n    do {\n        decodedUrl = decodeHtmlCharacters(decodedUrl)\n            .replace(constants_1.htmlCtrlEntityRegex, \"\")\n            .replace(constants_1.ctrlCharactersRegex, \"\")\n            .replace(constants_1.whitespaceEscapeCharsRegex, \"\")\n            .trim();\n        decodedUrl = decodeURI(decodedUrl);\n        charsToDecode =\n            decodedUrl.match(constants_1.ctrlCharactersRegex) ||\n                decodedUrl.match(constants_1.htmlEntitiesRegex) ||\n                decodedUrl.match(constants_1.htmlCtrlEntityRegex) ||\n                decodedUrl.match(constants_1.whitespaceEscapeCharsRegex);\n    } while (charsToDecode && charsToDecode.length > 0);\n    var sanitizedUrl = decodedUrl;\n    if (!sanitizedUrl) {\n        return constants_1.BLANK_URL;\n    }\n    if (isRelativeUrlWithoutProtocol(sanitizedUrl)) {\n        return sanitizedUrl;\n    }\n    // Remove any leading whitespace before checking the URL scheme\n    var trimmedUrl = sanitizedUrl.trimStart();\n    var urlSchemeParseResults = trimmedUrl.match(constants_1.urlSchemeRegex);\n    if (!urlSchemeParseResults) {\n        return sanitizedUrl;\n    }\n    var urlScheme = urlSchemeParseResults[0].toLowerCase().trim();\n    if (constants_1.invalidProtocolRegex.test(urlScheme)) {\n        return constants_1.BLANK_URL;\n    }\n    var backSanitized = trimmedUrl.replace(/\\\\/g, \"/\");\n    // Handle special cases for mailto: and custom deep-link protocols\n    if (urlScheme === \"mailto:\" || urlScheme.includes(\"://\")) {\n        return backSanitized;\n    }\n    // For http and https URLs, perform additional validation\n    if (urlScheme === \"http:\" || urlScheme === \"https:\") {\n        if (!isValidUrl(backSanitized)) {\n            return constants_1.BLANK_URL;\n        }\n        var url_1 = new URL(backSanitized);\n        url_1.protocol = url_1.protocol.toLowerCase();\n        url_1.hostname = url_1.hostname.toLowerCase();\n        return url_1.toString();\n    }\n    return backSanitized;\n}\nexports.sanitizeUrl = sanitizeUrl;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@braintree/sanitize-url/dist/index.js\n");

/***/ })

};
;