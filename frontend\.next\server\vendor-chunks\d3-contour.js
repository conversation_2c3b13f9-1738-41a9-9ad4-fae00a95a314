"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-contour";
exports.ids = ["vendor-chunks/d3-contour"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-contour/src/area.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-contour/src/area.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(ring) {\n  var i = 0, n = ring.length, area = ring[n - 1][1] * ring[0][0] - ring[n - 1][0] * ring[0][1];\n  while (++i < n) area += ring[i - 1][1] * ring[i][0] - ring[i - 1][0] * ring[i][1];\n  return area;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvYXJlYS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVM7QUFDeEI7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtY29udG91clxcc3JjXFxhcmVhLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHJpbmcpIHtcbiAgdmFyIGkgPSAwLCBuID0gcmluZy5sZW5ndGgsIGFyZWEgPSByaW5nW24gLSAxXVsxXSAqIHJpbmdbMF1bMF0gLSByaW5nW24gLSAxXVswXSAqIHJpbmdbMF1bMV07XG4gIHdoaWxlICgrK2kgPCBuKSBhcmVhICs9IHJpbmdbaSAtIDFdWzFdICogcmluZ1tpXVswXSAtIHJpbmdbaSAtIDFdWzBdICogcmluZ1tpXVsxXTtcbiAgcmV0dXJuIGFyZWE7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/area.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/array.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-contour/src/array.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   slice: () => (/* binding */ slice)\n/* harmony export */ });\nvar array = Array.prototype;\n\nvar slice = array.slice;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvYXJyYXkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUVPIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGQzLWNvbnRvdXJcXHNyY1xcYXJyYXkuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGFycmF5ID0gQXJyYXkucHJvdG90eXBlO1xuXG5leHBvcnQgdmFyIHNsaWNlID0gYXJyYXkuc2xpY2U7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/array.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/ascending.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-contour/src/ascending.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n  return a - b;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvYXNjZW5kaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxvQ0FBUztBQUN4QjtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGQzLWNvbnRvdXJcXHNyY1xcYXNjZW5kaW5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKGEsIGIpIHtcbiAgcmV0dXJuIGEgLSBiO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/ascending.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/constant.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-contour/src/constant.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (x => () => x);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvY29uc3RhbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLFlBQVksRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1jb250b3VyXFxzcmNcXGNvbnN0YW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHggPT4gKCkgPT4geDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/constant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/contains.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-contour/src/contains.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(ring, hole) {\n  var i = -1, n = hole.length, c;\n  while (++i < n) if (c = ringContains(ring, hole[i])) return c;\n  return 0;\n}\n\nfunction ringContains(ring, point) {\n  var x = point[0], y = point[1], contains = -1;\n  for (var i = 0, n = ring.length, j = n - 1; i < n; j = i++) {\n    var pi = ring[i], xi = pi[0], yi = pi[1], pj = ring[j], xj = pj[0], yj = pj[1];\n    if (segmentContains(pi, pj, point)) return 0;\n    if (((yi > y) !== (yj > y)) && ((x < (xj - xi) * (y - yi) / (yj - yi) + xi))) contains = -contains;\n  }\n  return contains;\n}\n\nfunction segmentContains(a, b, c) {\n  var i; return collinear(a, b, c) && within(a[i = +(a[0] === b[0])], c[i], b[i]);\n}\n\nfunction collinear(a, b, c) {\n  return (b[0] - a[0]) * (c[1] - a[1]) === (c[0] - a[0]) * (b[1] - a[1]);\n}\n\nfunction within(p, q, r) {\n  return p <= q && q <= r || r <= q && q <= p;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvY29udGFpbnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSw4Q0FBOEMsT0FBTztBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxTQUFTO0FBQ1Q7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1jb250b3VyXFxzcmNcXGNvbnRhaW5zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHJpbmcsIGhvbGUpIHtcbiAgdmFyIGkgPSAtMSwgbiA9IGhvbGUubGVuZ3RoLCBjO1xuICB3aGlsZSAoKytpIDwgbikgaWYgKGMgPSByaW5nQ29udGFpbnMocmluZywgaG9sZVtpXSkpIHJldHVybiBjO1xuICByZXR1cm4gMDtcbn1cblxuZnVuY3Rpb24gcmluZ0NvbnRhaW5zKHJpbmcsIHBvaW50KSB7XG4gIHZhciB4ID0gcG9pbnRbMF0sIHkgPSBwb2ludFsxXSwgY29udGFpbnMgPSAtMTtcbiAgZm9yICh2YXIgaSA9IDAsIG4gPSByaW5nLmxlbmd0aCwgaiA9IG4gLSAxOyBpIDwgbjsgaiA9IGkrKykge1xuICAgIHZhciBwaSA9IHJpbmdbaV0sIHhpID0gcGlbMF0sIHlpID0gcGlbMV0sIHBqID0gcmluZ1tqXSwgeGogPSBwalswXSwgeWogPSBwalsxXTtcbiAgICBpZiAoc2VnbWVudENvbnRhaW5zKHBpLCBwaiwgcG9pbnQpKSByZXR1cm4gMDtcbiAgICBpZiAoKCh5aSA+IHkpICE9PSAoeWogPiB5KSkgJiYgKCh4IDwgKHhqIC0geGkpICogKHkgLSB5aSkgLyAoeWogLSB5aSkgKyB4aSkpKSBjb250YWlucyA9IC1jb250YWlucztcbiAgfVxuICByZXR1cm4gY29udGFpbnM7XG59XG5cbmZ1bmN0aW9uIHNlZ21lbnRDb250YWlucyhhLCBiLCBjKSB7XG4gIHZhciBpOyByZXR1cm4gY29sbGluZWFyKGEsIGIsIGMpICYmIHdpdGhpbihhW2kgPSArKGFbMF0gPT09IGJbMF0pXSwgY1tpXSwgYltpXSk7XG59XG5cbmZ1bmN0aW9uIGNvbGxpbmVhcihhLCBiLCBjKSB7XG4gIHJldHVybiAoYlswXSAtIGFbMF0pICogKGNbMV0gLSBhWzFdKSA9PT0gKGNbMF0gLSBhWzBdKSAqIChiWzFdIC0gYVsxXSk7XG59XG5cbmZ1bmN0aW9uIHdpdGhpbihwLCBxLCByKSB7XG4gIHJldHVybiBwIDw9IHEgJiYgcSA8PSByIHx8IHIgPD0gcSAmJiBxIDw9IHA7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/contains.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/contours.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-contour/src/contours.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/threshold/sturges.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/extent.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/nice.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/d3-contour/src/array.js\");\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-contour/src/ascending.js\");\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./area.js */ \"(ssr)/./node_modules/d3-contour/src/area.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/./node_modules/d3-contour/src/constant.js\");\n/* harmony import */ var _contains_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./contains.js */ \"(ssr)/./node_modules/d3-contour/src/contains.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./noop.js */ \"(ssr)/./node_modules/d3-contour/src/noop.js\");\n\n\n\n\n\n\n\n\nvar cases = [\n  [],\n  [[[1.0, 1.5], [0.5, 1.0]]],\n  [[[1.5, 1.0], [1.0, 1.5]]],\n  [[[1.5, 1.0], [0.5, 1.0]]],\n  [[[1.0, 0.5], [1.5, 1.0]]],\n  [[[1.0, 1.5], [0.5, 1.0]], [[1.0, 0.5], [1.5, 1.0]]],\n  [[[1.0, 0.5], [1.0, 1.5]]],\n  [[[1.0, 0.5], [0.5, 1.0]]],\n  [[[0.5, 1.0], [1.0, 0.5]]],\n  [[[1.0, 1.5], [1.0, 0.5]]],\n  [[[0.5, 1.0], [1.0, 0.5]], [[1.5, 1.0], [1.0, 1.5]]],\n  [[[1.5, 1.0], [1.0, 0.5]]],\n  [[[0.5, 1.0], [1.5, 1.0]]],\n  [[[1.0, 1.5], [1.5, 1.0]]],\n  [[[0.5, 1.0], [1.0, 1.5]]],\n  []\n];\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  var dx = 1,\n      dy = 1,\n      threshold = d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n      smooth = smoothLinear;\n\n  function contours(values) {\n    var tz = threshold(values);\n\n    // Convert number of thresholds into uniform thresholds.\n    if (!Array.isArray(tz)) {\n      const e = (0,d3_array__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values, finite);\n      tz = (0,d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(...(0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(e[0], e[1], tz), tz);\n      while (tz[tz.length - 1] >= e[1]) tz.pop();\n      while (tz[1] < e[0]) tz.shift();\n    } else {\n      tz = tz.slice().sort(_ascending_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]);\n    }\n\n    return tz.map(value => contour(values, value));\n  }\n\n  // Accumulate, smooth contour rings, assign holes to exterior rings.\n  // Based on https://github.com/mbostock/shapefile/blob/v0.6.2/shp/polygon.js\n  function contour(values, value) {\n    const v = value == null ? NaN : +value;\n    if (isNaN(v)) throw new Error(`invalid value: ${value}`);\n\n    var polygons = [],\n        holes = [];\n\n    isorings(values, v, function(ring) {\n      smooth(ring, values, v);\n      if ((0,_area_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(ring) > 0) polygons.push([ring]);\n      else holes.push(ring);\n    });\n\n    holes.forEach(function(hole) {\n      for (var i = 0, n = polygons.length, polygon; i < n; ++i) {\n        if ((0,_contains_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((polygon = polygons[i])[0], hole) !== -1) {\n          polygon.push(hole);\n          return;\n        }\n      }\n    });\n\n    return {\n      type: \"MultiPolygon\",\n      value: value,\n      coordinates: polygons\n    };\n  }\n\n  // Marching squares with isolines stitched into rings.\n  // Based on https://github.com/topojson/topojson-client/blob/v3.0.0/src/stitch.js\n  function isorings(values, value, callback) {\n    var fragmentByStart = new Array,\n        fragmentByEnd = new Array,\n        x, y, t0, t1, t2, t3;\n\n    // Special case for the first row (y = -1, t2 = t3 = 0).\n    x = y = -1;\n    t1 = above(values[0], value);\n    cases[t1 << 1].forEach(stitch);\n    while (++x < dx - 1) {\n      t0 = t1, t1 = above(values[x + 1], value);\n      cases[t0 | t1 << 1].forEach(stitch);\n    }\n    cases[t1 << 0].forEach(stitch);\n\n    // General case for the intermediate rows.\n    while (++y < dy - 1) {\n      x = -1;\n      t1 = above(values[y * dx + dx], value);\n      t2 = above(values[y * dx], value);\n      cases[t1 << 1 | t2 << 2].forEach(stitch);\n      while (++x < dx - 1) {\n        t0 = t1, t1 = above(values[y * dx + dx + x + 1], value);\n        t3 = t2, t2 = above(values[y * dx + x + 1], value);\n        cases[t0 | t1 << 1 | t2 << 2 | t3 << 3].forEach(stitch);\n      }\n      cases[t1 | t2 << 3].forEach(stitch);\n    }\n\n    // Special case for the last row (y = dy - 1, t0 = t1 = 0).\n    x = -1;\n    t2 = values[y * dx] >= value;\n    cases[t2 << 2].forEach(stitch);\n    while (++x < dx - 1) {\n      t3 = t2, t2 = above(values[y * dx + x + 1], value);\n      cases[t2 << 2 | t3 << 3].forEach(stitch);\n    }\n    cases[t2 << 3].forEach(stitch);\n\n    function stitch(line) {\n      var start = [line[0][0] + x, line[0][1] + y],\n          end = [line[1][0] + x, line[1][1] + y],\n          startIndex = index(start),\n          endIndex = index(end),\n          f, g;\n      if (f = fragmentByEnd[startIndex]) {\n        if (g = fragmentByStart[endIndex]) {\n          delete fragmentByEnd[f.end];\n          delete fragmentByStart[g.start];\n          if (f === g) {\n            f.ring.push(end);\n            callback(f.ring);\n          } else {\n            fragmentByStart[f.start] = fragmentByEnd[g.end] = {start: f.start, end: g.end, ring: f.ring.concat(g.ring)};\n          }\n        } else {\n          delete fragmentByEnd[f.end];\n          f.ring.push(end);\n          fragmentByEnd[f.end = endIndex] = f;\n        }\n      } else if (f = fragmentByStart[endIndex]) {\n        if (g = fragmentByEnd[startIndex]) {\n          delete fragmentByStart[f.start];\n          delete fragmentByEnd[g.end];\n          if (f === g) {\n            f.ring.push(end);\n            callback(f.ring);\n          } else {\n            fragmentByStart[g.start] = fragmentByEnd[f.end] = {start: g.start, end: f.end, ring: g.ring.concat(f.ring)};\n          }\n        } else {\n          delete fragmentByStart[f.start];\n          f.ring.unshift(start);\n          fragmentByStart[f.start = startIndex] = f;\n        }\n      } else {\n        fragmentByStart[startIndex] = fragmentByEnd[endIndex] = {start: startIndex, end: endIndex, ring: [start, end]};\n      }\n    }\n  }\n\n  function index(point) {\n    return point[0] * 2 + point[1] * (dx + 1) * 4;\n  }\n\n  function smoothLinear(ring, values, value) {\n    ring.forEach(function(point) {\n      var x = point[0],\n          y = point[1],\n          xt = x | 0,\n          yt = y | 0,\n          v1 = valid(values[yt * dx + xt]);\n      if (x > 0 && x < dx && xt === x) {\n        point[0] = smooth1(x, valid(values[yt * dx + xt - 1]), v1, value);\n      }\n      if (y > 0 && y < dy && yt === y) {\n        point[1] = smooth1(y, valid(values[(yt - 1) * dx + xt]), v1, value);\n      }\n    });\n  }\n\n  contours.contour = contour;\n\n  contours.size = function(_) {\n    if (!arguments.length) return [dx, dy];\n    var _0 = Math.floor(_[0]), _1 = Math.floor(_[1]);\n    if (!(_0 >= 0 && _1 >= 0)) throw new Error(\"invalid size\");\n    return dx = _0, dy = _1, contours;\n  };\n\n  contours.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? (0,_constant_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(_array_js__WEBPACK_IMPORTED_MODULE_8__.slice.call(_)) : (0,_constant_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(_), contours) : threshold;\n  };\n\n  contours.smooth = function(_) {\n    return arguments.length ? (smooth = _ ? smoothLinear : _noop_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"], contours) : smooth === smoothLinear;\n  };\n\n  return contours;\n}\n\n// When computing the extent, ignore infinite values (as well as invalid ones).\nfunction finite(x) {\n  return isFinite(x) ? x : NaN;\n}\n\n// Is the (possibly invalid) x greater than or equal to the (known valid) value?\n// Treat any invalid value as below negative infinity.\nfunction above(x, value) {\n  return x == null ? false : +x >= value;\n}\n\n// During smoothing, treat any invalid value as negative infinity.\nfunction valid(v) {\n  return v == null || isNaN(v = +v) ? -Infinity : v;\n}\n\nfunction smooth1(x, v0, v1, value) {\n  const a = value - v0;\n  const b = v1 - v0;\n  const d = isFinite(a) || isFinite(b) ? a / b : Math.sign(a) / Math.sign(b);\n  return isNaN(d) ? x : x + d - 0.5;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/contours.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/density.js":
/*!************************************************!*\
  !*** ./node_modules/d3-contour/src/density.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/blur.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/max.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/d3-contour/src/array.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/./node_modules/d3-contour/src/constant.js\");\n/* harmony import */ var _contours_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./contours.js */ \"(ssr)/./node_modules/d3-contour/src/contours.js\");\n\n\n\n\n\nfunction defaultX(d) {\n  return d[0];\n}\n\nfunction defaultY(d) {\n  return d[1];\n}\n\nfunction defaultWeight() {\n  return 1;\n}\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n  var x = defaultX,\n      y = defaultY,\n      weight = defaultWeight,\n      dx = 960,\n      dy = 500,\n      r = 20, // blur radius\n      k = 2, // log2(grid cell size)\n      o = r * 3, // grid offset, to pad for blur\n      n = (dx + o * 2) >> k, // grid width\n      m = (dy + o * 2) >> k, // grid height\n      threshold = (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(20);\n\n  function grid(data) {\n    var values = new Float32Array(n * m),\n        pow2k = Math.pow(2, -k),\n        i = -1;\n\n    for (const d of data) {\n      var xi = (x(d, ++i, data) + o) * pow2k,\n          yi = (y(d, i, data) + o) * pow2k,\n          wi = +weight(d, i, data);\n      if (wi && xi >= 0 && xi < n && yi >= 0 && yi < m) {\n        var x0 = Math.floor(xi),\n            y0 = Math.floor(yi),\n            xt = xi - x0 - 0.5,\n            yt = yi - y0 - 0.5;\n        values[x0 + y0 * n] += (1 - xt) * (1 - yt) * wi;\n        values[x0 + 1 + y0 * n] += xt * (1 - yt) * wi;\n        values[x0 + 1 + (y0 + 1) * n] += xt * yt * wi;\n        values[x0 + (y0 + 1) * n] += (1 - xt) * yt * wi;\n      }\n    }\n\n    (0,d3_array__WEBPACK_IMPORTED_MODULE_1__.blur2)({data: values, width: n, height: m}, r * pow2k);\n    return values;\n  }\n\n  function density(data) {\n    var values = grid(data),\n        tz = threshold(values),\n        pow4k = Math.pow(2, 2 * k);\n\n    // Convert number of thresholds into uniform thresholds.\n    if (!Array.isArray(tz)) {\n      tz = (0,d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(Number.MIN_VALUE, (0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(values) / pow4k, tz);\n    }\n\n    return (0,_contours_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])()\n        .size([n, m])\n        .thresholds(tz.map(d => d * pow4k))\n      (values)\n        .map((c, i) => (c.value = +tz[i], transform(c)));\n  }\n\n  density.contours = function(data) {\n    var values = grid(data),\n        contours = (0,_contours_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])().size([n, m]),\n        pow4k = Math.pow(2, 2 * k),\n        contour = value => {\n          value = +value;\n          var c = transform(contours.contour(values, value * pow4k));\n          c.value = value; // preserve exact threshold value\n          return c;\n        };\n    Object.defineProperty(contour, \"max\", {get: () => (0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(values) / pow4k});\n    return contour;\n  };\n\n  function transform(geometry) {\n    geometry.coordinates.forEach(transformPolygon);\n    return geometry;\n  }\n\n  function transformPolygon(coordinates) {\n    coordinates.forEach(transformRing);\n  }\n\n  function transformRing(coordinates) {\n    coordinates.forEach(transformPoint);\n  }\n\n  // TODO Optimize.\n  function transformPoint(coordinates) {\n    coordinates[0] = coordinates[0] * Math.pow(2, k) - o;\n    coordinates[1] = coordinates[1] * Math.pow(2, k) - o;\n  }\n\n  function resize() {\n    o = r * 3;\n    n = (dx + o * 2) >> k;\n    m = (dy + o * 2) >> k;\n    return density;\n  }\n\n  density.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(+_), density) : x;\n  };\n\n  density.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(+_), density) : y;\n  };\n\n  density.weight = function(_) {\n    return arguments.length ? (weight = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(+_), density) : weight;\n  };\n\n  density.size = function(_) {\n    if (!arguments.length) return [dx, dy];\n    var _0 = +_[0], _1 = +_[1];\n    if (!(_0 >= 0 && _1 >= 0)) throw new Error(\"invalid size\");\n    return dx = _0, dy = _1, resize();\n  };\n\n  density.cellSize = function(_) {\n    if (!arguments.length) return 1 << k;\n    if (!((_ = +_) >= 1)) throw new Error(\"invalid cell size\");\n    return k = Math.floor(Math.log(_) / Math.LN2), resize();\n  };\n\n  density.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_array_js__WEBPACK_IMPORTED_MODULE_5__.slice.call(_)) : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_), density) : threshold;\n  };\n\n  density.bandwidth = function(_) {\n    if (!arguments.length) return Math.sqrt(r * (r + 1));\n    if (!((_ = +_) >= 0)) throw new Error(\"invalid bandwidth\");\n    return r = (Math.sqrt(4 * _ * _ + 1) - 1) / 2, resize();\n  };\n\n  return density;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvZGVuc2l0eS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTJDO0FBQ1Y7QUFDSTtBQUNBOztBQUVyQztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSw2QkFBZSxzQ0FBVztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix3REFBUTs7QUFFMUI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLElBQUksK0NBQUssRUFBRSxrQ0FBa0M7QUFDN0M7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsV0FBVyxvREFBSyxtQkFBbUIsb0RBQUc7QUFDdEM7O0FBRUEsV0FBVyx3REFBUTtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxtQkFBbUIsd0RBQVE7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBLDJDQUEyQyxXQUFXLG9EQUFHLGlCQUFpQjtBQUMxRTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxpRUFBaUUsd0RBQVE7QUFDekU7O0FBRUE7QUFDQSxpRUFBaUUsd0RBQVE7QUFDekU7O0FBRUE7QUFDQSxzRUFBc0Usd0RBQVE7QUFDOUU7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSw0RkFBNEYsd0RBQVEsQ0FBQyw0Q0FBSyxZQUFZLHdEQUFRO0FBQzlIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1jb250b3VyXFxzcmNcXGRlbnNpdHkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtibHVyMiwgbWF4LCB0aWNrc30gZnJvbSBcImQzLWFycmF5XCI7XG5pbXBvcnQge3NsaWNlfSBmcm9tIFwiLi9hcnJheS5qc1wiO1xuaW1wb3J0IGNvbnN0YW50IGZyb20gXCIuL2NvbnN0YW50LmpzXCI7XG5pbXBvcnQgQ29udG91cnMgZnJvbSBcIi4vY29udG91cnMuanNcIjtcblxuZnVuY3Rpb24gZGVmYXVsdFgoZCkge1xuICByZXR1cm4gZFswXTtcbn1cblxuZnVuY3Rpb24gZGVmYXVsdFkoZCkge1xuICByZXR1cm4gZFsxXTtcbn1cblxuZnVuY3Rpb24gZGVmYXVsdFdlaWdodCgpIHtcbiAgcmV0dXJuIDE7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge1xuICB2YXIgeCA9IGRlZmF1bHRYLFxuICAgICAgeSA9IGRlZmF1bHRZLFxuICAgICAgd2VpZ2h0ID0gZGVmYXVsdFdlaWdodCxcbiAgICAgIGR4ID0gOTYwLFxuICAgICAgZHkgPSA1MDAsXG4gICAgICByID0gMjAsIC8vIGJsdXIgcmFkaXVzXG4gICAgICBrID0gMiwgLy8gbG9nMihncmlkIGNlbGwgc2l6ZSlcbiAgICAgIG8gPSByICogMywgLy8gZ3JpZCBvZmZzZXQsIHRvIHBhZCBmb3IgYmx1clxuICAgICAgbiA9IChkeCArIG8gKiAyKSA+PiBrLCAvLyBncmlkIHdpZHRoXG4gICAgICBtID0gKGR5ICsgbyAqIDIpID4+IGssIC8vIGdyaWQgaGVpZ2h0XG4gICAgICB0aHJlc2hvbGQgPSBjb25zdGFudCgyMCk7XG5cbiAgZnVuY3Rpb24gZ3JpZChkYXRhKSB7XG4gICAgdmFyIHZhbHVlcyA9IG5ldyBGbG9hdDMyQXJyYXkobiAqIG0pLFxuICAgICAgICBwb3cyayA9IE1hdGgucG93KDIsIC1rKSxcbiAgICAgICAgaSA9IC0xO1xuXG4gICAgZm9yIChjb25zdCBkIG9mIGRhdGEpIHtcbiAgICAgIHZhciB4aSA9ICh4KGQsICsraSwgZGF0YSkgKyBvKSAqIHBvdzJrLFxuICAgICAgICAgIHlpID0gKHkoZCwgaSwgZGF0YSkgKyBvKSAqIHBvdzJrLFxuICAgICAgICAgIHdpID0gK3dlaWdodChkLCBpLCBkYXRhKTtcbiAgICAgIGlmICh3aSAmJiB4aSA+PSAwICYmIHhpIDwgbiAmJiB5aSA+PSAwICYmIHlpIDwgbSkge1xuICAgICAgICB2YXIgeDAgPSBNYXRoLmZsb29yKHhpKSxcbiAgICAgICAgICAgIHkwID0gTWF0aC5mbG9vcih5aSksXG4gICAgICAgICAgICB4dCA9IHhpIC0geDAgLSAwLjUsXG4gICAgICAgICAgICB5dCA9IHlpIC0geTAgLSAwLjU7XG4gICAgICAgIHZhbHVlc1t4MCArIHkwICogbl0gKz0gKDEgLSB4dCkgKiAoMSAtIHl0KSAqIHdpO1xuICAgICAgICB2YWx1ZXNbeDAgKyAxICsgeTAgKiBuXSArPSB4dCAqICgxIC0geXQpICogd2k7XG4gICAgICAgIHZhbHVlc1t4MCArIDEgKyAoeTAgKyAxKSAqIG5dICs9IHh0ICogeXQgKiB3aTtcbiAgICAgICAgdmFsdWVzW3gwICsgKHkwICsgMSkgKiBuXSArPSAoMSAtIHh0KSAqIHl0ICogd2k7XG4gICAgICB9XG4gICAgfVxuXG4gICAgYmx1cjIoe2RhdGE6IHZhbHVlcywgd2lkdGg6IG4sIGhlaWdodDogbX0sIHIgKiBwb3cyayk7XG4gICAgcmV0dXJuIHZhbHVlcztcbiAgfVxuXG4gIGZ1bmN0aW9uIGRlbnNpdHkoZGF0YSkge1xuICAgIHZhciB2YWx1ZXMgPSBncmlkKGRhdGEpLFxuICAgICAgICB0eiA9IHRocmVzaG9sZCh2YWx1ZXMpLFxuICAgICAgICBwb3c0ayA9IE1hdGgucG93KDIsIDIgKiBrKTtcblxuICAgIC8vIENvbnZlcnQgbnVtYmVyIG9mIHRocmVzaG9sZHMgaW50byB1bmlmb3JtIHRocmVzaG9sZHMuXG4gICAgaWYgKCFBcnJheS5pc0FycmF5KHR6KSkge1xuICAgICAgdHogPSB0aWNrcyhOdW1iZXIuTUlOX1ZBTFVFLCBtYXgodmFsdWVzKSAvIHBvdzRrLCB0eik7XG4gICAgfVxuXG4gICAgcmV0dXJuIENvbnRvdXJzKClcbiAgICAgICAgLnNpemUoW24sIG1dKVxuICAgICAgICAudGhyZXNob2xkcyh0ei5tYXAoZCA9PiBkICogcG93NGspKVxuICAgICAgKHZhbHVlcylcbiAgICAgICAgLm1hcCgoYywgaSkgPT4gKGMudmFsdWUgPSArdHpbaV0sIHRyYW5zZm9ybShjKSkpO1xuICB9XG5cbiAgZGVuc2l0eS5jb250b3VycyA9IGZ1bmN0aW9uKGRhdGEpIHtcbiAgICB2YXIgdmFsdWVzID0gZ3JpZChkYXRhKSxcbiAgICAgICAgY29udG91cnMgPSBDb250b3VycygpLnNpemUoW24sIG1dKSxcbiAgICAgICAgcG93NGsgPSBNYXRoLnBvdygyLCAyICogayksXG4gICAgICAgIGNvbnRvdXIgPSB2YWx1ZSA9PiB7XG4gICAgICAgICAgdmFsdWUgPSArdmFsdWU7XG4gICAgICAgICAgdmFyIGMgPSB0cmFuc2Zvcm0oY29udG91cnMuY29udG91cih2YWx1ZXMsIHZhbHVlICogcG93NGspKTtcbiAgICAgICAgICBjLnZhbHVlID0gdmFsdWU7IC8vIHByZXNlcnZlIGV4YWN0IHRocmVzaG9sZCB2YWx1ZVxuICAgICAgICAgIHJldHVybiBjO1xuICAgICAgICB9O1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShjb250b3VyLCBcIm1heFwiLCB7Z2V0OiAoKSA9PiBtYXgodmFsdWVzKSAvIHBvdzRrfSk7XG4gICAgcmV0dXJuIGNvbnRvdXI7XG4gIH07XG5cbiAgZnVuY3Rpb24gdHJhbnNmb3JtKGdlb21ldHJ5KSB7XG4gICAgZ2VvbWV0cnkuY29vcmRpbmF0ZXMuZm9yRWFjaCh0cmFuc2Zvcm1Qb2x5Z29uKTtcbiAgICByZXR1cm4gZ2VvbWV0cnk7XG4gIH1cblxuICBmdW5jdGlvbiB0cmFuc2Zvcm1Qb2x5Z29uKGNvb3JkaW5hdGVzKSB7XG4gICAgY29vcmRpbmF0ZXMuZm9yRWFjaCh0cmFuc2Zvcm1SaW5nKTtcbiAgfVxuXG4gIGZ1bmN0aW9uIHRyYW5zZm9ybVJpbmcoY29vcmRpbmF0ZXMpIHtcbiAgICBjb29yZGluYXRlcy5mb3JFYWNoKHRyYW5zZm9ybVBvaW50KTtcbiAgfVxuXG4gIC8vIFRPRE8gT3B0aW1pemUuXG4gIGZ1bmN0aW9uIHRyYW5zZm9ybVBvaW50KGNvb3JkaW5hdGVzKSB7XG4gICAgY29vcmRpbmF0ZXNbMF0gPSBjb29yZGluYXRlc1swXSAqIE1hdGgucG93KDIsIGspIC0gbztcbiAgICBjb29yZGluYXRlc1sxXSA9IGNvb3JkaW5hdGVzWzFdICogTWF0aC5wb3coMiwgaykgLSBvO1xuICB9XG5cbiAgZnVuY3Rpb24gcmVzaXplKCkge1xuICAgIG8gPSByICogMztcbiAgICBuID0gKGR4ICsgbyAqIDIpID4+IGs7XG4gICAgbSA9IChkeSArIG8gKiAyKSA+PiBrO1xuICAgIHJldHVybiBkZW5zaXR5O1xuICB9XG5cbiAgZGVuc2l0eS54ID0gZnVuY3Rpb24oXykge1xuICAgIHJldHVybiBhcmd1bWVudHMubGVuZ3RoID8gKHggPSB0eXBlb2YgXyA9PT0gXCJmdW5jdGlvblwiID8gXyA6IGNvbnN0YW50KCtfKSwgZGVuc2l0eSkgOiB4O1xuICB9O1xuXG4gIGRlbnNpdHkueSA9IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/ICh5ID0gdHlwZW9mIF8gPT09IFwiZnVuY3Rpb25cIiA/IF8gOiBjb25zdGFudCgrXyksIGRlbnNpdHkpIDogeTtcbiAgfTtcblxuICBkZW5zaXR5LndlaWdodCA9IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/ICh3ZWlnaHQgPSB0eXBlb2YgXyA9PT0gXCJmdW5jdGlvblwiID8gXyA6IGNvbnN0YW50KCtfKSwgZGVuc2l0eSkgOiB3ZWlnaHQ7XG4gIH07XG5cbiAgZGVuc2l0eS5zaXplID0gZnVuY3Rpb24oXykge1xuICAgIGlmICghYXJndW1lbnRzLmxlbmd0aCkgcmV0dXJuIFtkeCwgZHldO1xuICAgIHZhciBfMCA9ICtfWzBdLCBfMSA9ICtfWzFdO1xuICAgIGlmICghKF8wID49IDAgJiYgXzEgPj0gMCkpIHRocm93IG5ldyBFcnJvcihcImludmFsaWQgc2l6ZVwiKTtcbiAgICByZXR1cm4gZHggPSBfMCwgZHkgPSBfMSwgcmVzaXplKCk7XG4gIH07XG5cbiAgZGVuc2l0eS5jZWxsU2l6ZSA9IGZ1bmN0aW9uKF8pIHtcbiAgICBpZiAoIWFyZ3VtZW50cy5sZW5ndGgpIHJldHVybiAxIDw8IGs7XG4gICAgaWYgKCEoKF8gPSArXykgPj0gMSkpIHRocm93IG5ldyBFcnJvcihcImludmFsaWQgY2VsbCBzaXplXCIpO1xuICAgIHJldHVybiBrID0gTWF0aC5mbG9vcihNYXRoLmxvZyhfKSAvIE1hdGguTE4yKSwgcmVzaXplKCk7XG4gIH07XG5cbiAgZGVuc2l0eS50aHJlc2hvbGRzID0gZnVuY3Rpb24oXykge1xuICAgIHJldHVybiBhcmd1bWVudHMubGVuZ3RoID8gKHRocmVzaG9sZCA9IHR5cGVvZiBfID09PSBcImZ1bmN0aW9uXCIgPyBfIDogQXJyYXkuaXNBcnJheShfKSA/IGNvbnN0YW50KHNsaWNlLmNhbGwoXykpIDogY29uc3RhbnQoXyksIGRlbnNpdHkpIDogdGhyZXNob2xkO1xuICB9O1xuXG4gIGRlbnNpdHkuYmFuZHdpZHRoID0gZnVuY3Rpb24oXykge1xuICAgIGlmICghYXJndW1lbnRzLmxlbmd0aCkgcmV0dXJuIE1hdGguc3FydChyICogKHIgKyAxKSk7XG4gICAgaWYgKCEoKF8gPSArXykgPj0gMCkpIHRocm93IG5ldyBFcnJvcihcImludmFsaWQgYmFuZHdpZHRoXCIpO1xuICAgIHJldHVybiByID0gKE1hdGguc3FydCg0ICogXyAqIF8gKyAxKSAtIDEpIC8gMiwgcmVzaXplKCk7XG4gIH07XG5cbiAgcmV0dXJuIGRlbnNpdHk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/density.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/index.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-contour/src/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contourDensity: () => (/* reexport safe */ _density_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   contours: () => (/* reexport safe */ _contours_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _contours_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./contours.js */ \"(ssr)/./node_modules/d3-contour/src/contours.js\");\n/* harmony import */ var _density_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./density.js */ \"(ssr)/./node_modules/d3-contour/src/density.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFrRDtBQUNLIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGQzLWNvbnRvdXJcXHNyY1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHtkZWZhdWx0IGFzIGNvbnRvdXJzfSBmcm9tIFwiLi9jb250b3Vycy5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIGNvbnRvdXJEZW5zaXR5fSBmcm9tIFwiLi9kZW5zaXR5LmpzXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-contour/src/noop.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-contour/src/noop.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtY29udG91ci9zcmMvbm9vcC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsc0NBQVciLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtY29udG91clxcc3JjXFxub29wLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge31cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-contour/src/noop.js\n");

/***/ })

};
;