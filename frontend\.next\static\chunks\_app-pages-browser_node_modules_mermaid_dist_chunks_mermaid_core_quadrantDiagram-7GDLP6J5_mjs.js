"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_chunks_mermaid_core_quadrantDiagram-7GDLP6J5_mjs"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/quadrantDiagram-7GDLP6J5.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/mermaid/dist/chunks/mermaid.core/quadrantDiagram-7GDLP6J5.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: () => (/* binding */ diagram)\n/* harmony export */ });\n/* harmony import */ var _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-YTJNT7DU.mjs */ \"(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/chunk-YTJNT7DU.mjs\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n\n\n// src/diagrams/quadrant-chart/parser/quadrant.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 3], $V1 = [1, 4], $V2 = [1, 5], $V3 = [1, 6], $V4 = [1, 7], $V5 = [1, 4, 5, 10, 12, 13, 14, 18, 25, 35, 37, 39, 41, 42, 48, 50, 51, 52, 53, 54, 55, 56, 57, 60, 61, 63, 64, 65, 66, 67], $V6 = [1, 4, 5, 10, 12, 13, 14, 18, 25, 28, 35, 37, 39, 41, 42, 48, 50, 51, 52, 53, 54, 55, 56, 57, 60, 61, 63, 64, 65, 66, 67], $V7 = [55, 56, 57], $V8 = [2, 36], $V9 = [1, 37], $Va = [1, 36], $Vb = [1, 38], $Vc = [1, 35], $Vd = [1, 43], $Ve = [1, 41], $Vf = [1, 14], $Vg = [1, 23], $Vh = [1, 18], $Vi = [1, 19], $Vj = [1, 20], $Vk = [1, 21], $Vl = [1, 22], $Vm = [1, 24], $Vn = [1, 25], $Vo = [1, 26], $Vp = [1, 27], $Vq = [1, 28], $Vr = [1, 29], $Vs = [1, 32], $Vt = [1, 33], $Vu = [1, 34], $Vv = [1, 39], $Vw = [1, 40], $Vx = [1, 42], $Vy = [1, 44], $Vz = [1, 62], $VA = [1, 61], $VB = [4, 5, 8, 10, 12, 13, 14, 18, 44, 47, 49, 55, 56, 57, 63, 64, 65, 66, 67], $VC = [1, 65], $VD = [1, 66], $VE = [1, 67], $VF = [1, 68], $VG = [1, 69], $VH = [1, 70], $VI = [1, 71], $VJ = [1, 72], $VK = [1, 73], $VL = [1, 74], $VM = [1, 75], $VN = [1, 76], $VO = [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18], $VP = [1, 90], $VQ = [1, 91], $VR = [1, 92], $VS = [1, 99], $VT = [1, 93], $VU = [1, 96], $VV = [1, 94], $VW = [1, 95], $VX = [1, 97], $VY = [1, 98], $VZ = [1, 102], $V_ = [10, 55, 56, 57], $V$ = [4, 5, 6, 8, 10, 11, 13, 17, 18, 19, 20, 55, 56, 57];\n  var parser2 = {\n    trace: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"idStringToken\": 3, \"ALPHA\": 4, \"NUM\": 5, \"NODE_STRING\": 6, \"DOWN\": 7, \"MINUS\": 8, \"DEFAULT\": 9, \"COMMA\": 10, \"COLON\": 11, \"AMP\": 12, \"BRKT\": 13, \"MULT\": 14, \"UNICODE_TEXT\": 15, \"styleComponent\": 16, \"UNIT\": 17, \"SPACE\": 18, \"STYLE\": 19, \"PCT\": 20, \"idString\": 21, \"style\": 22, \"stylesOpt\": 23, \"classDefStatement\": 24, \"CLASSDEF\": 25, \"start\": 26, \"eol\": 27, \"QUADRANT\": 28, \"document\": 29, \"line\": 30, \"statement\": 31, \"axisDetails\": 32, \"quadrantDetails\": 33, \"points\": 34, \"title\": 35, \"title_value\": 36, \"acc_title\": 37, \"acc_title_value\": 38, \"acc_descr\": 39, \"acc_descr_value\": 40, \"acc_descr_multiline_value\": 41, \"section\": 42, \"text\": 43, \"point_start\": 44, \"point_x\": 45, \"point_y\": 46, \"class_name\": 47, \"X-AXIS\": 48, \"AXIS-TEXT-DELIMITER\": 49, \"Y-AXIS\": 50, \"QUADRANT_1\": 51, \"QUADRANT_2\": 52, \"QUADRANT_3\": 53, \"QUADRANT_4\": 54, \"NEWLINE\": 55, \"SEMI\": 56, \"EOF\": 57, \"alphaNumToken\": 58, \"textNoTagsToken\": 59, \"STR\": 60, \"MD_STR\": 61, \"alphaNum\": 62, \"PUNCTUATION\": 63, \"PLUS\": 64, \"EQUALS\": 65, \"DOT\": 66, \"UNDERSCORE\": 67, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"ALPHA\", 5: \"NUM\", 6: \"NODE_STRING\", 7: \"DOWN\", 8: \"MINUS\", 9: \"DEFAULT\", 10: \"COMMA\", 11: \"COLON\", 12: \"AMP\", 13: \"BRKT\", 14: \"MULT\", 15: \"UNICODE_TEXT\", 17: \"UNIT\", 18: \"SPACE\", 19: \"STYLE\", 20: \"PCT\", 25: \"CLASSDEF\", 28: \"QUADRANT\", 35: \"title\", 36: \"title_value\", 37: \"acc_title\", 38: \"acc_title_value\", 39: \"acc_descr\", 40: \"acc_descr_value\", 41: \"acc_descr_multiline_value\", 42: \"section\", 44: \"point_start\", 45: \"point_x\", 46: \"point_y\", 47: \"class_name\", 48: \"X-AXIS\", 49: \"AXIS-TEXT-DELIMITER\", 50: \"Y-AXIS\", 51: \"QUADRANT_1\", 52: \"QUADRANT_2\", 53: \"QUADRANT_3\", 54: \"QUADRANT_4\", 55: \"NEWLINE\", 56: \"SEMI\", 57: \"EOF\", 60: \"STR\", 61: \"MD_STR\", 63: \"PUNCTUATION\", 64: \"PLUS\", 65: \"EQUALS\", 66: \"DOT\", 67: \"UNDERSCORE\" },\n    productions_: [0, [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [3, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [16, 1], [21, 1], [21, 2], [22, 1], [22, 2], [23, 1], [23, 3], [24, 5], [26, 2], [26, 2], [26, 2], [29, 0], [29, 2], [30, 2], [31, 0], [31, 1], [31, 2], [31, 1], [31, 1], [31, 1], [31, 2], [31, 2], [31, 2], [31, 1], [31, 1], [34, 4], [34, 5], [34, 5], [34, 6], [32, 4], [32, 3], [32, 2], [32, 4], [32, 3], [32, 2], [33, 2], [33, 2], [33, 2], [33, 2], [27, 1], [27, 1], [27, 1], [43, 1], [43, 2], [43, 1], [43, 1], [62, 1], [62, 2], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [58, 1], [59, 1], [59, 1], [59, 1]],\n    performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 23:\n          this.$ = $$[$0];\n          break;\n        case 24:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n        case 26:\n          this.$ = $$[$0 - 1] + $$[$0];\n          break;\n        case 27:\n          this.$ = [$$[$0].trim()];\n          break;\n        case 28:\n          $$[$0 - 2].push($$[$0].trim());\n          this.$ = $$[$0 - 2];\n          break;\n        case 29:\n          this.$ = $$[$0 - 4];\n          yy.addClass($$[$0 - 2], $$[$0]);\n          break;\n        case 37:\n          this.$ = [];\n          break;\n        case 42:\n          this.$ = $$[$0].trim();\n          yy.setDiagramTitle(this.$);\n          break;\n        case 43:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 44:\n        case 45:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 46:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 47:\n          yy.addPoint($$[$0 - 3], \"\", $$[$0 - 1], $$[$0], []);\n          break;\n        case 48:\n          yy.addPoint($$[$0 - 4], $$[$0 - 3], $$[$0 - 1], $$[$0], []);\n          break;\n        case 49:\n          yy.addPoint($$[$0 - 4], \"\", $$[$0 - 2], $$[$0 - 1], $$[$0]);\n          break;\n        case 50:\n          yy.addPoint($$[$0 - 5], $$[$0 - 4], $$[$0 - 2], $$[$0 - 1], $$[$0]);\n          break;\n        case 51:\n          yy.setXAxisLeftText($$[$0 - 2]);\n          yy.setXAxisRightText($$[$0]);\n          break;\n        case 52:\n          $$[$0 - 1].text += \" \\u27F6 \";\n          yy.setXAxisLeftText($$[$0 - 1]);\n          break;\n        case 53:\n          yy.setXAxisLeftText($$[$0]);\n          break;\n        case 54:\n          yy.setYAxisBottomText($$[$0 - 2]);\n          yy.setYAxisTopText($$[$0]);\n          break;\n        case 55:\n          $$[$0 - 1].text += \" \\u27F6 \";\n          yy.setYAxisBottomText($$[$0 - 1]);\n          break;\n        case 56:\n          yy.setYAxisBottomText($$[$0]);\n          break;\n        case 57:\n          yy.setQuadrant1Text($$[$0]);\n          break;\n        case 58:\n          yy.setQuadrant2Text($$[$0]);\n          break;\n        case 59:\n          yy.setQuadrant3Text($$[$0]);\n          break;\n        case 60:\n          yy.setQuadrant4Text($$[$0]);\n          break;\n        case 64:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 65:\n          this.$ = { text: $$[$0 - 1].text + \"\" + $$[$0], type: $$[$0 - 1].type };\n          break;\n        case 66:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 67:\n          this.$ = { text: $$[$0], type: \"markdown\" };\n          break;\n        case 68:\n          this.$ = $$[$0];\n          break;\n        case 69:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 18: $V0, 26: 1, 27: 2, 28: $V1, 55: $V2, 56: $V3, 57: $V4 }, { 1: [3] }, { 18: $V0, 26: 8, 27: 2, 28: $V1, 55: $V2, 56: $V3, 57: $V4 }, { 18: $V0, 26: 9, 27: 2, 28: $V1, 55: $V2, 56: $V3, 57: $V4 }, o($V5, [2, 33], { 29: 10 }), o($V6, [2, 61]), o($V6, [2, 62]), o($V6, [2, 63]), { 1: [2, 30] }, { 1: [2, 31] }, o($V7, $V8, { 30: 11, 31: 12, 24: 13, 32: 15, 33: 16, 34: 17, 43: 30, 58: 31, 1: [2, 32], 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $Vf, 25: $Vg, 35: $Vh, 37: $Vi, 39: $Vj, 41: $Vk, 42: $Vl, 48: $Vm, 50: $Vn, 51: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V5, [2, 34]), { 27: 45, 55: $V2, 56: $V3, 57: $V4 }, o($V7, [2, 37]), o($V7, $V8, { 24: 13, 32: 15, 33: 16, 34: 17, 43: 30, 58: 31, 31: 46, 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $Vf, 25: $Vg, 35: $Vh, 37: $Vi, 39: $Vj, 41: $Vk, 42: $Vl, 48: $Vm, 50: $Vn, 51: $Vo, 52: $Vp, 53: $Vq, 54: $Vr, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 39]), o($V7, [2, 40]), o($V7, [2, 41]), { 36: [1, 47] }, { 38: [1, 48] }, { 40: [1, 49] }, o($V7, [2, 45]), o($V7, [2, 46]), { 18: [1, 50] }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 51, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 52, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 53, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 54, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 55, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 43: 56, 58: 31, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, { 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 44: [1, 57], 47: [1, 58], 58: 60, 59: 59, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }, o($VB, [2, 64]), o($VB, [2, 66]), o($VB, [2, 67]), o($VB, [2, 70]), o($VB, [2, 71]), o($VB, [2, 72]), o($VB, [2, 73]), o($VB, [2, 74]), o($VB, [2, 75]), o($VB, [2, 76]), o($VB, [2, 77]), o($VB, [2, 78]), o($VB, [2, 79]), o($VB, [2, 80]), o($V5, [2, 35]), o($V7, [2, 38]), o($V7, [2, 42]), o($V7, [2, 43]), o($V7, [2, 44]), { 3: 64, 4: $VC, 5: $VD, 6: $VE, 7: $VF, 8: $VG, 9: $VH, 10: $VI, 11: $VJ, 12: $VK, 13: $VL, 14: $VM, 15: $VN, 21: 63 }, o($V7, [2, 53], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 49: [1, 77], 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 56], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 49: [1, 78], 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 57], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 58], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 59], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 60], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), { 45: [1, 79] }, { 44: [1, 80] }, o($VB, [2, 65]), o($VB, [2, 81]), o($VB, [2, 82]), o($VB, [2, 83]), { 3: 82, 4: $VC, 5: $VD, 6: $VE, 7: $VF, 8: $VG, 9: $VH, 10: $VI, 11: $VJ, 12: $VK, 13: $VL, 14: $VM, 15: $VN, 18: [1, 81] }, o($VO, [2, 23]), o($VO, [2, 1]), o($VO, [2, 2]), o($VO, [2, 3]), o($VO, [2, 4]), o($VO, [2, 5]), o($VO, [2, 6]), o($VO, [2, 7]), o($VO, [2, 8]), o($VO, [2, 9]), o($VO, [2, 10]), o($VO, [2, 11]), o($VO, [2, 12]), o($V7, [2, 52], { 58: 31, 43: 83, 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 55], { 58: 31, 43: 84, 4: $V9, 5: $Va, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 60: $Vs, 61: $Vt, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), { 46: [1, 85] }, { 45: [1, 86] }, { 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 16: 89, 17: $VV, 18: $VW, 19: $VX, 20: $VY, 22: 88, 23: 87 }, o($VO, [2, 24]), o($V7, [2, 51], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 54], { 59: 59, 58: 60, 4: $V9, 5: $Va, 8: $Vz, 10: $Vb, 12: $Vc, 13: $Vd, 14: $Ve, 18: $VA, 63: $Vu, 64: $Vv, 65: $Vw, 66: $Vx, 67: $Vy }), o($V7, [2, 47], { 22: 88, 16: 89, 23: 100, 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 17: $VV, 18: $VW, 19: $VX, 20: $VY }), { 46: [1, 101] }, o($V7, [2, 29], { 10: $VZ }), o($V_, [2, 27], { 16: 103, 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 17: $VV, 18: $VW, 19: $VX, 20: $VY }), o($V$, [2, 25]), o($V$, [2, 13]), o($V$, [2, 14]), o($V$, [2, 15]), o($V$, [2, 16]), o($V$, [2, 17]), o($V$, [2, 18]), o($V$, [2, 19]), o($V$, [2, 20]), o($V$, [2, 21]), o($V$, [2, 22]), o($V7, [2, 49], { 10: $VZ }), o($V7, [2, 48], { 22: 88, 16: 89, 23: 104, 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 17: $VV, 18: $VW, 19: $VX, 20: $VY }), { 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 16: 89, 17: $VV, 18: $VW, 19: $VX, 20: $VY, 22: 105 }, o($V$, [2, 26]), o($V7, [2, 50], { 10: $VZ }), o($V_, [2, 28], { 16: 103, 4: $VP, 5: $VQ, 6: $VR, 8: $VS, 11: $VT, 13: $VU, 17: $VV, 18: $VW, 19: $VX, 20: $VY })],\n    defaultActions: { 8: [2, 30], 9: [2, 31] },\n    parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            return 55;\n            break;\n          case 3:\n            break;\n          case 4:\n            this.begin(\"title\");\n            return 35;\n            break;\n          case 5:\n            this.popState();\n            return \"title_value\";\n            break;\n          case 6:\n            this.begin(\"acc_title\");\n            return 37;\n            break;\n          case 7:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 8:\n            this.begin(\"acc_descr\");\n            return 39;\n            break;\n          case 9:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 10:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 11:\n            this.popState();\n            break;\n          case 12:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 13:\n            return 48;\n            break;\n          case 14:\n            return 50;\n            break;\n          case 15:\n            return 49;\n            break;\n          case 16:\n            return 51;\n            break;\n          case 17:\n            return 52;\n            break;\n          case 18:\n            return 53;\n            break;\n          case 19:\n            return 54;\n            break;\n          case 20:\n            return 25;\n            break;\n          case 21:\n            this.begin(\"md_string\");\n            break;\n          case 22:\n            return \"MD_STR\";\n            break;\n          case 23:\n            this.popState();\n            break;\n          case 24:\n            this.begin(\"string\");\n            break;\n          case 25:\n            this.popState();\n            break;\n          case 26:\n            return \"STR\";\n            break;\n          case 27:\n            this.begin(\"class_name\");\n            break;\n          case 28:\n            this.popState();\n            return 47;\n            break;\n          case 29:\n            this.begin(\"point_start\");\n            return 44;\n            break;\n          case 30:\n            this.begin(\"point_x\");\n            return 45;\n            break;\n          case 31:\n            this.popState();\n            break;\n          case 32:\n            this.popState();\n            this.begin(\"point_y\");\n            break;\n          case 33:\n            this.popState();\n            return 46;\n            break;\n          case 34:\n            return 28;\n            break;\n          case 35:\n            return 4;\n            break;\n          case 36:\n            return 11;\n            break;\n          case 37:\n            return 64;\n            break;\n          case 38:\n            return 10;\n            break;\n          case 39:\n            return 65;\n            break;\n          case 40:\n            return 65;\n            break;\n          case 41:\n            return 14;\n            break;\n          case 42:\n            return 13;\n            break;\n          case 43:\n            return 67;\n            break;\n          case 44:\n            return 66;\n            break;\n          case 45:\n            return 12;\n            break;\n          case 46:\n            return 8;\n            break;\n          case 47:\n            return 5;\n            break;\n          case 48:\n            return 18;\n            break;\n          case 49:\n            return 56;\n            break;\n          case 50:\n            return 63;\n            break;\n          case 51:\n            return 57;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n\\r]+)/i, /^(?:%%[^\\n]*)/i, /^(?:title\\b)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?: *x-axis *)/i, /^(?: *y-axis *)/i, /^(?: *--+> *)/i, /^(?: *quadrant-1 *)/i, /^(?: *quadrant-2 *)/i, /^(?: *quadrant-3 *)/i, /^(?: *quadrant-4 *)/i, /^(?:classDef\\b)/i, /^(?:[\"][`])/i, /^(?:[^`\"]+)/i, /^(?:[`][\"])/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?::::)/i, /^(?:^\\w+)/i, /^(?:\\s*:\\s*\\[\\s*)/i, /^(?:(1)|(0(.\\d+)?))/i, /^(?:\\s*\\] *)/i, /^(?:\\s*,\\s*)/i, /^(?:(1)|(0(.\\d+)?))/i, /^(?: *quadrantChart *)/i, /^(?:[A-Za-z]+)/i, /^(?::)/i, /^(?:\\+)/i, /^(?:,)/i, /^(?:=)/i, /^(?:=)/i, /^(?:\\*)/i, /^(?:#)/i, /^(?:[\\_])/i, /^(?:\\.)/i, /^(?:&)/i, /^(?:-)/i, /^(?:[0-9]+)/i, /^(?:\\s)/i, /^(?:;)/i, /^(?:[!\"#$%&'*+,-.`?\\\\_/])/i, /^(?:$)/i],\n      conditions: { \"class_name\": { \"rules\": [28], \"inclusive\": false }, \"point_y\": { \"rules\": [33], \"inclusive\": false }, \"point_x\": { \"rules\": [32], \"inclusive\": false }, \"point_start\": { \"rules\": [30, 31], \"inclusive\": false }, \"acc_descr_multiline\": { \"rules\": [11, 12], \"inclusive\": false }, \"acc_descr\": { \"rules\": [9], \"inclusive\": false }, \"acc_title\": { \"rules\": [7], \"inclusive\": false }, \"title\": { \"rules\": [5], \"inclusive\": false }, \"md_string\": { \"rules\": [22, 23], \"inclusive\": false }, \"string\": { \"rules\": [25, 26], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 6, 8, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 24, 27, 29, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar quadrant_default = parser;\n\n// src/diagrams/quadrant-chart/quadrantBuilder.ts\n\nvar defaultThemeVariables = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.getThemeVariables)();\nvar QuadrantBuilder = class {\n  constructor() {\n    this.classes = /* @__PURE__ */ new Map();\n    this.config = this.getDefaultConfig();\n    this.themeConfig = this.getDefaultThemeConfig();\n    this.data = this.getDefaultData();\n  }\n  static {\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(this, \"QuadrantBuilder\");\n  }\n  getDefaultData() {\n    return {\n      titleText: \"\",\n      quadrant1Text: \"\",\n      quadrant2Text: \"\",\n      quadrant3Text: \"\",\n      quadrant4Text: \"\",\n      xAxisLeftText: \"\",\n      xAxisRightText: \"\",\n      yAxisBottomText: \"\",\n      yAxisTopText: \"\",\n      points: []\n    };\n  }\n  getDefaultConfig() {\n    return {\n      showXAxis: true,\n      showYAxis: true,\n      showTitle: true,\n      chartHeight: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.chartWidth || 500,\n      chartWidth: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.chartHeight || 500,\n      titlePadding: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.titlePadding || 10,\n      titleFontSize: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.titleFontSize || 20,\n      quadrantPadding: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.quadrantPadding || 5,\n      xAxisLabelPadding: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.xAxisLabelPadding || 5,\n      yAxisLabelPadding: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.yAxisLabelPadding || 5,\n      xAxisLabelFontSize: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.xAxisLabelFontSize || 16,\n      yAxisLabelFontSize: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.yAxisLabelFontSize || 16,\n      quadrantLabelFontSize: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.quadrantLabelFontSize || 16,\n      quadrantTextTopPadding: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.quadrantTextTopPadding || 5,\n      pointTextPadding: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.pointTextPadding || 5,\n      pointLabelFontSize: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.pointLabelFontSize || 12,\n      pointRadius: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.pointRadius || 5,\n      xAxisPosition: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.xAxisPosition || \"top\",\n      yAxisPosition: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.yAxisPosition || \"left\",\n      quadrantInternalBorderStrokeWidth: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.quadrantInternalBorderStrokeWidth || 1,\n      quadrantExternalBorderStrokeWidth: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultConfig_default.quadrantChart?.quadrantExternalBorderStrokeWidth || 2\n    };\n  }\n  getDefaultThemeConfig() {\n    return {\n      quadrant1Fill: defaultThemeVariables.quadrant1Fill,\n      quadrant2Fill: defaultThemeVariables.quadrant2Fill,\n      quadrant3Fill: defaultThemeVariables.quadrant3Fill,\n      quadrant4Fill: defaultThemeVariables.quadrant4Fill,\n      quadrant1TextFill: defaultThemeVariables.quadrant1TextFill,\n      quadrant2TextFill: defaultThemeVariables.quadrant2TextFill,\n      quadrant3TextFill: defaultThemeVariables.quadrant3TextFill,\n      quadrant4TextFill: defaultThemeVariables.quadrant4TextFill,\n      quadrantPointFill: defaultThemeVariables.quadrantPointFill,\n      quadrantPointTextFill: defaultThemeVariables.quadrantPointTextFill,\n      quadrantXAxisTextFill: defaultThemeVariables.quadrantXAxisTextFill,\n      quadrantYAxisTextFill: defaultThemeVariables.quadrantYAxisTextFill,\n      quadrantTitleFill: defaultThemeVariables.quadrantTitleFill,\n      quadrantInternalBorderStrokeFill: defaultThemeVariables.quadrantInternalBorderStrokeFill,\n      quadrantExternalBorderStrokeFill: defaultThemeVariables.quadrantExternalBorderStrokeFill\n    };\n  }\n  clear() {\n    this.config = this.getDefaultConfig();\n    this.themeConfig = this.getDefaultThemeConfig();\n    this.data = this.getDefaultData();\n    this.classes = /* @__PURE__ */ new Map();\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.info(\"clear called\");\n  }\n  setData(data) {\n    this.data = { ...this.data, ...data };\n  }\n  addPoints(points) {\n    this.data.points = [...points, ...this.data.points];\n  }\n  addClass(className, styles) {\n    this.classes.set(className, styles);\n  }\n  setConfig(config2) {\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.trace(\"setConfig called with: \", config2);\n    this.config = { ...this.config, ...config2 };\n  }\n  setThemeConfig(themeConfig) {\n    _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.trace(\"setThemeConfig called with: \", themeConfig);\n    this.themeConfig = { ...this.themeConfig, ...themeConfig };\n  }\n  calculateSpace(xAxisPosition, showXAxis, showYAxis, showTitle) {\n    const xAxisSpaceCalculation = this.config.xAxisLabelPadding * 2 + this.config.xAxisLabelFontSize;\n    const xAxisSpace = {\n      top: xAxisPosition === \"top\" && showXAxis ? xAxisSpaceCalculation : 0,\n      bottom: xAxisPosition === \"bottom\" && showXAxis ? xAxisSpaceCalculation : 0\n    };\n    const yAxisSpaceCalculation = this.config.yAxisLabelPadding * 2 + this.config.yAxisLabelFontSize;\n    const yAxisSpace = {\n      left: this.config.yAxisPosition === \"left\" && showYAxis ? yAxisSpaceCalculation : 0,\n      right: this.config.yAxisPosition === \"right\" && showYAxis ? yAxisSpaceCalculation : 0\n    };\n    const titleSpaceCalculation = this.config.titleFontSize + this.config.titlePadding * 2;\n    const titleSpace = {\n      top: showTitle ? titleSpaceCalculation : 0\n    };\n    const quadrantLeft = this.config.quadrantPadding + yAxisSpace.left;\n    const quadrantTop = this.config.quadrantPadding + xAxisSpace.top + titleSpace.top;\n    const quadrantWidth = this.config.chartWidth - this.config.quadrantPadding * 2 - yAxisSpace.left - yAxisSpace.right;\n    const quadrantHeight = this.config.chartHeight - this.config.quadrantPadding * 2 - xAxisSpace.top - xAxisSpace.bottom - titleSpace.top;\n    const quadrantHalfWidth = quadrantWidth / 2;\n    const quadrantHalfHeight = quadrantHeight / 2;\n    const quadrantSpace = {\n      quadrantLeft,\n      quadrantTop,\n      quadrantWidth,\n      quadrantHalfWidth,\n      quadrantHeight,\n      quadrantHalfHeight\n    };\n    return {\n      xAxisSpace,\n      yAxisSpace,\n      titleSpace,\n      quadrantSpace\n    };\n  }\n  getAxisLabels(xAxisPosition, showXAxis, showYAxis, spaceData) {\n    const { quadrantSpace, titleSpace } = spaceData;\n    const {\n      quadrantHalfHeight,\n      quadrantHeight,\n      quadrantLeft,\n      quadrantHalfWidth,\n      quadrantTop,\n      quadrantWidth\n    } = quadrantSpace;\n    const drawXAxisLabelsInMiddle = Boolean(this.data.xAxisRightText);\n    const drawYAxisLabelsInMiddle = Boolean(this.data.yAxisTopText);\n    const axisLabels = [];\n    if (this.data.xAxisLeftText && showXAxis) {\n      axisLabels.push({\n        text: this.data.xAxisLeftText,\n        fill: this.themeConfig.quadrantXAxisTextFill,\n        x: quadrantLeft + (drawXAxisLabelsInMiddle ? quadrantHalfWidth / 2 : 0),\n        y: xAxisPosition === \"top\" ? this.config.xAxisLabelPadding + titleSpace.top : this.config.xAxisLabelPadding + quadrantTop + quadrantHeight + this.config.quadrantPadding,\n        fontSize: this.config.xAxisLabelFontSize,\n        verticalPos: drawXAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: 0\n      });\n    }\n    if (this.data.xAxisRightText && showXAxis) {\n      axisLabels.push({\n        text: this.data.xAxisRightText,\n        fill: this.themeConfig.quadrantXAxisTextFill,\n        x: quadrantLeft + quadrantHalfWidth + (drawXAxisLabelsInMiddle ? quadrantHalfWidth / 2 : 0),\n        y: xAxisPosition === \"top\" ? this.config.xAxisLabelPadding + titleSpace.top : this.config.xAxisLabelPadding + quadrantTop + quadrantHeight + this.config.quadrantPadding,\n        fontSize: this.config.xAxisLabelFontSize,\n        verticalPos: drawXAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: 0\n      });\n    }\n    if (this.data.yAxisBottomText && showYAxis) {\n      axisLabels.push({\n        text: this.data.yAxisBottomText,\n        fill: this.themeConfig.quadrantYAxisTextFill,\n        x: this.config.yAxisPosition === \"left\" ? this.config.yAxisLabelPadding : this.config.yAxisLabelPadding + quadrantLeft + quadrantWidth + this.config.quadrantPadding,\n        y: quadrantTop + quadrantHeight - (drawYAxisLabelsInMiddle ? quadrantHalfHeight / 2 : 0),\n        fontSize: this.config.yAxisLabelFontSize,\n        verticalPos: drawYAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: -90\n      });\n    }\n    if (this.data.yAxisTopText && showYAxis) {\n      axisLabels.push({\n        text: this.data.yAxisTopText,\n        fill: this.themeConfig.quadrantYAxisTextFill,\n        x: this.config.yAxisPosition === \"left\" ? this.config.yAxisLabelPadding : this.config.yAxisLabelPadding + quadrantLeft + quadrantWidth + this.config.quadrantPadding,\n        y: quadrantTop + quadrantHalfHeight - (drawYAxisLabelsInMiddle ? quadrantHalfHeight / 2 : 0),\n        fontSize: this.config.yAxisLabelFontSize,\n        verticalPos: drawYAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: -90\n      });\n    }\n    return axisLabels;\n  }\n  getQuadrants(spaceData) {\n    const { quadrantSpace } = spaceData;\n    const { quadrantHalfHeight, quadrantLeft, quadrantHalfWidth, quadrantTop } = quadrantSpace;\n    const quadrants = [\n      {\n        text: {\n          text: this.data.quadrant1Text,\n          fill: this.themeConfig.quadrant1TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft + quadrantHalfWidth,\n        y: quadrantTop,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant1Fill\n      },\n      {\n        text: {\n          text: this.data.quadrant2Text,\n          fill: this.themeConfig.quadrant2TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft,\n        y: quadrantTop,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant2Fill\n      },\n      {\n        text: {\n          text: this.data.quadrant3Text,\n          fill: this.themeConfig.quadrant3TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft,\n        y: quadrantTop + quadrantHalfHeight,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant3Fill\n      },\n      {\n        text: {\n          text: this.data.quadrant4Text,\n          fill: this.themeConfig.quadrant4TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft + quadrantHalfWidth,\n        y: quadrantTop + quadrantHalfHeight,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant4Fill\n      }\n    ];\n    for (const quadrant of quadrants) {\n      quadrant.text.x = quadrant.x + quadrant.width / 2;\n      if (this.data.points.length === 0) {\n        quadrant.text.y = quadrant.y + quadrant.height / 2;\n        quadrant.text.horizontalPos = \"middle\";\n      } else {\n        quadrant.text.y = quadrant.y + this.config.quadrantTextTopPadding;\n        quadrant.text.horizontalPos = \"top\";\n      }\n    }\n    return quadrants;\n  }\n  getQuadrantPoints(spaceData) {\n    const { quadrantSpace } = spaceData;\n    const { quadrantHeight, quadrantLeft, quadrantTop, quadrantWidth } = quadrantSpace;\n    const xAxis = (0,d3__WEBPACK_IMPORTED_MODULE_1__.scaleLinear)().domain([0, 1]).range([quadrantLeft, quadrantWidth + quadrantLeft]);\n    const yAxis = (0,d3__WEBPACK_IMPORTED_MODULE_1__.scaleLinear)().domain([0, 1]).range([quadrantHeight + quadrantTop, quadrantTop]);\n    const points = this.data.points.map((point) => {\n      const classStyles = this.classes.get(point.className);\n      if (classStyles) {\n        point = { ...classStyles, ...point };\n      }\n      const props = {\n        x: xAxis(point.x),\n        y: yAxis(point.y),\n        fill: point.color ?? this.themeConfig.quadrantPointFill,\n        radius: point.radius ?? this.config.pointRadius,\n        text: {\n          text: point.text,\n          fill: this.themeConfig.quadrantPointTextFill,\n          x: xAxis(point.x),\n          y: yAxis(point.y) + this.config.pointTextPadding,\n          verticalPos: \"center\",\n          horizontalPos: \"top\",\n          fontSize: this.config.pointLabelFontSize,\n          rotation: 0\n        },\n        strokeColor: point.strokeColor ?? this.themeConfig.quadrantPointFill,\n        strokeWidth: point.strokeWidth ?? \"0px\"\n      };\n      return props;\n    });\n    return points;\n  }\n  getBorders(spaceData) {\n    const halfExternalBorderWidth = this.config.quadrantExternalBorderStrokeWidth / 2;\n    const { quadrantSpace } = spaceData;\n    const {\n      quadrantHalfHeight,\n      quadrantHeight,\n      quadrantLeft,\n      quadrantHalfWidth,\n      quadrantTop,\n      quadrantWidth\n    } = quadrantSpace;\n    const borderLines = [\n      // top border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft - halfExternalBorderWidth,\n        y1: quadrantTop,\n        x2: quadrantLeft + quadrantWidth + halfExternalBorderWidth,\n        y2: quadrantTop\n      },\n      // right border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft + quadrantWidth,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft + quadrantWidth,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth\n      },\n      // bottom border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft - halfExternalBorderWidth,\n        y1: quadrantTop + quadrantHeight,\n        x2: quadrantLeft + quadrantWidth + halfExternalBorderWidth,\n        y2: quadrantTop + quadrantHeight\n      },\n      // left border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth\n      },\n      // vertical inner border\n      {\n        strokeFill: this.themeConfig.quadrantInternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantInternalBorderStrokeWidth,\n        x1: quadrantLeft + quadrantHalfWidth,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft + quadrantHalfWidth,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth\n      },\n      // horizontal inner border\n      {\n        strokeFill: this.themeConfig.quadrantInternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantInternalBorderStrokeWidth,\n        x1: quadrantLeft + halfExternalBorderWidth,\n        y1: quadrantTop + quadrantHalfHeight,\n        x2: quadrantLeft + quadrantWidth - halfExternalBorderWidth,\n        y2: quadrantTop + quadrantHalfHeight\n      }\n    ];\n    return borderLines;\n  }\n  getTitle(showTitle) {\n    if (showTitle) {\n      return {\n        text: this.data.titleText,\n        fill: this.themeConfig.quadrantTitleFill,\n        fontSize: this.config.titleFontSize,\n        horizontalPos: \"top\",\n        verticalPos: \"center\",\n        rotation: 0,\n        y: this.config.titlePadding,\n        x: this.config.chartWidth / 2\n      };\n    }\n    return;\n  }\n  build() {\n    const showXAxis = this.config.showXAxis && !!(this.data.xAxisLeftText || this.data.xAxisRightText);\n    const showYAxis = this.config.showYAxis && !!(this.data.yAxisTopText || this.data.yAxisBottomText);\n    const showTitle = this.config.showTitle && !!this.data.titleText;\n    const xAxisPosition = this.data.points.length > 0 ? \"bottom\" : this.config.xAxisPosition;\n    const calculatedSpace = this.calculateSpace(xAxisPosition, showXAxis, showYAxis, showTitle);\n    return {\n      points: this.getQuadrantPoints(calculatedSpace),\n      quadrants: this.getQuadrants(calculatedSpace),\n      axisLabels: this.getAxisLabels(xAxisPosition, showXAxis, showYAxis, calculatedSpace),\n      borderLines: this.getBorders(calculatedSpace),\n      title: this.getTitle(showTitle)\n    };\n  }\n};\n\n// src/diagrams/quadrant-chart/utils.ts\nvar InvalidStyleError = class extends Error {\n  static {\n    (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(this, \"InvalidStyleError\");\n  }\n  constructor(style, value, type) {\n    super(`value for ${style} ${value} is invalid, please use a valid ${type}`);\n    this.name = \"InvalidStyleError\";\n  }\n};\nfunction validateHexCode(value) {\n  return !/^#?([\\dA-Fa-f]{6}|[\\dA-Fa-f]{3})$/.test(value);\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(validateHexCode, \"validateHexCode\");\nfunction validateNumber(value) {\n  return !/^\\d+$/.test(value);\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(validateNumber, \"validateNumber\");\nfunction validateSizeInPixels(value) {\n  return !/^\\d+px$/.test(value);\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(validateSizeInPixels, \"validateSizeInPixels\");\n\n// src/diagrams/quadrant-chart/quadrantDb.ts\nvar config = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.getConfig2)();\nfunction textSanitizer(text) {\n  return (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.sanitizeText)(text.trim(), config);\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(textSanitizer, \"textSanitizer\");\nvar quadrantBuilder = new QuadrantBuilder();\nfunction setQuadrant1Text(textObj) {\n  quadrantBuilder.setData({ quadrant1Text: textSanitizer(textObj.text) });\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setQuadrant1Text, \"setQuadrant1Text\");\nfunction setQuadrant2Text(textObj) {\n  quadrantBuilder.setData({ quadrant2Text: textSanitizer(textObj.text) });\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setQuadrant2Text, \"setQuadrant2Text\");\nfunction setQuadrant3Text(textObj) {\n  quadrantBuilder.setData({ quadrant3Text: textSanitizer(textObj.text) });\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setQuadrant3Text, \"setQuadrant3Text\");\nfunction setQuadrant4Text(textObj) {\n  quadrantBuilder.setData({ quadrant4Text: textSanitizer(textObj.text) });\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setQuadrant4Text, \"setQuadrant4Text\");\nfunction setXAxisLeftText(textObj) {\n  quadrantBuilder.setData({ xAxisLeftText: textSanitizer(textObj.text) });\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setXAxisLeftText, \"setXAxisLeftText\");\nfunction setXAxisRightText(textObj) {\n  quadrantBuilder.setData({ xAxisRightText: textSanitizer(textObj.text) });\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setXAxisRightText, \"setXAxisRightText\");\nfunction setYAxisTopText(textObj) {\n  quadrantBuilder.setData({ yAxisTopText: textSanitizer(textObj.text) });\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setYAxisTopText, \"setYAxisTopText\");\nfunction setYAxisBottomText(textObj) {\n  quadrantBuilder.setData({ yAxisBottomText: textSanitizer(textObj.text) });\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setYAxisBottomText, \"setYAxisBottomText\");\nfunction parseStyles(styles) {\n  const stylesObject = {};\n  for (const style of styles) {\n    const [key, value] = style.trim().split(/\\s*:\\s*/);\n    if (key === \"radius\") {\n      if (validateNumber(value)) {\n        throw new InvalidStyleError(key, value, \"number\");\n      }\n      stylesObject.radius = parseInt(value);\n    } else if (key === \"color\") {\n      if (validateHexCode(value)) {\n        throw new InvalidStyleError(key, value, \"hex code\");\n      }\n      stylesObject.color = value;\n    } else if (key === \"stroke-color\") {\n      if (validateHexCode(value)) {\n        throw new InvalidStyleError(key, value, \"hex code\");\n      }\n      stylesObject.strokeColor = value;\n    } else if (key === \"stroke-width\") {\n      if (validateSizeInPixels(value)) {\n        throw new InvalidStyleError(key, value, \"number of pixels (eg. 10px)\");\n      }\n      stylesObject.strokeWidth = value;\n    } else {\n      throw new Error(`style named ${key} is not supported.`);\n    }\n  }\n  return stylesObject;\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(parseStyles, \"parseStyles\");\nfunction addPoint(textObj, className, x, y, styles) {\n  const stylesObject = parseStyles(styles);\n  quadrantBuilder.addPoints([\n    {\n      x,\n      y,\n      text: textSanitizer(textObj.text),\n      className,\n      ...stylesObject\n    }\n  ]);\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(addPoint, \"addPoint\");\nfunction addClass(className, styles) {\n  quadrantBuilder.addClass(className, parseStyles(styles));\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(addClass, \"addClass\");\nfunction setWidth(width) {\n  quadrantBuilder.setConfig({ chartWidth: width });\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setWidth, \"setWidth\");\nfunction setHeight(height) {\n  quadrantBuilder.setConfig({ chartHeight: height });\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(setHeight, \"setHeight\");\nfunction getQuadrantData() {\n  const config2 = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.getConfig2)();\n  const { themeVariables, quadrantChart: quadrantChartConfig } = config2;\n  if (quadrantChartConfig) {\n    quadrantBuilder.setConfig(quadrantChartConfig);\n  }\n  quadrantBuilder.setThemeConfig({\n    quadrant1Fill: themeVariables.quadrant1Fill,\n    quadrant2Fill: themeVariables.quadrant2Fill,\n    quadrant3Fill: themeVariables.quadrant3Fill,\n    quadrant4Fill: themeVariables.quadrant4Fill,\n    quadrant1TextFill: themeVariables.quadrant1TextFill,\n    quadrant2TextFill: themeVariables.quadrant2TextFill,\n    quadrant3TextFill: themeVariables.quadrant3TextFill,\n    quadrant4TextFill: themeVariables.quadrant4TextFill,\n    quadrantPointFill: themeVariables.quadrantPointFill,\n    quadrantPointTextFill: themeVariables.quadrantPointTextFill,\n    quadrantXAxisTextFill: themeVariables.quadrantXAxisTextFill,\n    quadrantYAxisTextFill: themeVariables.quadrantYAxisTextFill,\n    quadrantExternalBorderStrokeFill: themeVariables.quadrantExternalBorderStrokeFill,\n    quadrantInternalBorderStrokeFill: themeVariables.quadrantInternalBorderStrokeFill,\n    quadrantTitleFill: themeVariables.quadrantTitleFill\n  });\n  quadrantBuilder.setData({ titleText: (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.getDiagramTitle)() });\n  return quadrantBuilder.build();\n}\n(0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(getQuadrantData, \"getQuadrantData\");\nvar clear2 = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(function() {\n  quadrantBuilder.clear();\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.clear)();\n}, \"clear\");\nvar quadrantDb_default = {\n  setWidth,\n  setHeight,\n  setQuadrant1Text,\n  setQuadrant2Text,\n  setQuadrant3Text,\n  setQuadrant4Text,\n  setXAxisLeftText,\n  setXAxisRightText,\n  setYAxisTopText,\n  setYAxisBottomText,\n  parseStyles,\n  addPoint,\n  addClass,\n  getQuadrantData,\n  clear: clear2,\n  setAccTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.setAccTitle,\n  getAccTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.getAccTitle,\n  setDiagramTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.setDiagramTitle,\n  getDiagramTitle: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.getDiagramTitle,\n  getAccDescription: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.getAccDescription,\n  setAccDescription: _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.setAccDescription\n};\n\n// src/diagrams/quadrant-chart/quadrantRenderer.ts\n\nvar draw = /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)((txt, id, _version, diagObj) => {\n  function getDominantBaseLine(horizontalPos) {\n    return horizontalPos === \"top\" ? \"hanging\" : \"middle\";\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(getDominantBaseLine, \"getDominantBaseLine\");\n  function getTextAnchor(verticalPos) {\n    return verticalPos === \"left\" ? \"start\" : \"middle\";\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(getTextAnchor, \"getTextAnchor\");\n  function getTransformation(data) {\n    return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;\n  }\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(getTransformation, \"getTransformation\");\n  const conf = (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.getConfig2)();\n  _chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.log.debug(\"Rendering quadrant chart\\n\" + txt);\n  const securityLevel = conf.securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_1__.select)(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  const group = svg.append(\"g\").attr(\"class\", \"main\");\n  const width = conf.quadrantChart?.chartWidth ?? 500;\n  const height = conf.quadrantChart?.chartHeight ?? 500;\n  (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.configureSvgSize)(svg, height, width, conf.quadrantChart?.useMaxWidth ?? true);\n  svg.attr(\"viewBox\", \"0 0 \" + width + \" \" + height);\n  diagObj.db.setHeight(height);\n  diagObj.db.setWidth(width);\n  const quadrantData = diagObj.db.getQuadrantData();\n  const quadrantsGroup = group.append(\"g\").attr(\"class\", \"quadrants\");\n  const borderGroup = group.append(\"g\").attr(\"class\", \"border\");\n  const dataPointGroup = group.append(\"g\").attr(\"class\", \"data-points\");\n  const labelGroup = group.append(\"g\").attr(\"class\", \"labels\");\n  const titleGroup = group.append(\"g\").attr(\"class\", \"title\");\n  if (quadrantData.title) {\n    titleGroup.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", quadrantData.title.fill).attr(\"font-size\", quadrantData.title.fontSize).attr(\"dominant-baseline\", getDominantBaseLine(quadrantData.title.horizontalPos)).attr(\"text-anchor\", getTextAnchor(quadrantData.title.verticalPos)).attr(\"transform\", getTransformation(quadrantData.title)).text(quadrantData.title.text);\n  }\n  if (quadrantData.borderLines) {\n    borderGroup.selectAll(\"line\").data(quadrantData.borderLines).enter().append(\"line\").attr(\"x1\", (data) => data.x1).attr(\"y1\", (data) => data.y1).attr(\"x2\", (data) => data.x2).attr(\"y2\", (data) => data.y2).style(\"stroke\", (data) => data.strokeFill).style(\"stroke-width\", (data) => data.strokeWidth);\n  }\n  const quadrants = quadrantsGroup.selectAll(\"g.quadrant\").data(quadrantData.quadrants).enter().append(\"g\").attr(\"class\", \"quadrant\");\n  quadrants.append(\"rect\").attr(\"x\", (data) => data.x).attr(\"y\", (data) => data.y).attr(\"width\", (data) => data.width).attr(\"height\", (data) => data.height).attr(\"fill\", (data) => data.fill);\n  quadrants.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", (data) => data.text.fill).attr(\"font-size\", (data) => data.text.fontSize).attr(\n    \"dominant-baseline\",\n    (data) => getDominantBaseLine(data.text.horizontalPos)\n  ).attr(\"text-anchor\", (data) => getTextAnchor(data.text.verticalPos)).attr(\"transform\", (data) => getTransformation(data.text)).text((data) => data.text.text);\n  const labels = labelGroup.selectAll(\"g.label\").data(quadrantData.axisLabels).enter().append(\"g\").attr(\"class\", \"label\");\n  labels.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).text((data) => data.text).attr(\"fill\", (data) => data.fill).attr(\"font-size\", (data) => data.fontSize).attr(\"dominant-baseline\", (data) => getDominantBaseLine(data.horizontalPos)).attr(\"text-anchor\", (data) => getTextAnchor(data.verticalPos)).attr(\"transform\", (data) => getTransformation(data));\n  const dataPoints = dataPointGroup.selectAll(\"g.data-point\").data(quadrantData.points).enter().append(\"g\").attr(\"class\", \"data-point\");\n  dataPoints.append(\"circle\").attr(\"cx\", (data) => data.x).attr(\"cy\", (data) => data.y).attr(\"r\", (data) => data.radius).attr(\"fill\", (data) => data.fill).attr(\"stroke\", (data) => data.strokeColor).attr(\"stroke-width\", (data) => data.strokeWidth);\n  dataPoints.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).text((data) => data.text.text).attr(\"fill\", (data) => data.text.fill).attr(\"font-size\", (data) => data.text.fontSize).attr(\n    \"dominant-baseline\",\n    (data) => getDominantBaseLine(data.text.horizontalPos)\n  ).attr(\"text-anchor\", (data) => getTextAnchor(data.text.verticalPos)).attr(\"transform\", (data) => getTransformation(data.text));\n}, \"draw\");\nvar quadrantRenderer_default = {\n  draw\n};\n\n// src/diagrams/quadrant-chart/quadrantDiagram.ts\nvar diagram = {\n  parser: quadrant_default,\n  db: quadrantDb_default,\n  renderer: quadrantRenderer_default,\n  styles: /* @__PURE__ */ (0,_chunk_YTJNT7DU_mjs__WEBPACK_IMPORTED_MODULE_0__.__name)(() => \"\", \"styles\")\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/chunks/mermaid.core/quadrantDiagram-7GDLP6J5.mjs\n"));

/***/ })

}]);