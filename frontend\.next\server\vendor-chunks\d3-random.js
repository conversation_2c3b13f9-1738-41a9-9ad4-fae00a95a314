"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-random";
exports.ids = ["vendor-chunks/d3-random"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-random/src/bates.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-random/src/bates.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n/* harmony import */ var _irwinHall_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./irwinHall.js */ \"(ssr)/./node_modules/d3-random/src/irwinHall.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomBates(source) {\n  var I = _irwinHall_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].source(source);\n\n  function randomBates(n) {\n    // use limiting distribution at n === 0\n    if ((n = +n) === 0) return source;\n    var randomIrwinHall = I(n);\n    return function() {\n      return randomIrwinHall() / n;\n    };\n  }\n\n  randomBates.source = sourceRandomBates;\n\n  return randomBates;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9iYXRlcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0M7QUFDUjs7QUFFdkMsaUVBQWU7QUFDZixVQUFVLHFEQUFTOztBQUVuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EsQ0FBQyxFQUFFLHlEQUFhLENBQUMsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1yYW5kb21cXHNyY1xcYmF0ZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGRlZmF1bHRTb3VyY2UgZnJvbSBcIi4vZGVmYXVsdFNvdXJjZS5qc1wiO1xuaW1wb3J0IGlyd2luSGFsbCBmcm9tIFwiLi9pcndpbkhhbGwuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgKGZ1bmN0aW9uIHNvdXJjZVJhbmRvbUJhdGVzKHNvdXJjZSkge1xuICB2YXIgSSA9IGlyd2luSGFsbC5zb3VyY2Uoc291cmNlKTtcblxuICBmdW5jdGlvbiByYW5kb21CYXRlcyhuKSB7XG4gICAgLy8gdXNlIGxpbWl0aW5nIGRpc3RyaWJ1dGlvbiBhdCBuID09PSAwXG4gICAgaWYgKChuID0gK24pID09PSAwKSByZXR1cm4gc291cmNlO1xuICAgIHZhciByYW5kb21JcndpbkhhbGwgPSBJKG4pO1xuICAgIHJldHVybiBmdW5jdGlvbigpIHtcbiAgICAgIHJldHVybiByYW5kb21JcndpbkhhbGwoKSAvIG47XG4gICAgfTtcbiAgfVxuXG4gIHJhbmRvbUJhdGVzLnNvdXJjZSA9IHNvdXJjZVJhbmRvbUJhdGVzO1xuXG4gIHJldHVybiByYW5kb21CYXRlcztcbn0pKGRlZmF1bHRTb3VyY2UpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/bates.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/bernoulli.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-random/src/bernoulli.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomBernoulli(source) {\n  function randomBernoulli(p) {\n    if ((p = +p) < 0 || p > 1) throw new RangeError(\"invalid p\");\n    return function() {\n      return Math.floor(source() + p);\n    };\n  }\n\n  randomBernoulli.source = sourceRandomBernoulli;\n\n  return randomBernoulli;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9iZXJub3VsbGkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0M7O0FBRS9DLGlFQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EsQ0FBQyxFQUFFLHlEQUFhLENBQUMsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1yYW5kb21cXHNyY1xcYmVybm91bGxpLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBkZWZhdWx0U291cmNlIGZyb20gXCIuL2RlZmF1bHRTb3VyY2UuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgKGZ1bmN0aW9uIHNvdXJjZVJhbmRvbUJlcm5vdWxsaShzb3VyY2UpIHtcbiAgZnVuY3Rpb24gcmFuZG9tQmVybm91bGxpKHApIHtcbiAgICBpZiAoKHAgPSArcCkgPCAwIHx8IHAgPiAxKSB0aHJvdyBuZXcgUmFuZ2VFcnJvcihcImludmFsaWQgcFwiKTtcbiAgICByZXR1cm4gZnVuY3Rpb24oKSB7XG4gICAgICByZXR1cm4gTWF0aC5mbG9vcihzb3VyY2UoKSArIHApO1xuICAgIH07XG4gIH1cblxuICByYW5kb21CZXJub3VsbGkuc291cmNlID0gc291cmNlUmFuZG9tQmVybm91bGxpO1xuXG4gIHJldHVybiByYW5kb21CZXJub3VsbGk7XG59KShkZWZhdWx0U291cmNlKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/bernoulli.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/beta.js":
/*!********************************************!*\
  !*** ./node_modules/d3-random/src/beta.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n/* harmony import */ var _gamma_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./gamma.js */ \"(ssr)/./node_modules/d3-random/src/gamma.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomBeta(source) {\n  var G = _gamma_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].source(source);\n\n  function randomBeta(alpha, beta) {\n    var X = G(alpha),\n        Y = G(beta);\n    return function() {\n      var x = X();\n      return x === 0 ? 0 : x / (x + Y());\n    };\n  }\n\n  randomBeta.source = sourceRandomBeta;\n\n  return randomBeta;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9iZXRhLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQztBQUNoQjs7QUFFL0IsaUVBQWU7QUFDZixVQUFVLGlEQUFLOztBQUVmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQSxDQUFDLEVBQUUseURBQWEsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGQzLXJhbmRvbVxcc3JjXFxiZXRhLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBkZWZhdWx0U291cmNlIGZyb20gXCIuL2RlZmF1bHRTb3VyY2UuanNcIjtcbmltcG9ydCBnYW1tYSBmcm9tIFwiLi9nYW1tYS5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCAoZnVuY3Rpb24gc291cmNlUmFuZG9tQmV0YShzb3VyY2UpIHtcbiAgdmFyIEcgPSBnYW1tYS5zb3VyY2Uoc291cmNlKTtcblxuICBmdW5jdGlvbiByYW5kb21CZXRhKGFscGhhLCBiZXRhKSB7XG4gICAgdmFyIFggPSBHKGFscGhhKSxcbiAgICAgICAgWSA9IEcoYmV0YSk7XG4gICAgcmV0dXJuIGZ1bmN0aW9uKCkge1xuICAgICAgdmFyIHggPSBYKCk7XG4gICAgICByZXR1cm4geCA9PT0gMCA/IDAgOiB4IC8gKHggKyBZKCkpO1xuICAgIH07XG4gIH1cblxuICByYW5kb21CZXRhLnNvdXJjZSA9IHNvdXJjZVJhbmRvbUJldGE7XG5cbiAgcmV0dXJuIHJhbmRvbUJldGE7XG59KShkZWZhdWx0U291cmNlKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/beta.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/binomial.js":
/*!************************************************!*\
  !*** ./node_modules/d3-random/src/binomial.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n/* harmony import */ var _beta_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./beta.js */ \"(ssr)/./node_modules/d3-random/src/beta.js\");\n/* harmony import */ var _geometric_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geometric.js */ \"(ssr)/./node_modules/d3-random/src/geometric.js\");\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomBinomial(source) {\n  var G = _geometric_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].source(source),\n      B = _beta_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].source(source);\n\n  function randomBinomial(n, p) {\n    n = +n;\n    if ((p = +p) >= 1) return () => n;\n    if (p <= 0) return () => 0;\n    return function() {\n      var acc = 0, nn = n, pp = p;\n      while (nn * pp > 16 && nn * (1 - pp) > 16) {\n        var i = Math.floor((nn + 1) * pp),\n            y = B(i, nn - i + 1)();\n        if (y <= pp) {\n          acc += i;\n          nn -= i;\n          pp = (pp - y) / (1 - y);\n        } else {\n          nn = i - 1;\n          pp /= y;\n        }\n      }\n      var sign = pp < 0.5,\n          pFinal = sign ? pp : 1 - pp,\n          g = G(pFinal);\n      for (var s = g(), k = 0; s <= nn; ++k) s += g();\n      return acc + (sign ? k : nn - k);\n    };\n  }\n\n  randomBinomial.source = sourceRandomBinomial;\n\n  return randomBinomial;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/binomial.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/cauchy.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-random/src/cauchy.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomCauchy(source) {\n  function randomCauchy(a, b) {\n    a = a == null ? 0 : +a;\n    b = b == null ? 1 : +b;\n    return function() {\n      return a + b * Math.tan(Math.PI * source());\n    };\n  }\n\n  randomCauchy.source = sourceRandomCauchy;\n\n  return randomCauchy;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9jYXVjaHkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0M7O0FBRS9DLGlFQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQSxDQUFDLEVBQUUseURBQWEsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGQzLXJhbmRvbVxcc3JjXFxjYXVjaHkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGRlZmF1bHRTb3VyY2UgZnJvbSBcIi4vZGVmYXVsdFNvdXJjZS5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCAoZnVuY3Rpb24gc291cmNlUmFuZG9tQ2F1Y2h5KHNvdXJjZSkge1xuICBmdW5jdGlvbiByYW5kb21DYXVjaHkoYSwgYikge1xuICAgIGEgPSBhID09IG51bGwgPyAwIDogK2E7XG4gICAgYiA9IGIgPT0gbnVsbCA/IDEgOiArYjtcbiAgICByZXR1cm4gZnVuY3Rpb24oKSB7XG4gICAgICByZXR1cm4gYSArIGIgKiBNYXRoLnRhbihNYXRoLlBJICogc291cmNlKCkpO1xuICAgIH07XG4gIH1cblxuICByYW5kb21DYXVjaHkuc291cmNlID0gc291cmNlUmFuZG9tQ2F1Y2h5O1xuXG4gIHJldHVybiByYW5kb21DYXVjaHk7XG59KShkZWZhdWx0U291cmNlKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/cauchy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/defaultSource.js":
/*!*****************************************************!*\
  !*** ./node_modules/d3-random/src/defaultSource.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Math.random);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9kZWZhdWx0U291cmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxXQUFXLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtcmFuZG9tXFxzcmNcXGRlZmF1bHRTb3VyY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgTWF0aC5yYW5kb207XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/defaultSource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/exponential.js":
/*!***************************************************!*\
  !*** ./node_modules/d3-random/src/exponential.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomExponential(source) {\n  function randomExponential(lambda) {\n    return function() {\n      return -Math.log1p(-source()) / lambda;\n    };\n  }\n\n  randomExponential.source = sourceRandomExponential;\n\n  return randomExponential;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9leHBvbmVudGlhbC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErQzs7QUFFL0MsaUVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EsQ0FBQyxFQUFFLHlEQUFhLENBQUMsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1yYW5kb21cXHNyY1xcZXhwb25lbnRpYWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGRlZmF1bHRTb3VyY2UgZnJvbSBcIi4vZGVmYXVsdFNvdXJjZS5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCAoZnVuY3Rpb24gc291cmNlUmFuZG9tRXhwb25lbnRpYWwoc291cmNlKSB7XG4gIGZ1bmN0aW9uIHJhbmRvbUV4cG9uZW50aWFsKGxhbWJkYSkge1xuICAgIHJldHVybiBmdW5jdGlvbigpIHtcbiAgICAgIHJldHVybiAtTWF0aC5sb2cxcCgtc291cmNlKCkpIC8gbGFtYmRhO1xuICAgIH07XG4gIH1cblxuICByYW5kb21FeHBvbmVudGlhbC5zb3VyY2UgPSBzb3VyY2VSYW5kb21FeHBvbmVudGlhbDtcblxuICByZXR1cm4gcmFuZG9tRXhwb25lbnRpYWw7XG59KShkZWZhdWx0U291cmNlKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/exponential.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/gamma.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-random/src/gamma.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n/* harmony import */ var _normal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./normal.js */ \"(ssr)/./node_modules/d3-random/src/normal.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomGamma(source) {\n  var randomNormal = _normal_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].source(source)();\n\n  function randomGamma(k, theta) {\n    if ((k = +k) < 0) throw new RangeError(\"invalid k\");\n    // degenerate distribution if k === 0\n    if (k === 0) return () => 0;\n    theta = theta == null ? 1 : +theta;\n    // exponential distribution if k === 1\n    if (k === 1) return () => -Math.log1p(-source()) * theta;\n\n    var d = (k < 1 ? k + 1 : k) - 1 / 3,\n        c = 1 / (3 * Math.sqrt(d)),\n        multiplier = k < 1 ? () => Math.pow(source(), 1 / k) : () => 1;\n    return function() {\n      do {\n        do {\n          var x = randomNormal(),\n              v = 1 + c * x;\n        } while (v <= 0);\n        v *= v * v;\n        var u = 1 - source();\n      } while (u >= 1 - 0.0331 * x * x * x * x && Math.log(u) >= 0.5 * x * x + d * (1 - v + Math.log(v)));\n      return d * v * multiplier() * theta;\n    };\n  }\n\n  randomGamma.source = sourceRandomGamma;\n\n  return randomGamma;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9nYW1tYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0M7QUFDZDs7QUFFakMsaUVBQWU7QUFDZixxQkFBcUIsa0RBQU07O0FBRTNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQSxDQUFDLEVBQUUseURBQWEsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGQzLXJhbmRvbVxcc3JjXFxnYW1tYS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZGVmYXVsdFNvdXJjZSBmcm9tIFwiLi9kZWZhdWx0U291cmNlLmpzXCI7XG5pbXBvcnQgbm9ybWFsIGZyb20gXCIuL25vcm1hbC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCAoZnVuY3Rpb24gc291cmNlUmFuZG9tR2FtbWEoc291cmNlKSB7XG4gIHZhciByYW5kb21Ob3JtYWwgPSBub3JtYWwuc291cmNlKHNvdXJjZSkoKTtcblxuICBmdW5jdGlvbiByYW5kb21HYW1tYShrLCB0aGV0YSkge1xuICAgIGlmICgoayA9ICtrKSA8IDApIHRocm93IG5ldyBSYW5nZUVycm9yKFwiaW52YWxpZCBrXCIpO1xuICAgIC8vIGRlZ2VuZXJhdGUgZGlzdHJpYnV0aW9uIGlmIGsgPT09IDBcbiAgICBpZiAoayA9PT0gMCkgcmV0dXJuICgpID0+IDA7XG4gICAgdGhldGEgPSB0aGV0YSA9PSBudWxsID8gMSA6ICt0aGV0YTtcbiAgICAvLyBleHBvbmVudGlhbCBkaXN0cmlidXRpb24gaWYgayA9PT0gMVxuICAgIGlmIChrID09PSAxKSByZXR1cm4gKCkgPT4gLU1hdGgubG9nMXAoLXNvdXJjZSgpKSAqIHRoZXRhO1xuXG4gICAgdmFyIGQgPSAoayA8IDEgPyBrICsgMSA6IGspIC0gMSAvIDMsXG4gICAgICAgIGMgPSAxIC8gKDMgKiBNYXRoLnNxcnQoZCkpLFxuICAgICAgICBtdWx0aXBsaWVyID0gayA8IDEgPyAoKSA9PiBNYXRoLnBvdyhzb3VyY2UoKSwgMSAvIGspIDogKCkgPT4gMTtcbiAgICByZXR1cm4gZnVuY3Rpb24oKSB7XG4gICAgICBkbyB7XG4gICAgICAgIGRvIHtcbiAgICAgICAgICB2YXIgeCA9IHJhbmRvbU5vcm1hbCgpLFxuICAgICAgICAgICAgICB2ID0gMSArIGMgKiB4O1xuICAgICAgICB9IHdoaWxlICh2IDw9IDApO1xuICAgICAgICB2ICo9IHYgKiB2O1xuICAgICAgICB2YXIgdSA9IDEgLSBzb3VyY2UoKTtcbiAgICAgIH0gd2hpbGUgKHUgPj0gMSAtIDAuMDMzMSAqIHggKiB4ICogeCAqIHggJiYgTWF0aC5sb2codSkgPj0gMC41ICogeCAqIHggKyBkICogKDEgLSB2ICsgTWF0aC5sb2codikpKTtcbiAgICAgIHJldHVybiBkICogdiAqIG11bHRpcGxpZXIoKSAqIHRoZXRhO1xuICAgIH07XG4gIH1cblxuICByYW5kb21HYW1tYS5zb3VyY2UgPSBzb3VyY2VSYW5kb21HYW1tYTtcblxuICByZXR1cm4gcmFuZG9tR2FtbWE7XG59KShkZWZhdWx0U291cmNlKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/gamma.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/geometric.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-random/src/geometric.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomGeometric(source) {\n  function randomGeometric(p) {\n    if ((p = +p) < 0 || p > 1) throw new RangeError(\"invalid p\");\n    if (p === 0) return () => Infinity;\n    if (p === 1) return () => 1;\n    p = Math.log1p(-p);\n    return function() {\n      return 1 + Math.floor(Math.log1p(-source()) / p);\n    };\n  }\n\n  randomGeometric.source = sourceRandomGeometric;\n\n  return randomGeometric;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9nZW9tZXRyaWMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0M7O0FBRS9DLGlFQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EsQ0FBQyxFQUFFLHlEQUFhLENBQUMsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1yYW5kb21cXHNyY1xcZ2VvbWV0cmljLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBkZWZhdWx0U291cmNlIGZyb20gXCIuL2RlZmF1bHRTb3VyY2UuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgKGZ1bmN0aW9uIHNvdXJjZVJhbmRvbUdlb21ldHJpYyhzb3VyY2UpIHtcbiAgZnVuY3Rpb24gcmFuZG9tR2VvbWV0cmljKHApIHtcbiAgICBpZiAoKHAgPSArcCkgPCAwIHx8IHAgPiAxKSB0aHJvdyBuZXcgUmFuZ2VFcnJvcihcImludmFsaWQgcFwiKTtcbiAgICBpZiAocCA9PT0gMCkgcmV0dXJuICgpID0+IEluZmluaXR5O1xuICAgIGlmIChwID09PSAxKSByZXR1cm4gKCkgPT4gMTtcbiAgICBwID0gTWF0aC5sb2cxcCgtcCk7XG4gICAgcmV0dXJuIGZ1bmN0aW9uKCkge1xuICAgICAgcmV0dXJuIDEgKyBNYXRoLmZsb29yKE1hdGgubG9nMXAoLXNvdXJjZSgpKSAvIHApO1xuICAgIH07XG4gIH1cblxuICByYW5kb21HZW9tZXRyaWMuc291cmNlID0gc291cmNlUmFuZG9tR2VvbWV0cmljO1xuXG4gIHJldHVybiByYW5kb21HZW9tZXRyaWM7XG59KShkZWZhdWx0U291cmNlKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/geometric.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/index.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-random/src/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   randomBates: () => (/* reexport safe */ _bates_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   randomBernoulli: () => (/* reexport safe */ _bernoulli_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   randomBeta: () => (/* reexport safe */ _beta_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   randomBinomial: () => (/* reexport safe */ _binomial_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   randomCauchy: () => (/* reexport safe */ _cauchy_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"]),\n/* harmony export */   randomExponential: () => (/* reexport safe */ _exponential_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   randomGamma: () => (/* reexport safe */ _gamma_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   randomGeometric: () => (/* reexport safe */ _geometric_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   randomInt: () => (/* reexport safe */ _int_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   randomIrwinHall: () => (/* reexport safe */ _irwinHall_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   randomLcg: () => (/* reexport safe */ _lcg_js__WEBPACK_IMPORTED_MODULE_17__[\"default\"]),\n/* harmony export */   randomLogNormal: () => (/* reexport safe */ _logNormal_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   randomLogistic: () => (/* reexport safe */ _logistic_js__WEBPACK_IMPORTED_MODULE_15__[\"default\"]),\n/* harmony export */   randomNormal: () => (/* reexport safe */ _normal_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   randomPareto: () => (/* reexport safe */ _pareto_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   randomPoisson: () => (/* reexport safe */ _poisson_js__WEBPACK_IMPORTED_MODULE_16__[\"default\"]),\n/* harmony export */   randomUniform: () => (/* reexport safe */ _uniform_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   randomWeibull: () => (/* reexport safe */ _weibull_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _uniform_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./uniform.js */ \"(ssr)/./node_modules/d3-random/src/uniform.js\");\n/* harmony import */ var _int_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./int.js */ \"(ssr)/./node_modules/d3-random/src/int.js\");\n/* harmony import */ var _normal_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./normal.js */ \"(ssr)/./node_modules/d3-random/src/normal.js\");\n/* harmony import */ var _logNormal_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./logNormal.js */ \"(ssr)/./node_modules/d3-random/src/logNormal.js\");\n/* harmony import */ var _bates_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./bates.js */ \"(ssr)/./node_modules/d3-random/src/bates.js\");\n/* harmony import */ var _irwinHall_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./irwinHall.js */ \"(ssr)/./node_modules/d3-random/src/irwinHall.js\");\n/* harmony import */ var _exponential_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./exponential.js */ \"(ssr)/./node_modules/d3-random/src/exponential.js\");\n/* harmony import */ var _pareto_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./pareto.js */ \"(ssr)/./node_modules/d3-random/src/pareto.js\");\n/* harmony import */ var _bernoulli_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./bernoulli.js */ \"(ssr)/./node_modules/d3-random/src/bernoulli.js\");\n/* harmony import */ var _geometric_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./geometric.js */ \"(ssr)/./node_modules/d3-random/src/geometric.js\");\n/* harmony import */ var _binomial_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./binomial.js */ \"(ssr)/./node_modules/d3-random/src/binomial.js\");\n/* harmony import */ var _gamma_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./gamma.js */ \"(ssr)/./node_modules/d3-random/src/gamma.js\");\n/* harmony import */ var _beta_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./beta.js */ \"(ssr)/./node_modules/d3-random/src/beta.js\");\n/* harmony import */ var _weibull_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./weibull.js */ \"(ssr)/./node_modules/d3-random/src/weibull.js\");\n/* harmony import */ var _cauchy_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./cauchy.js */ \"(ssr)/./node_modules/d3-random/src/cauchy.js\");\n/* harmony import */ var _logistic_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./logistic.js */ \"(ssr)/./node_modules/d3-random/src/logistic.js\");\n/* harmony import */ var _poisson_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./poisson.js */ \"(ssr)/./node_modules/d3-random/src/poisson.js\");\n/* harmony import */ var _lcg_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./lcg.js */ \"(ssr)/./node_modules/d3-random/src/lcg.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBc0Q7QUFDUjtBQUNNO0FBQ007QUFDUjtBQUNRO0FBQ0k7QUFDVjtBQUNNO0FBQ0E7QUFDRjtBQUNOO0FBQ0Y7QUFDTTtBQUNGO0FBQ0k7QUFDRjtBQUNSIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGQzLXJhbmRvbVxcc3JjXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge2RlZmF1bHQgYXMgcmFuZG9tVW5pZm9ybX0gZnJvbSBcIi4vdW5pZm9ybS5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIHJhbmRvbUludH0gZnJvbSBcIi4vaW50LmpzXCI7XG5leHBvcnQge2RlZmF1bHQgYXMgcmFuZG9tTm9ybWFsfSBmcm9tIFwiLi9ub3JtYWwuanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyByYW5kb21Mb2dOb3JtYWx9IGZyb20gXCIuL2xvZ05vcm1hbC5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIHJhbmRvbUJhdGVzfSBmcm9tIFwiLi9iYXRlcy5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIHJhbmRvbUlyd2luSGFsbH0gZnJvbSBcIi4vaXJ3aW5IYWxsLmpzXCI7XG5leHBvcnQge2RlZmF1bHQgYXMgcmFuZG9tRXhwb25lbnRpYWx9IGZyb20gXCIuL2V4cG9uZW50aWFsLmpzXCI7XG5leHBvcnQge2RlZmF1bHQgYXMgcmFuZG9tUGFyZXRvfSBmcm9tIFwiLi9wYXJldG8uanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyByYW5kb21CZXJub3VsbGl9IGZyb20gXCIuL2Jlcm5vdWxsaS5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIHJhbmRvbUdlb21ldHJpY30gZnJvbSBcIi4vZ2VvbWV0cmljLmpzXCI7XG5leHBvcnQge2RlZmF1bHQgYXMgcmFuZG9tQmlub21pYWx9IGZyb20gXCIuL2Jpbm9taWFsLmpzXCI7XG5leHBvcnQge2RlZmF1bHQgYXMgcmFuZG9tR2FtbWF9IGZyb20gXCIuL2dhbW1hLmpzXCI7XG5leHBvcnQge2RlZmF1bHQgYXMgcmFuZG9tQmV0YX0gZnJvbSBcIi4vYmV0YS5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIHJhbmRvbVdlaWJ1bGx9IGZyb20gXCIuL3dlaWJ1bGwuanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyByYW5kb21DYXVjaHl9IGZyb20gXCIuL2NhdWNoeS5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIHJhbmRvbUxvZ2lzdGljfSBmcm9tIFwiLi9sb2dpc3RpYy5qc1wiO1xuZXhwb3J0IHtkZWZhdWx0IGFzIHJhbmRvbVBvaXNzb259IGZyb20gXCIuL3BvaXNzb24uanNcIjtcbmV4cG9ydCB7ZGVmYXVsdCBhcyByYW5kb21MY2d9IGZyb20gXCIuL2xjZy5qc1wiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/int.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-random/src/int.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomInt(source) {\n  function randomInt(min, max) {\n    if (arguments.length < 2) max = min, min = 0;\n    min = Math.floor(min);\n    max = Math.floor(max) - min;\n    return function() {\n      return Math.floor(source() * max + min);\n    };\n  }\n\n  randomInt.source = sourceRandomInt;\n\n  return randomInt;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9pbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0M7O0FBRS9DLGlFQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLENBQUMsRUFBRSx5REFBYSxDQUFDLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtcmFuZG9tXFxzcmNcXGludC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZGVmYXVsdFNvdXJjZSBmcm9tIFwiLi9kZWZhdWx0U291cmNlLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IChmdW5jdGlvbiBzb3VyY2VSYW5kb21JbnQoc291cmNlKSB7XG4gIGZ1bmN0aW9uIHJhbmRvbUludChtaW4sIG1heCkge1xuICAgIGlmIChhcmd1bWVudHMubGVuZ3RoIDwgMikgbWF4ID0gbWluLCBtaW4gPSAwO1xuICAgIG1pbiA9IE1hdGguZmxvb3IobWluKTtcbiAgICBtYXggPSBNYXRoLmZsb29yKG1heCkgLSBtaW47XG4gICAgcmV0dXJuIGZ1bmN0aW9uKCkge1xuICAgICAgcmV0dXJuIE1hdGguZmxvb3Ioc291cmNlKCkgKiBtYXggKyBtaW4pO1xuICAgIH07XG4gIH1cblxuICByYW5kb21JbnQuc291cmNlID0gc291cmNlUmFuZG9tSW50O1xuXG4gIHJldHVybiByYW5kb21JbnQ7XG59KShkZWZhdWx0U291cmNlKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/int.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/irwinHall.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-random/src/irwinHall.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomIrwinHall(source) {\n  function randomIrwinHall(n) {\n    if ((n = +n) <= 0) return () => 0;\n    return function() {\n      for (var sum = 0, i = n; i > 1; --i) sum += source();\n      return sum + i * source();\n    };\n  }\n\n  randomIrwinHall.source = sourceRandomIrwinHall;\n\n  return randomIrwinHall;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9pcndpbkhhbGwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0M7O0FBRS9DLGlFQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLE9BQU87QUFDdEM7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EsQ0FBQyxFQUFFLHlEQUFhLENBQUMsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1yYW5kb21cXHNyY1xcaXJ3aW5IYWxsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBkZWZhdWx0U291cmNlIGZyb20gXCIuL2RlZmF1bHRTb3VyY2UuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgKGZ1bmN0aW9uIHNvdXJjZVJhbmRvbUlyd2luSGFsbChzb3VyY2UpIHtcbiAgZnVuY3Rpb24gcmFuZG9tSXJ3aW5IYWxsKG4pIHtcbiAgICBpZiAoKG4gPSArbikgPD0gMCkgcmV0dXJuICgpID0+IDA7XG4gICAgcmV0dXJuIGZ1bmN0aW9uKCkge1xuICAgICAgZm9yICh2YXIgc3VtID0gMCwgaSA9IG47IGkgPiAxOyAtLWkpIHN1bSArPSBzb3VyY2UoKTtcbiAgICAgIHJldHVybiBzdW0gKyBpICogc291cmNlKCk7XG4gICAgfTtcbiAgfVxuXG4gIHJhbmRvbUlyd2luSGFsbC5zb3VyY2UgPSBzb3VyY2VSYW5kb21JcndpbkhhbGw7XG5cbiAgcmV0dXJuIHJhbmRvbUlyd2luSGFsbDtcbn0pKGRlZmF1bHRTb3VyY2UpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/irwinHall.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/lcg.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-random/src/lcg.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ lcg)\n/* harmony export */ });\n// https://en.wikipedia.org/wiki/Linear_congruential_generator#Parameters_in_common_use\nconst mul = 0x19660D;\nconst inc = 0x3C6EF35F;\nconst eps = 1 / 0x100000000;\n\nfunction lcg(seed = Math.random()) {\n  let state = (0 <= seed && seed < 1 ? seed / eps : Math.abs(seed)) | 0;\n  return () => (state = mul * state + inc | 0, eps * (state >>> 0));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9sY2cuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVlO0FBQ2Y7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGQzLXJhbmRvbVxcc3JjXFxsY2cuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gaHR0cHM6Ly9lbi53aWtpcGVkaWEub3JnL3dpa2kvTGluZWFyX2NvbmdydWVudGlhbF9nZW5lcmF0b3IjUGFyYW1ldGVyc19pbl9jb21tb25fdXNlXG5jb25zdCBtdWwgPSAweDE5NjYwRDtcbmNvbnN0IGluYyA9IDB4M0M2RUYzNUY7XG5jb25zdCBlcHMgPSAxIC8gMHgxMDAwMDAwMDA7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGxjZyhzZWVkID0gTWF0aC5yYW5kb20oKSkge1xuICBsZXQgc3RhdGUgPSAoMCA8PSBzZWVkICYmIHNlZWQgPCAxID8gc2VlZCAvIGVwcyA6IE1hdGguYWJzKHNlZWQpKSB8IDA7XG4gIHJldHVybiAoKSA9PiAoc3RhdGUgPSBtdWwgKiBzdGF0ZSArIGluYyB8IDAsIGVwcyAqIChzdGF0ZSA+Pj4gMCkpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/lcg.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/logNormal.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-random/src/logNormal.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n/* harmony import */ var _normal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./normal.js */ \"(ssr)/./node_modules/d3-random/src/normal.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomLogNormal(source) {\n  var N = _normal_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].source(source);\n\n  function randomLogNormal() {\n    var randomNormal = N.apply(this, arguments);\n    return function() {\n      return Math.exp(randomNormal());\n    };\n  }\n\n  randomLogNormal.source = sourceRandomLogNormal;\n\n  return randomLogNormal;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9sb2dOb3JtYWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStDO0FBQ2Q7O0FBRWpDLGlFQUFlO0FBQ2YsVUFBVSxrREFBTTs7QUFFaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EsQ0FBQyxFQUFFLHlEQUFhLENBQUMsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1yYW5kb21cXHNyY1xcbG9nTm9ybWFsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBkZWZhdWx0U291cmNlIGZyb20gXCIuL2RlZmF1bHRTb3VyY2UuanNcIjtcbmltcG9ydCBub3JtYWwgZnJvbSBcIi4vbm9ybWFsLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IChmdW5jdGlvbiBzb3VyY2VSYW5kb21Mb2dOb3JtYWwoc291cmNlKSB7XG4gIHZhciBOID0gbm9ybWFsLnNvdXJjZShzb3VyY2UpO1xuXG4gIGZ1bmN0aW9uIHJhbmRvbUxvZ05vcm1hbCgpIHtcbiAgICB2YXIgcmFuZG9tTm9ybWFsID0gTi5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xuICAgIHJldHVybiBmdW5jdGlvbigpIHtcbiAgICAgIHJldHVybiBNYXRoLmV4cChyYW5kb21Ob3JtYWwoKSk7XG4gICAgfTtcbiAgfVxuXG4gIHJhbmRvbUxvZ05vcm1hbC5zb3VyY2UgPSBzb3VyY2VSYW5kb21Mb2dOb3JtYWw7XG5cbiAgcmV0dXJuIHJhbmRvbUxvZ05vcm1hbDtcbn0pKGRlZmF1bHRTb3VyY2UpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/logNormal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/logistic.js":
/*!************************************************!*\
  !*** ./node_modules/d3-random/src/logistic.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomLogistic(source) {\n  function randomLogistic(a, b) {\n    a = a == null ? 0 : +a;\n    b = b == null ? 1 : +b;\n    return function() {\n      var u = source();\n      return a + b * Math.log(u / (1 - u));\n    };\n  }\n\n  randomLogistic.source = sourceRandomLogistic;\n\n  return randomLogistic;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9sb2dpc3RpYy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErQzs7QUFFL0MsaUVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EsQ0FBQyxFQUFFLHlEQUFhLENBQUMsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1yYW5kb21cXHNyY1xcbG9naXN0aWMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGRlZmF1bHRTb3VyY2UgZnJvbSBcIi4vZGVmYXVsdFNvdXJjZS5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCAoZnVuY3Rpb24gc291cmNlUmFuZG9tTG9naXN0aWMoc291cmNlKSB7XG4gIGZ1bmN0aW9uIHJhbmRvbUxvZ2lzdGljKGEsIGIpIHtcbiAgICBhID0gYSA9PSBudWxsID8gMCA6ICthO1xuICAgIGIgPSBiID09IG51bGwgPyAxIDogK2I7XG4gICAgcmV0dXJuIGZ1bmN0aW9uKCkge1xuICAgICAgdmFyIHUgPSBzb3VyY2UoKTtcbiAgICAgIHJldHVybiBhICsgYiAqIE1hdGgubG9nKHUgLyAoMSAtIHUpKTtcbiAgICB9O1xuICB9XG5cbiAgcmFuZG9tTG9naXN0aWMuc291cmNlID0gc291cmNlUmFuZG9tTG9naXN0aWM7XG5cbiAgcmV0dXJuIHJhbmRvbUxvZ2lzdGljO1xufSkoZGVmYXVsdFNvdXJjZSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/logistic.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/normal.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-random/src/normal.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomNormal(source) {\n  function randomNormal(mu, sigma) {\n    var x, r;\n    mu = mu == null ? 0 : +mu;\n    sigma = sigma == null ? 1 : +sigma;\n    return function() {\n      var y;\n\n      // If available, use the second previously-generated uniform random.\n      if (x != null) y = x, x = null;\n\n      // Otherwise, generate a new x and y.\n      else do {\n        x = source() * 2 - 1;\n        y = source() * 2 - 1;\n        r = x * x + y * y;\n      } while (!r || r > 1);\n\n      return mu + sigma * y * Math.sqrt(-2 * Math.log(r) / r);\n    };\n  }\n\n  randomNormal.source = sourceRandomNormal;\n\n  return randomNormal;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9ub3JtYWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0M7O0FBRS9DLGlFQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7O0FBRVI7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EsQ0FBQyxFQUFFLHlEQUFhLENBQUMsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzYWFuZFxcT25lRHJpdmVcXERvY3VtZW50c1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxkMy1yYW5kb21cXHNyY1xcbm9ybWFsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBkZWZhdWx0U291cmNlIGZyb20gXCIuL2RlZmF1bHRTb3VyY2UuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgKGZ1bmN0aW9uIHNvdXJjZVJhbmRvbU5vcm1hbChzb3VyY2UpIHtcbiAgZnVuY3Rpb24gcmFuZG9tTm9ybWFsKG11LCBzaWdtYSkge1xuICAgIHZhciB4LCByO1xuICAgIG11ID0gbXUgPT0gbnVsbCA/IDAgOiArbXU7XG4gICAgc2lnbWEgPSBzaWdtYSA9PSBudWxsID8gMSA6ICtzaWdtYTtcbiAgICByZXR1cm4gZnVuY3Rpb24oKSB7XG4gICAgICB2YXIgeTtcblxuICAgICAgLy8gSWYgYXZhaWxhYmxlLCB1c2UgdGhlIHNlY29uZCBwcmV2aW91c2x5LWdlbmVyYXRlZCB1bmlmb3JtIHJhbmRvbS5cbiAgICAgIGlmICh4ICE9IG51bGwpIHkgPSB4LCB4ID0gbnVsbDtcblxuICAgICAgLy8gT3RoZXJ3aXNlLCBnZW5lcmF0ZSBhIG5ldyB4IGFuZCB5LlxuICAgICAgZWxzZSBkbyB7XG4gICAgICAgIHggPSBzb3VyY2UoKSAqIDIgLSAxO1xuICAgICAgICB5ID0gc291cmNlKCkgKiAyIC0gMTtcbiAgICAgICAgciA9IHggKiB4ICsgeSAqIHk7XG4gICAgICB9IHdoaWxlICghciB8fCByID4gMSk7XG5cbiAgICAgIHJldHVybiBtdSArIHNpZ21hICogeSAqIE1hdGguc3FydCgtMiAqIE1hdGgubG9nKHIpIC8gcik7XG4gICAgfTtcbiAgfVxuXG4gIHJhbmRvbU5vcm1hbC5zb3VyY2UgPSBzb3VyY2VSYW5kb21Ob3JtYWw7XG5cbiAgcmV0dXJuIHJhbmRvbU5vcm1hbDtcbn0pKGRlZmF1bHRTb3VyY2UpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/normal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/pareto.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-random/src/pareto.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomPareto(source) {\n  function randomPareto(alpha) {\n    if ((alpha = +alpha) < 0) throw new RangeError(\"invalid alpha\");\n    alpha = 1 / -alpha;\n    return function() {\n      return Math.pow(1 - source(), alpha);\n    };\n  }\n\n  randomPareto.source = sourceRandomPareto;\n\n  return randomPareto;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9wYXJldG8uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0M7O0FBRS9DLGlFQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQSxDQUFDLEVBQUUseURBQWEsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNhYW5kXFxPbmVEcml2ZVxcRG9jdW1lbnRzXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGQzLXJhbmRvbVxcc3JjXFxwYXJldG8uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGRlZmF1bHRTb3VyY2UgZnJvbSBcIi4vZGVmYXVsdFNvdXJjZS5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCAoZnVuY3Rpb24gc291cmNlUmFuZG9tUGFyZXRvKHNvdXJjZSkge1xuICBmdW5jdGlvbiByYW5kb21QYXJldG8oYWxwaGEpIHtcbiAgICBpZiAoKGFscGhhID0gK2FscGhhKSA8IDApIHRocm93IG5ldyBSYW5nZUVycm9yKFwiaW52YWxpZCBhbHBoYVwiKTtcbiAgICBhbHBoYSA9IDEgLyAtYWxwaGE7XG4gICAgcmV0dXJuIGZ1bmN0aW9uKCkge1xuICAgICAgcmV0dXJuIE1hdGgucG93KDEgLSBzb3VyY2UoKSwgYWxwaGEpO1xuICAgIH07XG4gIH1cblxuICByYW5kb21QYXJldG8uc291cmNlID0gc291cmNlUmFuZG9tUGFyZXRvO1xuXG4gIHJldHVybiByYW5kb21QYXJldG87XG59KShkZWZhdWx0U291cmNlKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/pareto.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/poisson.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-random/src/poisson.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n/* harmony import */ var _binomial_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./binomial.js */ \"(ssr)/./node_modules/d3-random/src/binomial.js\");\n/* harmony import */ var _gamma_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./gamma.js */ \"(ssr)/./node_modules/d3-random/src/gamma.js\");\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomPoisson(source) {\n  var G = _gamma_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].source(source),\n      B = _binomial_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].source(source);\n\n  function randomPoisson(lambda) {\n    return function() {\n      var acc = 0, l = lambda;\n      while (l > 16) {\n        var n = Math.floor(0.875 * l),\n            t = G(n)();\n        if (t > l) return acc + B(n - 1, l / t)();\n        acc += n;\n        l -= t;\n      }\n      for (var s = -Math.log1p(-source()), k = 0; s <= l; ++k) s -= Math.log1p(-source());\n      return acc + k;\n    };\n  }\n\n  randomPoisson.source = sourceRandomPoisson;\n\n  return randomPoisson;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy9wb2lzc29uLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBK0M7QUFDVjtBQUNOOztBQUUvQixpRUFBZTtBQUNmLFVBQVUsaURBQUs7QUFDZixVQUFVLG9EQUFROztBQUVsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCxRQUFRO0FBQzFEO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLENBQUMsRUFBRSx5REFBYSxDQUFDLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtcmFuZG9tXFxzcmNcXHBvaXNzb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGRlZmF1bHRTb3VyY2UgZnJvbSBcIi4vZGVmYXVsdFNvdXJjZS5qc1wiO1xuaW1wb3J0IGJpbm9taWFsIGZyb20gXCIuL2Jpbm9taWFsLmpzXCI7XG5pbXBvcnQgZ2FtbWEgZnJvbSBcIi4vZ2FtbWEuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgKGZ1bmN0aW9uIHNvdXJjZVJhbmRvbVBvaXNzb24oc291cmNlKSB7XG4gIHZhciBHID0gZ2FtbWEuc291cmNlKHNvdXJjZSksXG4gICAgICBCID0gYmlub21pYWwuc291cmNlKHNvdXJjZSk7XG5cbiAgZnVuY3Rpb24gcmFuZG9tUG9pc3NvbihsYW1iZGEpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24oKSB7XG4gICAgICB2YXIgYWNjID0gMCwgbCA9IGxhbWJkYTtcbiAgICAgIHdoaWxlIChsID4gMTYpIHtcbiAgICAgICAgdmFyIG4gPSBNYXRoLmZsb29yKDAuODc1ICogbCksXG4gICAgICAgICAgICB0ID0gRyhuKSgpO1xuICAgICAgICBpZiAodCA+IGwpIHJldHVybiBhY2MgKyBCKG4gLSAxLCBsIC8gdCkoKTtcbiAgICAgICAgYWNjICs9IG47XG4gICAgICAgIGwgLT0gdDtcbiAgICAgIH1cbiAgICAgIGZvciAodmFyIHMgPSAtTWF0aC5sb2cxcCgtc291cmNlKCkpLCBrID0gMDsgcyA8PSBsOyArK2spIHMgLT0gTWF0aC5sb2cxcCgtc291cmNlKCkpO1xuICAgICAgcmV0dXJuIGFjYyArIGs7XG4gICAgfTtcbiAgfVxuXG4gIHJhbmRvbVBvaXNzb24uc291cmNlID0gc291cmNlUmFuZG9tUG9pc3NvbjtcblxuICByZXR1cm4gcmFuZG9tUG9pc3Nvbjtcbn0pKGRlZmF1bHRTb3VyY2UpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/poisson.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/uniform.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-random/src/uniform.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomUniform(source) {\n  function randomUniform(min, max) {\n    min = min == null ? 0 : +min;\n    max = max == null ? 1 : +max;\n    if (arguments.length === 1) max = min, min = 0;\n    else max -= min;\n    return function() {\n      return source() * max + min;\n    };\n  }\n\n  randomUniform.source = sourceRandomUniform;\n\n  return randomUniform;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy91bmlmb3JtLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStDOztBQUUvQyxpRUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLENBQUMsRUFBRSx5REFBYSxDQUFDLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtcmFuZG9tXFxzcmNcXHVuaWZvcm0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGRlZmF1bHRTb3VyY2UgZnJvbSBcIi4vZGVmYXVsdFNvdXJjZS5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCAoZnVuY3Rpb24gc291cmNlUmFuZG9tVW5pZm9ybShzb3VyY2UpIHtcbiAgZnVuY3Rpb24gcmFuZG9tVW5pZm9ybShtaW4sIG1heCkge1xuICAgIG1pbiA9IG1pbiA9PSBudWxsID8gMCA6ICttaW47XG4gICAgbWF4ID0gbWF4ID09IG51bGwgPyAxIDogK21heDtcbiAgICBpZiAoYXJndW1lbnRzLmxlbmd0aCA9PT0gMSkgbWF4ID0gbWluLCBtaW4gPSAwO1xuICAgIGVsc2UgbWF4IC09IG1pbjtcbiAgICByZXR1cm4gZnVuY3Rpb24oKSB7XG4gICAgICByZXR1cm4gc291cmNlKCkgKiBtYXggKyBtaW47XG4gICAgfTtcbiAgfVxuXG4gIHJhbmRvbVVuaWZvcm0uc291cmNlID0gc291cmNlUmFuZG9tVW5pZm9ybTtcblxuICByZXR1cm4gcmFuZG9tVW5pZm9ybTtcbn0pKGRlZmF1bHRTb3VyY2UpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/uniform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-random/src/weibull.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-random/src/weibull.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _defaultSource_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultSource.js */ \"(ssr)/./node_modules/d3-random/src/defaultSource.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((function sourceRandomWeibull(source) {\n  function randomWeibull(k, a, b) {\n    var outerFunc;\n    if ((k = +k) === 0) {\n      outerFunc = x => -Math.log(x);\n    } else {\n      k = 1 / k;\n      outerFunc = x => Math.pow(x, k);\n    }\n    a = a == null ? 0 : +a;\n    b = b == null ? 1 : +b;\n    return function() {\n      return a + b * outerFunc(-Math.log1p(-source()));\n    };\n  }\n\n  randomWeibull.source = sourceRandomWeibull;\n\n  return randomWeibull;\n})(_defaultSource_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtcmFuZG9tL3NyYy93ZWlidWxsLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStDOztBQUUvQyxpRUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLENBQUMsRUFBRSx5REFBYSxDQUFDLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2FhbmRcXE9uZURyaXZlXFxEb2N1bWVudHNcXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZDMtcmFuZG9tXFxzcmNcXHdlaWJ1bGwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGRlZmF1bHRTb3VyY2UgZnJvbSBcIi4vZGVmYXVsdFNvdXJjZS5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCAoZnVuY3Rpb24gc291cmNlUmFuZG9tV2VpYnVsbChzb3VyY2UpIHtcbiAgZnVuY3Rpb24gcmFuZG9tV2VpYnVsbChrLCBhLCBiKSB7XG4gICAgdmFyIG91dGVyRnVuYztcbiAgICBpZiAoKGsgPSAraykgPT09IDApIHtcbiAgICAgIG91dGVyRnVuYyA9IHggPT4gLU1hdGgubG9nKHgpO1xuICAgIH0gZWxzZSB7XG4gICAgICBrID0gMSAvIGs7XG4gICAgICBvdXRlckZ1bmMgPSB4ID0+IE1hdGgucG93KHgsIGspO1xuICAgIH1cbiAgICBhID0gYSA9PSBudWxsID8gMCA6ICthO1xuICAgIGIgPSBiID09IG51bGwgPyAxIDogK2I7XG4gICAgcmV0dXJuIGZ1bmN0aW9uKCkge1xuICAgICAgcmV0dXJuIGEgKyBiICogb3V0ZXJGdW5jKC1NYXRoLmxvZzFwKC1zb3VyY2UoKSkpO1xuICAgIH07XG4gIH1cblxuICByYW5kb21XZWlidWxsLnNvdXJjZSA9IHNvdXJjZVJhbmRvbVdlaWJ1bGw7XG5cbiAgcmV0dXJuIHJhbmRvbVdlaWJ1bGw7XG59KShkZWZhdWx0U291cmNlKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-random/src/weibull.js\n");

/***/ })

};
;