"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Download, ZoomIn, ZoomOut, RotateCcw, Maximize, MessageSquare } from "lucide-react"
import { motion } from "framer-motion"
import { useTheme } from "@/components/theme-provider"
import { ScrollArea } from "@/components/ui/scroll-area"
import { flowchartApi } from "@/lib/api"
import mermaid from 'mermaid'

interface FlowchartInterfaceProps {
  documentId?: number
}

export function FlowchartInterface({ documentId }: FlowchartInterfaceProps) {
  const [zoom, setZoom] = useState(1)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [flowchartData, setFlowchartData] = useState("")
  const flowchartRef = useRef<HTMLDivElement>(null)
  const fullscreenFlowchartRef = useRef<HTMLDivElement>(null)
  const { theme } = useTheme()
  const flowchartContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Initialize mermaid
    mermaid.initialize({
      startOnLoad: false, // We'll render manually
      theme: theme === "dark" ? "dark" : "default",
      themeVariables:
        theme === "dark"
          ? {
              primaryColor: "#6366f1",
              primaryTextColor: "#ffffff",
              primaryBorderColor: "#4f46e5",
              lineColor: "#8b5cf6",
              secondaryColor: "#7c3aed",
              tertiaryColor: "#a855f7",
              background: "#1e1b4b",
              mainBkg: "#312e81",
              secondBkg: "#3730a3",
              tertiaryBkg: "#4338ca",
            }
          : {
              primaryColor: "#818cf8",
              primaryTextColor: "#ffffff",
              primaryBorderColor: "#6366f1",
              lineColor: "#6366f1",
              secondaryColor: "#c7d2fe",
              tertiaryColor: "#e0e7ff",
              background: "#ffffff",
              mainBkg: "#f3f4f6",
              secondBkg: "#e5e7eb",
              tertiaryBkg: "#f9fafb",
            },
      securityLevel: 'loose', // Allow more flexibility in rendering
      fontFamily: 'inherit'
    })

    loadFlowchart()
  }, [theme, documentId])

  const loadFlowchart = async () => {
    if (!documentId) {
      console.warn('No document ID provided for flowchart')
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      // First, try to get existing flowchart
      try {
        const existingFlowchart = await flowchartApi.getFlowchart(documentId)
        if (existingFlowchart && existingFlowchart.mermaid_code) {
          setFlowchartData(existingFlowchart.mermaid_code)
          await renderFlowchart(existingFlowchart.mermaid_code)
          return
        }
      } catch (existingError) {
        console.log('No existing flowchart found, will generate new one')
      }

      // If no existing flowchart, generate new one
      console.log('Generating new flowchart for document:', documentId)
      const response = await flowchartApi.generateFlowchart(documentId)
      const mermaidCode = response.flowchart

      if (mermaidCode) {
        setFlowchartData(mermaidCode)
        await renderFlowchart(mermaidCode)
      } else {
        throw new Error('No flowchart data received')
      }
    } catch (error) {
      console.error("Failed to load flowchart:", error)
      setError("Failed to load flowchart visualization. Please try again later.")
      setFlowchartData("")
    } finally {
      setIsLoading(false)
    }
  }

  const renderFlowchart = async (mermaidCode: string) => {
    if (flowchartRef.current && mermaidCode) {
      try {
        // Clear the container first
        flowchartRef.current.innerHTML = ''

        // Validate that the mermaid code is not empty
        if (!mermaidCode.trim()) {
          throw new Error('Empty mermaid code')
        }

        // Create a unique ID for this diagram
        const diagramId = `mermaid-${Date.now()}`

        // Use mermaid.render to generate SVG
        const { svg } = await mermaid.render(diagramId, mermaidCode)

        // Insert the rendered SVG
        flowchartRef.current.innerHTML = svg

        // Clear any previous errors
        setError(null)
      } catch (renderError) {
        console.error('Error rendering flowchart:', renderError)

        // Provide more specific error messages
        let errorMessage = "Error rendering flowchart."
        if (renderError instanceof Error) {
          if (renderError.message.includes('Parse error') || renderError.message.includes('syntax')) {
            errorMessage = "Invalid flowchart syntax. The generated code may contain errors."
          } else if (renderError.message.includes('Empty')) {
            errorMessage = "No flowchart content was generated."
          } else {
            errorMessage = `Rendering error: ${renderError.message}`
          }
        }

        setError(errorMessage)

        // Show the raw mermaid code in case of error for debugging
        if (flowchartRef.current) {
          flowchartRef.current.innerHTML = `
            <div class="p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
              <h4 class="text-red-400 font-medium mb-2">Flowchart Rendering Error</h4>
              <p class="text-red-300 text-sm mb-3">${errorMessage}</p>
              <details class="text-xs">
                <summary class="text-red-400 cursor-pointer">Show raw code</summary>
                <pre class="mt-2 p-2 bg-black/30 rounded text-gray-300 overflow-auto">${mermaidCode}</pre>
              </details>
            </div>
          `
        }
      }
    }
  }

  const handleZoomIn = () => {
    setZoom((prev) => Math.min(prev + 0.2, 3))
  }

  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev - 0.2, 0.5))
  }

  const handleReset = () => {
    setZoom(1)
  }

  const handleDownload = () => {
    // In a real implementation, this would export the flowchart as SVG/PNG
    console.log("Download flowchart")
  }

  const toggleFullscreen = async () => {
    const newFullscreenState = !isFullscreen
    setIsFullscreen(newFullscreenState)

    // If opening fullscreen and we have flowchart data, render it in the fullscreen container
    if (newFullscreenState && flowchartData && fullscreenFlowchartRef.current) {
      setTimeout(async () => {
        try {
          const diagramId = `mermaid-fullscreen-${Date.now()}`
          const { svg } = await mermaid.render(diagramId, flowchartData)
          if (fullscreenFlowchartRef.current) {
            fullscreenFlowchartRef.current.innerHTML = svg
          }
        } catch (error) {
          console.error('Error rendering fullscreen flowchart:', error)
        }
      }, 100) // Small delay to ensure the modal is rendered
    }
  }

  return (
    <div className="flex flex-col h-full bg-black" ref={flowchartContainerRef}>
      <div className="p-4 border-b border-neutral-800">
        <h2 className="text-xl font-medium text-center">Flowchart</h2>
        <p className="text-center text-sm text-muted-foreground mt-1">Machine Learning Process Visualization</p>
      </div>

      <div className="flex items-center justify-between px-4 py-2 border-b border-neutral-800">
        <div>
          <span className="text-sm text-muted-foreground">Zoom: {Math.round(zoom * 100)}%</span>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleZoomOut} size="sm" variant="outline" className="h-8 w-8 p-0" disabled={zoom <= 0.5}>
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button onClick={handleReset} size="sm" variant="outline" className="h-8 w-8 p-0">
            <RotateCcw className="h-4 w-4" />
          </Button>
          <Button onClick={handleZoomIn} size="sm" variant="outline" className="h-8 w-8 p-0" disabled={zoom >= 3}>
            <ZoomIn className="h-4 w-4" />
          </Button>
          <Button onClick={toggleFullscreen} size="sm" variant="outline" className="h-8 w-8 p-0">
            <Maximize className="h-4 w-4" />
          </Button>
          <Button onClick={handleDownload} size="sm" className="bg-purple-600 hover:bg-purple-700">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1">
        {isLoading ? (
          <div className="h-full flex items-center justify-center">
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
              <p className="text-muted-foreground">
                {!documentId
                  ? "No document selected"
                  : "Checking for existing flowchart or generating new one..."
                }
              </p>
            </div>
          </div>
        ) : error || !documentId ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center max-w-md p-6">
              <MessageSquare className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-red-500 mb-2">
                {!documentId ? "No Document Selected" : "Error Loading Flowchart"}
              </h3>
              <p className="text-muted-foreground">
                {!documentId
                  ? "Please select a document to view its flowchart."
                  : error
                }
              </p>
              {documentId && error && (
                <button
                  onClick={loadFlowchart}
                  className="mt-3 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-md transition-colors"
                >
                  Try Again
                </button>
              )}
            </div>
          </div>
        ) : !flowchartData ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center max-w-md p-6">
              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-muted-foreground mb-2">No Flowchart Available</h3>
              <p className="text-muted-foreground text-sm">
                {!documentId
                  ? "Please select a document to view its flowchart."
                  : "Unable to load or generate flowchart for this document."
                }
              </p>
            </div>
          </div>
        ) : (
          <div className="p-6">
            <Card className="bg-neutral-800 border-neutral-700 p-6 overflow-hidden">
              <div className="overflow-auto">
                <motion.div
                  ref={flowchartRef}
                  className="flex items-center justify-center min-h-[500px]"
                  style={{
                    transform: `scale(${zoom})`,
                    transformOrigin: "center top",
                  }}
                  transition={{ duration: 0.3 }}
                >
                  {/* Mermaid flowchart will be rendered here */}
                </motion.div>
              </div>
            </Card>
          </div>
        )}
      </ScrollArea>

      {/* Fullscreen Modal */}
      {isFullscreen && (
        <div className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-6" onClick={toggleFullscreen}>
          <div
            className="w-full h-full max-w-6xl max-h-[90vh] bg-neutral-800 border border-neutral-700 rounded-lg overflow-auto p-6"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-medium">Machine Learning Process Flowchart</h3>
            </div>
            <div className="overflow-auto">
              <motion.div
                ref={fullscreenFlowchartRef}
                className="flex items-center justify-center min-h-[500px]"
                style={{
                  transform: `scale(${zoom})`,
                  transformOrigin: "center top",
                }}
                transition={{ duration: 0.3 }}
              >
                {/* Mermaid flowchart will be rendered here */}
              </motion.div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 